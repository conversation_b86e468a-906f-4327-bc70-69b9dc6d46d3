// Direct database testing script
// Run with: node test-database.js

const path = require('path')

// Import database functions
async function testDatabase() {
  console.log('🔍 Testing Database Operations Directly...')
  console.log('=' .repeat(50))
  
  try {
    // Import the database module
    const db = require('./lib/database')
    
    console.log('\n1. Testing database initialization...')
    const database = await db.initDatabase()
    console.log('✅ Database initialized successfully')
    
    console.log('\n2. Testing user creation...')
    try {
      const result = await db.createUser(
        'test-user',
        '<EMAIL>',
        '+1234567890',
        'Test user for database testing',
        'I know someone who went to school with <PERSON><PERSON> Musk',
        'https://example.com/selfie.jpg'
      )
      console.log('✅ User created successfully, ID:', result.lastID)
    } catch (error) {
      console.log('❌ User creation failed:', error.message)
    }
    
    console.log('\n3. Testing user retrieval...')
    try {
      const user = await db.getUserByName('admin')
      if (user) {
        console.log('✅ Admin user found:', user.name)
      } else {
        console.log('❌ Admin user not found')
      }
    } catch (error) {
      console.log('❌ User retrieval failed:', error.message)
    }
    
    console.log('\n4. Testing user with role retrieval...')
    try {
      const userWithRole = await db.getUserWithRole('admin')
      if (userWithRole) {
        console.log('✅ Admin user with role found:', userWithRole.name, 'Role:', userWithRole.role)
      } else {
        console.log('❌ Admin user with role not found')
      }
    } catch (error) {
      console.log('❌ User with role retrieval failed:', error.message)
    }
    
    console.log('\n5. Testing all users retrieval...')
    try {
      const users = await db.getAllUsers()
      console.log('✅ Retrieved', users.length, 'users')
    } catch (error) {
      console.log('❌ All users retrieval failed:', error.message)
    }
    
    console.log('\n6. Testing all users with roles retrieval...')
    try {
      const usersWithRoles = await db.getAllUsersWithRoles()
      console.log('✅ Retrieved', usersWithRoles.length, 'users with roles')
      usersWithRoles.forEach(user => {
        console.log(`   - ${user.name}: ${user.role || 'no role'}`)
      })
    } catch (error) {
      console.log('❌ All users with roles retrieval failed:', error.message)
    }
    
    console.log('\n7. Testing role assignment...')
    try {
      // Find a non-admin user to assign role to
      const users = await db.getAllUsers()
      const nonAdminUser = users.find(u => u.name !== 'admin')
      
      if (nonAdminUser) {
        await db.assignRole(nonAdminUser.id, 'detective')
        console.log('✅ Role assigned successfully to', nonAdminUser.name)
        
        // Verify role assignment
        const role = await db.getUserRole(nonAdminUser.id)
        console.log('✅ Role verified:', role)
      } else {
        console.log('⚠️  No non-admin users found for role assignment test')
      }
    } catch (error) {
      console.log('❌ Role assignment failed:', error.message)
    }
    
    console.log('\n8. Testing role statistics...')
    try {
      const stats = await db.getRoleStats()
      console.log('✅ Role statistics:', stats)
    } catch (error) {
      console.log('❌ Role statistics failed:', error.message)
    }
    
    console.log('\n9. Testing role assignment to non-admins...')
    try {
      const result = await db.assignRolesToNonAdmins()
      console.log('✅ Role assignment result:', result.message)
    } catch (error) {
      console.log('❌ Role assignment to non-admins failed:', error.message)
    }
    
    console.log('\n10. Cleaning up test data...')
    try {
      // Remove test user if it exists
      const testUser = await db.getUserByName('test-user')
      if (testUser) {
        // Note: We don't have a deleteUser function, so we'll leave it
        console.log('⚠️  Test user left in database (no delete function available)')
      }
    } catch (error) {
      console.log('⚠️  Cleanup note:', error.message)
    }
    
    console.log('\n✅ Database testing completed!')
    
    // Close database connection
    db.closeDatabase()
    
  } catch (error) {
    console.error('❌ Database testing failed:', error)
  }
}

async function testNotionIntegration() {
  console.log('\n🔗 Testing Notion Integration...')
  console.log('=' .repeat(50))
  
  try {
    const notion = require('./lib/notion')
    
    console.log('\n1. Testing Notion database fetch...')
    const people = await notion.fetchNotionDatabase()
    
    console.log('✅ Notion fetch successful')
    console.log(`   Retrieved ${people.length} people from Notion`)
    
    if (people.length > 0) {
      console.log('\n   Sample person data:')
      const sample = people[0]
      console.log(`   - Name: ${sample.name}`)
      console.log(`   - Email: ${sample.email}`)
      console.log(`   - Phone: ${sample.phone}`)
      console.log(`   - About: ${sample.about?.substring(0, 50)}...`)
      console.log(`   - Selfie URLs: ${sample.selfie?.length || 0}`)
    }
    
    return people
    
  } catch (error) {
    console.log('❌ Notion integration failed:', error.message)
    console.log('   Check your NOTION_API_KEY and NOTION_DB_ID in .env.local')
    return []
  }
}

async function runDatabaseTests() {
  console.log('🚀 Starting Database Tests...')
  
  await testDatabase()
  await testNotionIntegration()
  
  console.log('\n🎯 Database testing completed!')
}

// Only run if this file is executed directly
if (require.main === module) {
  runDatabaseTests().catch(console.error)
}

module.exports = { testDatabase, testNotionIntegration, runDatabaseTests }
