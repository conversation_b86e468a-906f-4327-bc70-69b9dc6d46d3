import sqlite3 from 'sqlite3'
import bcrypt from 'bcryptjs'
import path from 'path'

const dbPath = path.join(process.cwd(), 'poy.db')
let db: sqlite3.Database

// Generate random password
function generateRandomPassword(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

function migrateUsersTable(database: sqlite3.Database): void {
  database.serialize(() => {
    // Create new table with correct constraints
    database.run(`
      CREATE TABLE users_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        email TEXT UNIQUE,
        phone TEXT,
        about TEXT,
        connection TEXT,
        selfie TEXT,
        password TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Copy data from old table, handling potential duplicates
    database.run(`
      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)
      SELECT id, name, email, phone, about, selfie, password, created_at FROM users
    `)

    // Drop old table
    database.run('DROP TABLE users')

    // Rename new table
    database.run('ALTER TABLE users_new RENAME TO users')

    console.log('✅ Users table migration completed')
  })
}

function addConnectionColumn(database: sqlite3.Database): void {
  // Add connection column if it doesn't exist
  database.run(`ALTER TABLE users ADD COLUMN connection TEXT`, (err: Error | null) => {
    if (err && !err.message.includes('duplicate column name')) {
      console.error('Error adding connection column:', err)
    } else if (!err) {
      console.log('✅ Connection column added to users table')
    }
  })
}

export function initDatabase(): Promise<sqlite3.Database> {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    db = new sqlite3.Database(dbPath, (err: Error | null) => {
      if (err) {
        reject(err)
        return
      }

      // Create tables
      db.serialize(() => {
        // Check if we need to migrate the users table
        db.get("SELECT sql FROM sqlite_master WHERE type='table' AND name='users'", (err: Error | null, row: any) => {
          if (err) {
            console.error('Error checking table schema:', err)
            return
          }

          // If table exists but doesn't have UNIQUE constraint on name, migrate it
          if (row && row.sql && !row.sql.includes('name TEXT NOT NULL UNIQUE')) {
            console.log('Migrating users table to add UNIQUE constraint on name...')
            migrateUsersTable(db)
          } else {
            // Create users table with proper constraints
            db.run(`
              CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                email TEXT UNIQUE,
                phone TEXT,
                about TEXT,
                connection TEXT,
                selfie TEXT,
                password TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
              )
            `)

            // Add connection column to existing tables if needed
            addConnectionColumn(db)
          }
        })

        // Create roles table
        db.run(`
          CREATE TABLE IF NOT EXISTS roles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),
            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
          )
        `)

        // Create notes table
        db.run(`
          CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            content TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
          )
        `)

        // Create user_passwords table to store plain passwords for admin viewing
        db.run(`
          CREATE TABLE IF NOT EXISTS user_passwords (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL UNIQUE,
            plain_password TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
          )
        `)

        // Create admin user if it doesn't exist and ensure admin role
        db.get('SELECT id FROM users WHERE name = ?', ['admin'], (err: Error | null, row: any) => {
          if (err) {
            console.error('Error checking admin user:', err)
            return
          }

          if (!row) {
            // Create admin user
            const hashedPassword = bcrypt.hashSync('zxcvbnm', 10)
            db.run('INSERT INTO users (name, password, connection) VALUES (?, ?, ?)', ['admin', hashedPassword, ''], function(this: sqlite3.RunResult, err: Error | null) {
              if (err) {
                console.error('Error creating admin user:', err)
                return
              }
              // Assign admin role
              db.run('INSERT INTO roles (user_id, role) VALUES (?, ?)', [this.lastID, 'admin'])
              console.log('Admin user created with password: zxcvbnm')
            })
          } else {
            // Admin user exists, ensure they have admin role
            db.get('SELECT id FROM roles WHERE user_id = ? AND role = ?', [row.id, 'admin'], (err: Error | null, roleRow: any) => {
              if (err) {
                console.error('Error checking admin role:', err)
                return
              }

              if (!roleRow) {
                // Remove any existing role and assign admin role
                db.run('DELETE FROM roles WHERE user_id = ?', [row.id], (err: Error | null) => {
                  if (err) {
                    console.error('Error removing existing admin roles:', err)
                    return
                  }
                  db.run('INSERT INTO roles (user_id, role) VALUES (?, ?)', [row.id, 'admin'], (err: Error | null) => {
                    if (err) {
                      console.error('Error assigning admin role:', err)
                    } else {
                      console.log('Admin role assigned to existing admin user')
                    }
                  })
                })
              }
            })
          }
        })

        resolve(db)
      })
    })
  })
}

export function getDatabase(): Promise<sqlite3.Database> {
  if (db) {
    return Promise.resolve(db)
  }
  return initDatabase()
}

export function closeDatabase() {
  if (db) {
    db.close()
  }
}

// User functions
export function createUser(name: string, email: string, phone: string, about: string, connection: string, selfie: string): Promise<{ lastID: number, password: string }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      const plainPassword = generateRandomPassword(8)
      const hashedPassword = bcrypt.hashSync(plainPassword, 10)

      db.run('INSERT INTO users (name, email, phone, about, connection, selfie, password) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [name, email, phone, about, connection, selfie, hashedPassword],
        function(this: sqlite3.RunResult, err: Error | null) {
          if (err) {
            reject(err)
            return
          }

          const userId = this.lastID

          // Store plain password for admin viewing
          db.run('INSERT INTO user_passwords (user_id, plain_password) VALUES (?, ?)',
            [userId, plainPassword],
            (err: Error | null) => {
              if (err) {
                console.error('Error storing plain password:', err)
                // Don't reject here, user creation was successful
              }

              // Assign normal role to new user
              db.run('INSERT INTO roles (user_id, role) VALUES (?, ?)',
                [userId, 'normal'],
                (err: Error | null) => {
                  if (err) {
                    console.error('Error assigning normal role to new user:', err)
                    // Don't reject here, user creation was successful
                  }
                  resolve({ lastID: userId, password: plainPassword })
                }
              )
            }
          )
        }
      )
    }).catch(reject)
  })
}

export function getUserByName(name: string): Promise<any> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get('SELECT * FROM users WHERE name = ?', [name], (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(row)
      })
    }).catch(reject)
  })
}

export function getUserByEmail(email: string): Promise<any> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get('SELECT * FROM users WHERE email = ?', [email], (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(row)
      })
    }).catch(reject)
  })
}

export function userExists(name: string, email: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get('SELECT id FROM users WHERE name = ? OR email = ?', [name, email], (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(!!row)
      })
    }).catch(reject)
  })
}

export function getAllUsers(): Promise<any[]> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.all('SELECT * FROM users ORDER BY name', (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }
        resolve(rows)
      })
    }).catch(reject)
  })
}

export function getUserWithRole(name: string): Promise<any> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get(`
        SELECT u.*, r.role 
        FROM users u 
        LEFT JOIN roles r ON u.id = r.user_id 
        WHERE u.name = ?
      `, [name], (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(row)
      })
    }).catch(reject)
  })
}

export function getAllUsersWithRoles(): Promise<any[]> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.all(`
        SELECT u.*, r.role 
        FROM users u 
        LEFT JOIN roles r ON u.id = r.user_id 
        ORDER BY u.name
      `, (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }
        resolve(rows)
      })
    }).catch(reject)
  })
}

// Role functions
export function assignRole(userId: number, role: 'detective' | 'liar' | 'normal'): Promise<void> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.serialize(() => {
        // Remove existing role
        db.run('DELETE FROM roles WHERE user_id = ?', [userId], (err: Error | null) => {
          if (err) {
            reject(err)
            return
          }
          
          // Assign new role
          db.run('INSERT INTO roles (user_id, role) VALUES (?, ?)', [userId, role], (err: Error | null) => {
            if (err) {
              reject(err)
              return
            }
            resolve()
          })
        })
      })
    }).catch(reject)
  })
}

export function getUserRole(userId: number): Promise<string | null> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get('SELECT role FROM roles WHERE user_id = ?', [userId], (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(row ? row.role : null)
      })
    }).catch(reject)
  })
}

export function clearNonAdminRoles(): Promise<void> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.run('DELETE FROM roles WHERE role != "admin"', (err: Error | null) => {
        if (err) {
          reject(err)
          return
        }
        resolve()
      })
    }).catch(reject)
  })
}

export function getRoleStats(): Promise<{ [key: string]: number }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.all(`
        SELECT role, COUNT(*) as count 
        FROM roles 
        GROUP BY role
      `, (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }
        
        const result = { detective: 0, liar: 0, normal: 0, admin: 0 }
        rows.forEach((row: any) => {
          result[row.role as keyof typeof result] = row.count
        })
        
        resolve(result)
      })
    }).catch(reject)
  })
}

export function getNonAdminUserCount(): Promise<number> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.get(`
        SELECT COUNT(*) as count FROM users u
        LEFT JOIN roles r ON u.id = r.user_id
        WHERE r.role IS NULL OR r.role != 'admin'
      `, (err: Error | null, row: any) => {
        if (err) {
          reject(err)
          return
        }
        resolve(row.count)
      })
    }).catch(reject)
  })
}

export function assignRolesToNonAdmins(): Promise<{ message: string }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      // Get all users without admin role
      db.all(`
        SELECT u.id, u.name FROM users u
        LEFT JOIN roles r ON u.id = r.user_id
        WHERE r.role IS NULL OR r.role != 'admin'
      `, async (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }

        if (rows.length < 3) {
          resolve({ message: `Need at least 3 non-admin users to assign roles (found ${rows.length})` })
          return
        }

        try {
          // Clear existing non-admin roles
          await clearNonAdminRoles()

          // Shuffle users randomly
          const shuffled = [...rows].sort(() => Math.random() - 0.5)

          // Assign exactly 1 detective, 1 liar, and everyone else as normal
          await assignRole(shuffled[0].id, 'detective')
          await assignRole(shuffled[1].id, 'liar')

          // Assign normal role to everyone else
          for (let i = 2; i < shuffled.length; i++) {
            await assignRole(shuffled[i].id, 'normal')
          }

          resolve({
            message: `Roles assigned successfully: 1 detective, 1 liar, ${shuffled.length - 2} normal users`
          })
        } catch (error) {
          reject(error)
        }
      })
    }).catch(reject)
  })
}

export function assignStickyRolesToNonAdmins(): Promise<{ message: string }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      // Get all users with their current roles
      db.all(`
        SELECT u.id, u.name, r.role FROM users u
        LEFT JOIN roles r ON u.id = r.user_id
        WHERE r.role IS NULL OR r.role != 'admin'
        ORDER BY u.id
      `, async (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }

        if (rows.length < 5) {
          resolve({ message: `Need at least 5 non-admin users to assign roles (found ${rows.length})` })
          return
        }

        try {
          // Count existing special roles
          const existingDetectives = rows.filter(u => u.role === 'detective')
          const existingLiars = rows.filter(u => u.role === 'liar')

          let detectivesNeeded = 0
          let liarsNeeded = 0

          // Determine how many detectives and liars we need based on user count
          if (rows.length >= 9) {
            // 9+ users: 2 detectives, 2 liars
            detectivesNeeded = 2 - existingDetectives.length
            liarsNeeded = 2 - existingLiars.length
          } else {
            // 5-8 users: 1 detective, 1 liar
            detectivesNeeded = 1 - existingDetectives.length
            liarsNeeded = 1 - existingLiars.length
          }

          let newAssignments = 0

          // First, assign normal role to any unassigned users
          const unassignedUsers = rows.filter(u => !u.role)
          for (const user of unassignedUsers) {
            await assignRole(user.id, 'normal')
            newAssignments++
          }

          // If we need more detectives or liars, promote from normal users
          if (detectivesNeeded > 0 || liarsNeeded > 0) {
            // Get users who are currently normal (including newly assigned ones)
            const availableNormalUsers = rows.filter(u => u.role === 'normal' || !u.role)
            const shuffledNormal = [...availableNormalUsers].sort(() => Math.random() - 0.5)
            let promotionIndex = 0

            // Assign needed detectives
            for (let i = 0; i < detectivesNeeded && promotionIndex < shuffledNormal.length; i++) {
              await assignRole(shuffledNormal[promotionIndex].id, 'detective')
              promotionIndex++
              newAssignments++
            }

            // Assign needed liars
            for (let i = 0; i < liarsNeeded && promotionIndex < shuffledNormal.length; i++) {
              await assignRole(shuffledNormal[promotionIndex].id, 'liar')
              promotionIndex++
              newAssignments++
            }
          }

          const finalDetectives = existingDetectives.length + Math.max(0, detectivesNeeded)
          const finalLiars = existingLiars.length + Math.max(0, liarsNeeded)
          const finalNormal = rows.length - finalDetectives - finalLiars

          resolve({
            message: `Roles assigned: ${finalDetectives} detective(s), ${finalLiars} liar(s), ${finalNormal} normal users (${newAssignments} new assignments)`
          })
        } catch (error) {
          reject(error)
        }
      })
    }).catch(reject)
  })
}

// Notes functions
export function createNote(userId: number, content: string): Promise<{ lastID: number }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.run('INSERT INTO notes (user_id, content) VALUES (?, ?)',
        [userId, content],
        function(this: sqlite3.RunResult, err: Error | null) {
          if (err) {
            reject(err)
            return
          }
          resolve({ lastID: this.lastID })
        }
      )
    }).catch(reject)
  })
}

export function getAllNotes(): Promise<any[]> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.all(`
        SELECT n.*, u.name as user_name
        FROM notes n
        JOIN users u ON n.user_id = u.id
        ORDER BY n.created_at DESC
      `, (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }
        resolve(rows)
      })
    }).catch(reject)
  })
}

export function getUserPasswords(): Promise<any[]> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.all(`
        SELECT up.*, u.name as user_name
        FROM user_passwords up
        JOIN users u ON up.user_id = u.id
        ORDER BY u.name
      `, (err: Error | null, rows: any[]) => {
        if (err) {
          reject(err)
          return
        }
        resolve(rows)
      })
    }).catch(reject)
  })
}

// Clear all data except admin
export function clearDatabase(): Promise<{ message: string }> {
  return new Promise((resolve, reject) => {
    getDatabase().then(db => {
      db.serialize(() => {
        db.run('DELETE FROM notes', (err: Error | null) => {
          if (err) {
            reject(err)
            return
          }

          db.run('DELETE FROM user_passwords WHERE user_id != (SELECT id FROM users WHERE name = "admin")', (err: Error | null) => {
            if (err) {
              reject(err)
              return
            }

            db.run('DELETE FROM roles WHERE role != "admin"', (err: Error | null) => {
              if (err) {
                reject(err)
                return
              }

              db.run('DELETE FROM users WHERE name != "admin"', (err: Error | null) => {
                if (err) {
                  reject(err)
                  return
                }
                resolve({ message: 'Database cleared successfully' })
              })
            })
          })
        })
      })
    }).catch(reject)
  })
}
