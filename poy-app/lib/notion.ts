import { Client } from '@notionhq/client'
import { NotionPerson } from './types'

const notion = new Client({
  auth: process.env.NOTION_API_KEY,
})

export async function fetchNotionDatabase(): Promise<NotionPerson[]> {
  try {
    const response = await notion.databases.query({
      database_id: process.env.NOTION_DB_ID!,
    })

    return response.results.map((item: any) => {
      const properties = item.properties

      // Name (title)
      let name = ''
      const nameProp = properties["What’s your name?"]
      if (nameProp?.title?.length > 0) {
        const t = nameProp.title[0]
        name = t.plain_text || t.text?.content || ''
      }

      // Email
      const emailProp = properties["What’s your preferred email?"]
      const email = emailProp?.email || ''

      // Phone
      const phoneProp = properties["What’s your phone number? "]
      const phone = phoneProp?.phone_number || ''

      // About (rich_text)
      let about = ''
      const aboutProp = properties["Tell us a little about yourself"]
      if (aboutProp?.rich_text?.length > 0) {
        const rt = aboutProp.rich_text[0]
        about = rt.plain_text || rt.text?.content || ''
      }

      // Connection (rich_text)
      let connection = ''
      const connectionProp = properties["Whats your coolest n <= 3 degree connection"]
      if (connectionProp?.rich_text?.length > 0) {
        const rt = connectionProp.rich_text[0]
        connection = rt.plain_text || rt.text?.content || ''
      }

      // Selfie (files)
      const selfieProp = properties["Send a selfie ;)"]
      const selfie =
        selfieProp?.files?.map(
          (f: any) => f.file?.url || f.external?.url || f.url || ''
        ).filter(Boolean) || []

      // Respondent (created_by)
      const respondentProp = properties["Respondent"]
      const respondent = respondentProp?.created_by?.id || ''

      // Submission time (created_time)
      const submissionTimeProp = properties["Submission time"]
      const submissionTime = submissionTimeProp?.created_time || ''

      return {
        id: item.id,
        name,
        email,
        phone,
        about,
        connection,
        selfie,
        respondent,
        submissionTime,
      }
    })
  } catch (error) {
    console.error('Error fetching from Notion:', error)
    throw new Error('Failed to fetch from Notion')
  }
}
