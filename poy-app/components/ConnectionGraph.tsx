'use client'

import { useEffect, useRef } from 'react'

interface ConnectionGraphProps {
  connection: string
}

export default function ConnectionGraph({ connection }: ConnectionGraphProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // Parse connection string like "adit→high school friend→works with <PERSON>"
  const parseConnection = (connectionStr: string) => {
    const parts = connectionStr.split('→').map(part => part.trim())
    return parts
  }

  const connectionParts = parseConnection(connection)

  // Enhanced graph visualization with network-style layout
  return (
    <div ref={containerRef} className="w-full">
      {connectionParts.length > 1 ? (
        <div className="relative">
          {/* Compact Network Graph Style */}
          <div className="flex flex-col space-y-2">
            {connectionParts.map((part, index) => (
              <div key={index} className="flex items-center">
                {/* Node */}
                <div className={`
                  relative px-2 py-1 rounded-lg text-xs font-medium transition-all duration-300
                  ${index === 0
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm'
                    : index === connectionParts.length - 1
                      ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-sm'
                      : 'bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-sm'
                  }
                `}>
                  {part.length > 20 ? `${part.substring(0, 20)}...` : part}
                </div>

                {/* Connection Arrow */}
                {index < connectionParts.length - 1 && (
                  <div className="flex items-center ml-2">
                    <div className="w-4 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400"></div>
                    <div className="w-1 h-1 bg-purple-400 rounded-full ml-1"></div>
                  </div>
                )}
              </div>
            ))}
          </div>

        </div>
      ) : (
        <div className="text-sm text-gray-600 italic bg-gray-50 rounded-lg p-3">
          {connection}
        </div>
      )}
    </div>
  )
}
