'use client'

import { useEffect, useRef } from 'react'

interface ConnectionGraphProps {
  connection: string
}

export default function ConnectionGraph({ connection }: ConnectionGraphProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  // Parse connection string like "adit→high school friend→works with <PERSON>"
  const parseConnection = (connectionStr: string) => {
    const parts = connectionStr.split('→').map(part => part.trim())
    return parts
  }

  const connectionParts = parseConnection(connection)

  // Enhanced graph visualization with network-style layout
  return (
    <div ref={containerRef} className="w-full">
      {connectionParts.length > 1 ? (
        <div className="relative">
          {/* Network Graph Style */}
          <div className="flex flex-wrap items-center justify-center gap-3 p-4">
            {connectionParts.map((part, index) => (
              <div key={index} className="flex items-center">
                {/* Node */}
                <div className={`
                  relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-500 hover:scale-105 cursor-pointer
                  ${index === 0
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-200'
                    : index === connectionParts.length - 1
                      ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg shadow-purple-200'
                      : 'bg-gradient-to-r from-gray-400 to-gray-500 text-white shadow-lg shadow-gray-200'
                  }
                `}>
                  {part}
                  {/* Glow effect */}
                  <div className={`absolute inset-0 rounded-xl blur-sm opacity-30 ${
                    index === 0
                      ? 'bg-blue-400'
                      : index === connectionParts.length - 1
                        ? 'bg-purple-400'
                        : 'bg-gray-400'
                  }`}></div>
                </div>

                {/* Connection Arrow */}
                {index < connectionParts.length - 1 && (
                  <div className="flex items-center mx-3">
                    <div className="relative">
                      {/* Animated connection line */}
                      <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-pulse"></div>
                      </div>
                      {/* Arrow head */}
                      <div className="absolute -right-1 -top-1 w-2 h-2 bg-purple-400 rotate-45 transform"></div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Alternative: Vertical Flow for longer connections */}
          {connectionParts.length > 3 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="text-xs text-gray-500 mb-2">Connection Path:</div>
              <div className="flex flex-col space-y-2">
                {connectionParts.map((part, index) => (
                  <div key={`vertical-${index}`} className="flex items-center text-sm">
                    <div className={`w-3 h-3 rounded-full mr-3 ${
                      index === 0
                        ? 'bg-blue-500'
                        : index === connectionParts.length - 1
                          ? 'bg-purple-500'
                          : 'bg-gray-400'
                    }`}></div>
                    <span className="text-gray-700">{part}</span>
                    {index < connectionParts.length - 1 && (
                      <div className="ml-auto text-gray-400">↓</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-sm text-gray-600 italic bg-gray-50 rounded-lg p-3">
          {connection}
        </div>
      )}
    </div>
  )
}
