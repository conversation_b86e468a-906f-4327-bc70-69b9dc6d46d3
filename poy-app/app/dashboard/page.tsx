'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import ConnectionGraph from '@/components/ConnectionGraph'

interface User {
  id: number
  name: string
  email: string | null
  phone: string | null
  about: string | null
  connection: string | null
  selfie: string | null
  role: 'detective' | 'liar' | 'normal' | 'admin'
}

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null)
  const [allUsers, setAllUsers] = useState<User[]>([])
  const [currentUserIndex, setCurrentUserIndex] = useState(0)
  const [loading, setLoading] = useState(true)
  const [noteContent, setNoteContent] = useState('')
  const [submittingNote, setSubmittingNote] = useState(false)
  const [noteMessage, setNoteMessage] = useState('')
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user')
    if (!userData) {
      router.push('/')
      return
    }

    const userObj = JSON.parse(userData)
    if (userObj.role === 'admin') {
      router.push('/admin')
      return
    }

    setUser(userObj)
    fetchUsers()
  }, [router])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/people')
      const data = await response.json()
      
      if (data.success) {
        // Filter out admin users and the current user
        const filteredUsers = data.users.filter((u: User) => 
          u.role !== 'admin' && u.name !== user?.name
        )
        setAllUsers(filteredUsers)
      }
    } catch (error) {
      console.error('Failed to fetch users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('user')
    router.push('/')
  }

  const nextUser = () => {
    setCurrentUserIndex((prev) => (prev + 1) % allUsers.length)
  }

  const prevUser = () => {
    setCurrentUserIndex((prev) => (prev - 1 + allUsers.length) % allUsers.length)
  }

  const handleSubmitNote = async () => {
    if (!noteContent.trim()) {
      setNoteMessage('Please enter a note before submitting.')
      return
    }

    setSubmittingNote(true)
    setNoteMessage('')

    try {
      const response = await fetch('/api/notes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userName: user?.name,
          content: noteContent.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        setNoteMessage('Note submitted successfully!')
        setNoteContent('')
      } else {
        setNoteMessage(data.message || 'Failed to submit note')
      }
    } catch (error) {
      setNoteMessage('Network error. Please try again.')
    } finally {
      setSubmittingNote(false)
      // Clear message after 3 seconds
      setTimeout(() => setNoteMessage(''), 3000)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) return null

  const getRoleStyles = (role: string) => {
    switch (role) {
      case 'detective':
        return {
          bg: 'bg-gradient-to-r from-blue-600 to-blue-800',
          text: 'text-white',
          icon: '🕵️',
          description: 'You are the DETECTIVE! Find the liar among the group.'
        }
      case 'liar':
        return {
          bg: 'bg-gradient-to-r from-red-600 to-red-800',
          text: 'text-white',
          icon: '🎭',
          description: 'You are the LIAR! Blend in and avoid detection.'
        }
      case 'normal':
        return {
          bg: 'bg-gradient-to-r from-green-600 to-green-800',
          text: 'text-white',
          icon: '👤',
          description: 'You are a NORMAL person. Help the detective find the liar!'
        }
      default:
        return {
          bg: 'bg-gradient-to-r from-gray-600 to-gray-800',
          text: 'text-white',
          icon: '❓',
          description: 'Your role has not been assigned yet.'
        }
    }
  }

  const roleStyles = getRoleStyles(user.role)

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="max-w-6xl mx-auto py-8 px-4">
        {/* Header with Logout */}
        <div className="flex justify-end mb-6">
          <button
            onClick={handleLogout}
            className="px-6 py-2 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors shadow-lg"
          >
            Logout
          </button>
        </div>

        {/* Prominent Role Display */}
        <div className={`${roleStyles.bg} ${roleStyles.text} rounded-3xl shadow-2xl p-8 mb-8 text-center transform hover:scale-105 transition-transform duration-300`}>
          <div className="text-8xl mb-4">{roleStyles.icon}</div>
          <h1 className="text-4xl font-bold mb-2">Welcome, {user.name}!</h1>
          <div className="text-2xl font-semibold mb-4 uppercase tracking-wider">
            YOUR ROLE: {user.role}
          </div>
          <p className="text-xl opacity-90 max-w-2xl mx-auto">
            {roleStyles.description}
          </p>
        </div>

        {/* Playing Cards Carousel */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Meet the Others</h2>

          {allUsers.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">👥</div>
              <p className="text-xl text-gray-500">No other users found.</p>
              <p className="text-gray-400 mt-2">Check back later!</p>
            </div>
          ) : (
            <div className="max-w-4xl mx-auto">
              {/* Playing Cards Stack */}
              <div className="relative h-[600px] flex items-center justify-center perspective-1000">
                {allUsers.map((user, index) => {
                  const offset = index - currentUserIndex
                  const isActive = index === currentUserIndex
                  const isVisible = Math.abs(offset) <= 2

                  if (!isVisible) return null

                  return (
                    <div
                      key={user.id}
                      className={`absolute w-80 h-[500px] bg-gradient-to-br from-white via-gray-50 to-white rounded-3xl shadow-2xl border border-gray-200 transition-all duration-700 ease-out transform-gpu ${
                        isActive
                          ? 'z-30 scale-100 rotate-0 translate-x-0'
                          : offset > 0
                            ? `z-${20 - Math.abs(offset)} scale-90 rotate-${offset * 3} translate-x-${offset * 20} translate-y-${Math.abs(offset) * 10}`
                            : `z-${20 - Math.abs(offset)} scale-90 rotate-${offset * 3} translate-x-${offset * 20} translate-y-${Math.abs(offset) * 10}`
                      } ${!isActive ? 'opacity-70 hover:opacity-90' : ''}`}
                      style={{
                        transform: isActive
                          ? 'translateX(0) translateY(0) scale(1) rotateY(0deg) rotateZ(0deg)'
                          : `translateX(${offset * 60}px) translateY(${Math.abs(offset) * 20}px) scale(0.85) rotateY(${offset * 15}deg) rotateZ(${offset * 2}deg)`,
                        zIndex: isActive ? 30 : 20 - Math.abs(offset)
                      }}
                      onClick={() => !isActive && setCurrentUserIndex(index)}
                    >
                      <div className="p-8 h-full flex flex-col">
                        {/* Profile Picture */}
                        <div className="mb-6 flex-shrink-0">
                          {user.selfie ? (
                            <img
                              src={user.selfie}
                              alt={`${user.name}'s photo`}
                              className="w-32 h-32 rounded-full object-cover mx-auto shadow-lg ring-4 ring-white"
                            />
                          ) : (
                            <div className="w-32 h-32 rounded-full bg-gradient-to-br from-gray-300 to-gray-400 mx-auto shadow-lg ring-4 ring-white flex items-center justify-center">
                              <span className="text-4xl text-white">👤</span>
                            </div>
                          )}
                        </div>

                        {/* Name */}
                        <h3 className="text-2xl font-bold text-gray-900 mb-4 text-center">{user.name}</h3>

                        {/* Bio */}
                        <div className="bg-gray-50/80 rounded-xl p-4 mb-4 flex-grow overflow-hidden">
                          <p className="text-gray-700 text-sm leading-relaxed line-clamp-4">
                            {user.about || "This person hasn't shared anything about themselves yet."}
                          </p>
                        </div>

                        {/* Connection Graph */}
                        {user.connection && (
                          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mt-auto">
                            <h4 className="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                              <span className="mr-2">🔗</span>
                              Cool Connection
                            </h4>
                            <ConnectionGraph connection={user.connection} />
                          </div>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* Navigation */}
              <div className="flex items-center justify-center space-x-6 mt-8">
                <button
                  onClick={prevUser}
                  className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center text-xl font-bold"
                  disabled={allUsers.length <= 1}
                >
                  ←
                </button>

                <div className="text-center">
                  <div className="text-sm text-gray-500 mb-2">
                    {currentUserIndex + 1} of {allUsers.length}
                  </div>
                  {/* Dots indicator */}
                  <div className="flex space-x-2">
                    {allUsers.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentUserIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          index === currentUserIndex
                            ? 'bg-indigo-600 scale-125 shadow-lg'
                            : 'bg-gray-300 hover:bg-gray-400 hover:scale-110'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <button
                  onClick={nextUser}
                  className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center text-xl font-bold"
                  disabled={allUsers.length <= 1}
                >
                  →
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Notes Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 mt-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Send a Note</h2>
          <div className="max-w-2xl mx-auto">
            <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg p-6 border border-gray-100">
              <textarea
                value={noteContent}
                onChange={(e) => setNoteContent(e.target.value)}
                placeholder="Write your note here... (max 1000 characters)"
                className="w-full h-32 p-4 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                maxLength={1000}
                disabled={submittingNote}
              />

              <div className="flex justify-between items-center mt-4">
                <span className="text-sm text-gray-500">
                  {noteContent.length}/1000 characters
                </span>
                <button
                  onClick={handleSubmitNote}
                  disabled={submittingNote || !noteContent.trim()}
                  className="px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  {submittingNote ? 'Submitting...' : 'Submit Note'}
                </button>
              </div>

              {noteMessage && (
                <div className={`mt-4 p-3 rounded-lg text-center ${
                  noteMessage.includes('successfully')
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {noteMessage}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
