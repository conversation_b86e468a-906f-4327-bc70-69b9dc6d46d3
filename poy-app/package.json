{"name": "poy-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "start:init": "node -e \"require('./lib/cache').ensureCacheInitialized().then(() => console.log('Cache initialized')).catch(console.error)\""}, "dependencies": {"@notionhq/client": "^2.2.14", "bcryptjs": "^2.4.3", "next": "14.0.0", "react": "^18", "react-dom": "^18", "sqlite3": "^5.1.6", "vis-network": "^10.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/sqlite3": "^3.1.11", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.0", "typescript": "^5"}}