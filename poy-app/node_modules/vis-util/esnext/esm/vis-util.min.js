/**
 * vis-util
 * https://github.com/visjs/vis-util
 *
 * utilitie collection for visjs
 *
 * @version 6.0.0
 * @date    2025-07-12T18:02:43.836Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
import t from"component-emitter";import e from"@egjs/hammerjs";const i=Symbol("DELETE");function o(t,...e){return n({},t,...e)}function n(...t){const e=r(...t);return a(e),e}function r(...t){if(t.length<2)return t[0];if(t.length>2)return r(n(t[0],t[1]),...t.slice(2));const e=t[0],o=t[1];if(e instanceof Date&&o instanceof Date)return e.setTime(o.getTime()),e;for(const t of Reflect.ownKeys(o))Object.prototype.propertyIsEnumerable.call(o,t)&&(o[t]===i?delete e[t]:null===e[t]||null===o[t]||"object"!=typeof e[t]||"object"!=typeof o[t]||Array.isArray(e[t])||Array.isArray(o[t])?e[t]=s(o[t]):e[t]=r(e[t],o[t]));return e}function s(t){return Array.isArray(t)?t.map(t=>s(t)):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):r({},t):t}function a(t){for(const e of Object.keys(t))t[e]===i?delete t[e]:"object"==typeof t[e]&&null!==t[e]&&a(t[e])}function l(...t){return function(t){let[e,i,o]=function(...t){const e=function(){let t=**********;return function(e){const i=e.toString();for(let e=0;e<i.length;e++){t+=i.charCodeAt(e);let o=.02519603282416938*t;t=o>>>0,o-=t,o*=t,t=o>>>0,o-=t,t+=4294967296*o}return 2.3283064365386963e-10*(t>>>0)}}();let i=e(" "),o=e(" "),n=e(" ");for(let r=0;r<t.length;r++)i-=e(t[r]),i<0&&(i+=1),o-=e(t[r]),o<0&&(o+=1),n-=e(t[r]),n<0&&(n+=1);return[i,o,n]}(t),n=1;const r=()=>{const t=2091639*e+2.3283064365386963e-10*n;return e=i,i=o,o=t-(n=0|t)};return r.uint32=()=>4294967296*r(),r.fract53=()=>r()+11102230246251565e-32*(2097152*r()|0),r.algorithm="Alea",r.seed=t,r.version="0.9",r}(t.length?t:[Date.now()])}const c="undefined"!=typeof window?window.Hammer||e:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function h(t){this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const e=c(this._dom.overlay);e.on("tap",this._onTapOverlay.bind(this)),this._cleanupQueue.push(()=>{e.destroy()});["tap","doubletap","press","pinch","pan","panstart","panmove","panend"].forEach(t=>{e.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}t(h.prototype),h.current=null,h.prototype.destroy=function(){this.deactivate();for(const t of this._cleanupQueue.splice(0).reverse())t()},h.prototype.activate=function(){h.current&&h.current.deactivate(),h.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},h.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},h.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};const d=/^\/?Date\((-?\d+)/i,u=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,p=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,f=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,g=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function m(t){return t instanceof Number||"number"==typeof t}function v(t){if(t)for(;!0===t.hasChildNodes();){const e=t.firstChild;e&&(v(e),t.removeChild(e))}}function b(t){return t instanceof String||"string"==typeof t}function y(t){return"object"==typeof t&&null!==t}function k(t){if(t instanceof Date)return!0;if(b(t)){if(d.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1}function C(t,e,i,o){let n=!1;!0===o&&(n=null===e[i]&&void 0!==t[i]),n?delete t[i]:t[i]=e[i]}function F(t,e,i=!1){for(const o in t)if(void 0!==e[o])if(null===e[o]||"object"!=typeof e[o])C(t,e,o,i);else{const n=t[o],r=e[o];y(n)&&y(r)&&F(n,r,i)}}const _=Object.assign;function w(t,e,...i){if(!Array.isArray(t))throw new Error("Array with property names expected as first argument");for(const o of i)for(let i=0;i<t.length;i++){const n=t[i];o&&Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e}function E(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(let n=0;n<t.length;n++){const r=t[n];if(Object.prototype.hasOwnProperty.call(i,r))if(i[r]&&i[r].constructor===Object)void 0===e[r]&&(e[r]={}),e[r].constructor===Object?x(e[r],i[r],!1,o):C(e,i,r,o);else{if(Array.isArray(i[r]))throw new TypeError("Arrays are not supported by deepExtend");C(e,i,r,o)}}return e}function D(t,e,i,o=!1){if(Array.isArray(i))throw new TypeError("Arrays are not supported by deepExtend");for(const n in i)if(Object.prototype.hasOwnProperty.call(i,n)&&!t.includes(n))if(i[n]&&i[n].constructor===Object)void 0===e[n]&&(e[n]={}),e[n].constructor===Object?x(e[n],i[n]):C(e,i,n,o);else if(Array.isArray(i[n])){e[n]=[];for(let t=0;t<i[n].length;t++)e[n].push(i[n][t])}else C(e,i,n,o);return e}function x(t,e,i=!1,o=!1){for(const n in e)(Object.prototype.hasOwnProperty.call(e,n)||!0===i)&&("object"==typeof e[n]&&null!==e[n]&&Object.getPrototypeOf(e[n])===Object.prototype?void 0===t[n]?t[n]=x({},e[n],i):"object"==typeof t[n]&&null!==t[n]&&Object.getPrototypeOf(t[n])===Object.prototype?x(t[n],e[n],i):C(t,e,n,o):Array.isArray(e[n])?t[n]=e[n].slice():C(t,e,n,o));return t}function O(t,e){if(t.length!==e.length)return!1;for(let i=0,o=t.length;i<o;i++)if(t[i]!=e[i])return!1;return!0}function P(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Array.isArray(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e}function B(t,e){return[...t,e]}function A(t){return t.slice()}function j(t){return t.getBoundingClientRect().left}function N(t){return t.getBoundingClientRect().right}function R(t){return t.getBoundingClientRect().top}function S(t,e){let i=t.className.split(" ");const o=e.split(" ");i=i.concat(o.filter(function(t){return!i.includes(t)})),t.className=i.join(" ")}function T(t,e){let i=t.className.split(" ");const o=e.split(" ");i=i.filter(function(t){return!o.includes(t)}),t.className=i.join(" ")}function M(t,e){if(Array.isArray(t)){const i=t.length;for(let o=0;o<i;o++)e(t[o],o,t)}else for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&e(t[i],i,t)}const I=Object.values;function L(t,e,i){return t[e]!==i&&(t[e]=i,!0)}function H(t){let e=!1;return()=>{e||(e=!0,requestAnimationFrame(()=>{e=!1,t()}))}}function Q(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)}function q(t=window.event){let e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode,e instanceof Element))?e:null}function W(t,e){let i=t;for(;i;){if(i===e)return!0;if(!i.parentNode)return!1;i=i.parentNode}return!1}const z={asBoolean:(t,e)=>("function"==typeof t&&(t=t()),null!=t?0!=t:e||null),asNumber:(t,e)=>("function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null),asString:(t,e)=>("function"==typeof t&&(t=t()),null!=t?String(t):e||null),asSize:(t,e)=>("function"==typeof t&&(t=t()),b(t)?t:m(t)?t+"px":e||null),asElement:(t,e)=>("function"==typeof t&&(t=t()),t||e||null)};function $(t){let e;switch(t.length){case 3:case 4:return e=p.exec(t),e?{r:parseInt(e[1]+e[1],16),g:parseInt(e[2]+e[2],16),b:parseInt(e[3]+e[3],16)}:null;case 6:case 7:return e=u.exec(t),e?{r:parseInt(e[1],16),g:parseInt(e[2],16),b:parseInt(e[3],16)}:null;default:return null}}function V(t,e){if(t.includes("rgba"))return t;if(t.includes("rgb")){const i=t.substr(t.indexOf("(")+1).replace(")","").split(",");return"rgba("+i[0]+","+i[1]+","+i[2]+","+e+")"}{const i=$(t);return null==i?t:"rgba("+i.r+","+i.g+","+i.b+","+e+")"}}function U(t,e,i){return"#"+((1<<24)+(t<<16)+(e<<8)+i).toString(16).slice(1)}function J(t,e){if(b(t)){let e=t;if(ot(e)){const t=e.substr(4).substr(0,e.length-5).split(",").map(function(t){return parseInt(t)});e=U(t[0],t[1],t[2])}if(!0===it(e)){const t=et(e),i={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},o={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},n=tt(o.h,o.s,o.v),r=tt(i.h,i.s,i.v);return{background:e,border:n,highlight:{background:r,border:n},hover:{background:r,border:n}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}if(e){return{background:t.background||e.background,border:t.border||e.border,highlight:b(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:b(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}}return{background:t.background||void 0,border:t.border||void 0,highlight:b(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:b(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function K(t,e,i){t/=255,e/=255,i/=255;const o=Math.min(t,Math.min(e,i)),n=Math.max(t,Math.max(e,i));if(o===n)return{h:0,s:0,v:o};return{h:60*((t===o?3:i===o?1:5)-(t===o?e-i:i===o?t-e:i-t)/(n-o))/360,s:(n-o)/n,v:n}}function G(t){const e=document.createElement("div"),i={};e.style.cssText=t;for(let t=0;t<e.style.length;++t)i[e.style[t]]=e.style.getPropertyValue(e.style[t]);return i}function X(t,e){const i=G(e);for(const[e,o]of Object.entries(i))t.style.setProperty(e,o)}function Y(t,e){const i=G(e);for(const e of Object.keys(i))t.style.removeProperty(e)}function Z(t,e,i){let o,n,r;const s=Math.floor(6*t),a=6*t-s,l=i*(1-e),c=i*(1-a*e),h=i*(1-(1-a)*e);switch(s%6){case 0:o=i,n=h,r=l;break;case 1:o=c,n=i,r=l;break;case 2:o=l,n=i,r=h;break;case 3:o=l,n=c,r=i;break;case 4:o=h,n=l,r=i;break;case 5:o=i,n=l,r=c}return{r:Math.floor(255*o),g:Math.floor(255*n),b:Math.floor(255*r)}}function tt(t,e,i){const o=Z(t,e,i);return U(o.r,o.g,o.b)}function et(t){const e=$(t);if(!e)throw new TypeError(`'${t}' is not a valid color.`);return K(e.r,e.g,e.b)}function it(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function ot(t){return f.test(t)}function nt(t){return g.test(t)}function rt(t,e){if(null!==e&&"object"==typeof e){const i=Object.create(e);for(let o=0;o<t.length;o++)Object.prototype.hasOwnProperty.call(e,t[o])&&"object"==typeof e[t[o]]&&(i[t[o]]=st(e[t[o]]));return i}return null}function st(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=Object.create(t);for(const i in t)Object.prototype.hasOwnProperty.call(t,i)&&"object"==typeof t[i]&&(e[i]=st(t[i]));return e}function at(t,e){for(let i=0;i<t.length;i++){const o=t[i];let n;for(n=i;n>0&&e(o,t[n-1])<0;n--)t[n]=t[n-1];t[n]=o}return t}function lt(t,e,i,o={}){const n=function(t){return null!=t},r=function(t){return null!==t&&"object"==typeof t};if(!r(t))throw new Error("Parameter mergeTarget must be an object");if(!r(e))throw new Error("Parameter options must be an object");if(!n(i))throw new Error("Parameter option must have a value");if(!r(o))throw new Error("Parameter globalOptions must be an object");const s=e[i],a=r(o)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(o)?o[i]:void 0,l=a?a.enabled:void 0;if(void 0===s)return;if("boolean"==typeof s)return r(t[i])||(t[i]={}),void(t[i].enabled=s);if(null===s&&!r(t[i])){if(!n(a))return;t[i]=Object.create(a)}if(!r(s))return;let c=!0;void 0!==s.enabled?c=s.enabled:void 0!==l&&(c=a.enabled),function(t,e,i){r(t[i])||(t[i]={});const o=e[i],n=t[i];for(const t in o)Object.prototype.hasOwnProperty.call(o,t)&&(n[t]=o[t])}(t,e,i),t[i].enabled=c}function ct(t,e,i,o){let n=0,r=0,s=t.length-1;for(;r<=s&&n<1e4;){const a=Math.floor((r+s)/2),l=t[a],c=e(void 0===o?l[i]:l[i][o]);if(0==c)return a;-1==c?r=a+1:s=a-1,n++}return-1}function ht(t,e,i,o,n){let r,s,a,l,c=0,h=0,d=t.length-1;for(n=null!=n?n:function(t,e){return t==e?0:t<e?-1:1};h<=d&&c<1e4;){if(l=Math.floor(.5*(d+h)),r=t[Math.max(0,l-1)][i],s=t[l][i],a=t[Math.min(t.length-1,l+1)][i],0==n(s,e))return l;if(n(r,e)<0&&n(s,e)>0)return"before"==o?Math.max(0,l-1):l;if(n(s,e)<0&&n(a,e)>0)return"before"==o?l:Math.min(t.length-1,l+1);n(s,e)<0?h=l+1:d=l-1,c++}return-1}const dt={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};function ut(){const t=document.createElement("p");t.style.width="100%",t.style.height="200px";const e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);const i=t.offsetWidth;e.style.overflow="scroll";let o=t.offsetWidth;return i==o&&(o=e.clientWidth),document.body.removeChild(e),i-o}function pt(t,e){let i;Array.isArray(e)||(e=[e]);for(const o of t)if(o){i=o[e[0]];for(let t=1;t<e.length;t++)i&&(i=i[e[t]]);if(void 0!==i)break}return i}const ft={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let gt=class{constructor(t=1){this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return ft[t]}setColor(t,e=!0){if("none"===t)return;let i;const o=this._isColorString(t);if(void 0!==o&&(t=o),!0===b(t)){if(!0===ot(t)){const e=t.substr(4).substr(0,t.length-5).split(",");i={r:e[0],g:e[1],b:e[2],a:1}}else if(!0===nt(t)){const e=t.substr(5).substr(0,t.length-6).split(",");i={r:e[0],g:e[1],b:e[2],a:e[3]}}else if(!0===it(t)){const e=$(t);i={r:e.r,g:e.g,b:e.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const e=void 0!==t.a?t.a:"1.0";i={r:t.r,g:t.g,b:t.b,a:e}}if(void 0===i)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+JSON.stringify(t));this._setColor(i,e)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(t=!0){!0===t&&(this.previousColor=Object.assign({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",setTimeout(()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)},0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t,e=!0){!0===e&&(this.initialColor=Object.assign({},t)),this.color=t;const i=K(t.r,t.g,t.b),o=2*Math.PI,n=this.r*i.s,r=this.centerCoordinates.x+n*Math.sin(o*i.h),s=this.centerCoordinates.y+n*Math.cos(o*i.h);this.colorPickerSelector.style.left=r-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=s-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=K(this.color.r,this.color.g,this.color.b);e.v=t/100;const i=Z(e.h,e.s,e.v);i.a=this.color.a,this.color=i,this._updatePicker()}_updatePicker(t=this.color){const e=K(t.r,t.g,t.b),i=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(i.webkitBackingStorePixelRatio||i.mozBackingStorePixelRatio||i.msBackingStorePixelRatio||i.oBackingStorePixelRatio||i.backingStorePixelRatio||1)),i.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const o=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;i.clearRect(0,0,o,n),i.putImageData(this.hueCircle,0,0),i.fillStyle="rgba(0,0,0,"+(1-e.v)+")",i.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),i.fill(),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const t=this;this.opacityRange.onchange=function(){t._setOpacity(this.value)},this.opacityRange.oninput=function(){t._setOpacity(this.value)},this.brightnessRange.onchange=function(){t._setBrightness(this.value)},this.brightnessRange.oninput=function(){t._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=this._hide.bind(this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=this._apply.bind(this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=this._save.bind(this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=this._loadLast.bind(this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new c(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",t=>{t.isFirst&&this._moveSelector(t)}),this.hammer.on("tap",t=>{this._moveSelector(t)}),this.hammer.on("panstart",t=>{this._moveSelector(t)}),this.hammer.on("panmove",t=>{this._moveSelector(t)}),this.hammer.on("panend",t=>{this._moveSelector(t)})}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;let o,n,r,s;t.clearRect(0,0,e,i),this.centerCoordinates={x:.5*e,y:.5*i},this.r=.49*e;const a=2*Math.PI/360,l=1/360,c=1/this.r;let h;for(r=0;r<360;r++)for(s=0;s<this.r;s++)o=this.centerCoordinates.x+s*Math.sin(a*r),n=this.centerCoordinates.y+s*Math.cos(a*r),h=Z(r*l,s*c,1),t.fillStyle="rgb("+h.r+","+h.g+","+h.b+")",t.fillRect(o-.5,n-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,i)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),i=t.center.x-e.left,o=t.center.y-e.top,n=.5*this.colorPickerDiv.clientHeight,r=.5*this.colorPickerDiv.clientWidth,s=i-r,a=o-n,l=Math.atan2(s,a),c=.98*Math.min(Math.sqrt(s*s+a*a),r),h=Math.cos(l)*c+n,d=Math.sin(l)*c+r;this.colorPickerSelector.style.top=h-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=d-.5*this.colorPickerSelector.clientWidth+"px";let u=l/(2*Math.PI);u=u<0?u+1:u;const p=c/this.r,f=K(this.color.r,this.color.g,this.color.b);f.h=u,f.s=p;const g=Z(f.h,f.s,f.v);g.a=this.color.a,this.color=g,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function mt(...t){if(t.length<1)throw new TypeError("Invalid arguments.");if(1===t.length)return document.createTextNode(t[0]);{const e=document.createElement(t[0]);return e.appendChild(mt(...t.slice(1))),e}}let vt,bt=!1;const yt="background: #FFeeee; color: #dd0000";const kt=h,Ct=gt,Ft=class{constructor(t,e,i,o=1,n=()=>!1){this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=n,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},Object.assign(this.options,this.defaultOptions),this.configureOptions=i,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new gt(o),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Array.isArray(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==t.filter&&(this.options.filter=t.filter),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===this.options.filter&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=this.options.filter;let e=0,i=!1;for(const o in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,o)&&(this.allowCreation=!1,i=!1,"function"==typeof t?(i=t(o,[]),i=i||this._handleObject(this.configureOptions[o],[o],!0)):!0!==t&&-1===t.indexOf(o)||(i=!0),!1!==i&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(o),this._handleObject(this.configureOptions[o],[o])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let i=0;i<t.length;i++){if(void 0===e[t[i]]){e=void 0;break}e=e[t[i]]}return e}_makeItem(t,...e){if(!0===this.allowCreation){const i=document.createElement("div");return i.className="vis-configuration vis-config-item vis-config-s"+t.length,e.forEach(t=>{i.appendChild(t)}),this.domElements.push(i),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e,i=!1){const o=document.createElement("div");if(o.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===i){for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild(mt("i","b",t))}else o.innerText=t+":";return o}_makeDropdown(t,e,i){const o=document.createElement("select");o.className="vis-configuration vis-config-select";let n=0;void 0!==e&&-1!==t.indexOf(e)&&(n=t.indexOf(e));for(let e=0;e<t.length;e++){const i=document.createElement("option");i.value=t[e],e===n&&(i.selected="selected"),i.innerText=t[e],o.appendChild(i)}const r=this;o.onchange=function(){r._update(this.value,i)};const s=this._makeLabel(i[i.length-1],i);this._makeItem(i,s,o)}_makeRange(t,e,i){const o=t[0],n=t[1],r=t[2],s=t[3],a=document.createElement("input");a.className="vis-configuration vis-config-range";try{a.type="range",a.min=n,a.max=r}catch(t){}a.step=s;let l="",c=0;if(void 0!==e){const t=1.2;e<0&&e*t<n?(a.min=Math.ceil(e*t),c=a.min,l="range increased"):e/t<n&&(a.min=Math.ceil(e/t),c=a.min,l="range increased"),e*t>r&&1!==r&&(a.max=Math.ceil(e*t),c=a.max,l="range increased"),a.value=e}else a.value=o;const h=document.createElement("input");h.className="vis-configuration vis-config-rangeinput",h.value=a.value;const d=this;a.onchange=function(){h.value=this.value,d._update(Number(this.value),i)},a.oninput=function(){h.value=this.value};const u=this._makeLabel(i[i.length-1],i),p=this._makeItem(i,u,a,h);""!==l&&this.popupHistory[p]!==c&&(this.popupHistory[p]=c,this._setupPopup(l,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const i=document.createElement("div");i.id="vis-configuration-popup",i.className="vis-configuration-popup",i.innerText=t,i.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:i,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=setTimeout(()=>{this.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=setTimeout(()=>{this._removePopup()},1800)}}_makeCheckbox(t,e,i){const o=document.createElement("input");o.type="checkbox",o.className="vis-configuration vis-config-checkbox",o.checked=t,void 0!==e&&(o.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:i,value:e}):this.changedOptions.push({path:i,value:e})));const n=this;o.onchange=function(){n._update(this.checked,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}_makeTextInput(t,e,i){const o=document.createElement("input");o.type="text",o.className="vis-configuration vis-config-text",o.value=e,e!==t&&this.changedOptions.push({path:i,value:e});const n=this;o.onchange=function(){n._update(this.value,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,o)}_makeColorField(t,e,i){const o=t[1],n=document.createElement("div");"none"!==(e=void 0===e?o:e)?(n.className="vis-configuration vis-config-colorBlock",n.style.backgroundColor=e):n.className="vis-configuration vis-config-colorBlock none",e=void 0===e?o:e,n.onclick=()=>{this._showColorPicker(e,n,i)};const r=this._makeLabel(i[i.length-1],i);this._makeItem(i,r,n)}_showColorPicker(t,e,i){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback(t=>{const o="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=o,this._update(o,i)}),this.colorPicker.setCloseCallback(()=>{e.onclick=()=>{this._showColorPicker(t,e,i)}})}_handleObject(t,e=[],i=!1){let o=!1;const n=this.options.filter;let r=!1;for(const s in t)if(Object.prototype.hasOwnProperty.call(t,s)){o=!0;const a=t[s],l=B(e,s);if("function"==typeof n&&(o=n(s,e),!1===o&&!Array.isArray(a)&&"string"!=typeof a&&"boolean"!=typeof a&&a instanceof Object&&(this.allowCreation=!1,o=this._handleObject(a,l,!0),this.allowCreation=!1===i)),!1!==o){r=!0;const t=this._getValue(l);if(Array.isArray(a))this._handleArray(a,t,l);else if("string"==typeof a)this._makeTextInput(a,t,l);else if("boolean"==typeof a)this._makeCheckbox(a,t,l);else if(a instanceof Object){if(!this.hideOption(e,s,this.moduleOptions))if(void 0!==a.enabled){const t=B(l,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(s,l,!0);this._makeItem(l,t),r=this._handleObject(a,l)||r}else this._makeCheckbox(a,e,l)}else{const t=this._makeLabel(s,l,!0);this._makeItem(l,t),r=this._handleObject(a,l)||r}}else console.error("dont know how to handle",a,s,l)}}return r}_handleArray(t,e,i){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,i),t[1]!==e&&this.changedOptions.push({path:i,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,i),t[0]!==e&&this.changedOptions.push({path:i,value:Number(e)}))}_update(t,e){const i=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",i),this.initialized=!0,this.parent.setOptions(i)}_constructOptions(t,e,i={}){let o=i;t="false"!==(t="true"===t||t)&&t;for(let i=0;i<e.length;i++)"global"!==e[i]&&(void 0===o[e[i]]&&(o[e[i]]={}),i!==e.length-1?o=o[e[i]]:o[e[i]]=t);return i}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(mt("pre","const options = "+JSON.stringify(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},_t=c,wt=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=parseInt(t),this.y=parseInt(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,i=this.frame.parentNode.clientHeight,o=this.frame.parentNode.clientWidth;let n=0,r=0;if("flip"==this.overflowMethod){let i=!1,s=!0;this.y-t<this.padding&&(s=!1),this.x+e>o-this.padding&&(i=!0),n=i?this.x-e:this.x,r=s?this.y-t:this.y}else r=this.y-t,r+t+this.padding>i&&(r=i-t-this.padding),r<this.padding&&(r=this.padding),n=this.x,n+e+this.padding>o&&(n=o-e-this.padding),n<this.padding&&(n=this.padding);this.frame.style.left=n+"px",this.frame.style.top=r+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},Et=yt,Dt=class t{static validate(e,i,o){bt=!1,vt=i;let n=i;return void 0!==o&&(n=i[o]),t.parse(e,n,[]),bt}static parse(e,i,o){for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.check(n,e,i,o)}static check(e,i,o,n){if(void 0===o[e]&&void 0===o.__any__)return void t.getSuggestion(e,o,n);let r=e,s=!0;void 0===o[e]&&void 0!==o.__any__&&(r="__any__",s="object"===t.getType(i[e]));let a=o[r];s&&void 0!==a.__type__&&(a=a.__type__),t.checkFields(e,i,o,r,a,n)}static checkFields(e,i,o,n,r,s){const a=function(i){console.error("%c"+i+t.printLocation(s,e),yt)},l=t.getType(i[e]),c=r[l];void 0!==c?"array"===t.getType(c)&&-1===c.indexOf(i[e])?(a('Invalid option detected in "'+e+'". Allowed values are:'+t.print(c)+' not "'+i[e]+'". '),bt=!0):"object"===l&&"__any__"!==n&&(s=B(s,e),t.parse(i[e],o[n],s)):void 0===r.any&&(a('Invalid type received for "'+e+'". Expected: '+t.print(Object.keys(r))+". Received ["+l+'] "'+i[e]+'"'),bt=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Array.isArray(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,i,o){const n=t.findInOptions(e,i,o,!1),r=t.findInOptions(e,vt,[],!0);let s;s=void 0!==n.indexMatch?" in "+t.printLocation(n.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+n.indexMatch+'"?\n\n':r.distance<=4&&n.distance>r.distance?" in "+t.printLocation(n.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(r.path,r.closestMatch,""):n.distance<=8?'. Did you mean "'+n.closestMatch+'"?'+t.printLocation(n.path,e):". Did you mean one of these: "+t.print(Object.keys(i))+t.printLocation(o,e),console.error('%cUnknown option detected: "'+e+'"'+s,yt),bt=!0}static findInOptions(e,i,o,n=!1){let r=1e9,s="",a=[];const l=e.toLowerCase();let c;for(const h in i){let d;if(void 0!==i[h].__type__&&!0===n){const n=t.findInOptions(e,i[h],B(o,h));r>n.distance&&(s=n.closestMatch,a=n.path,r=n.distance,c=n.indexMatch)}else-1!==h.toLowerCase().indexOf(l)&&(c=h),d=t.levenshteinDistance(e,h),r>d&&(s=h,a=A(o),r=d)}return{closestMatch:s,path:a,distance:r,indexMatch:c}}static printLocation(t,e,i="Problem value found at: \n"){let o="\n\n"+i+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)o+="  ";o+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)o+="  ";o+=e+"\n";for(let e=0;e<t.length+1;e++){for(let i=0;i<t.length-e;i++)o+="  ";o+="}\n"}return o+"\n\n"}static print(t){return JSON.stringify(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const i=[];let o,n;for(o=0;o<=e.length;o++)i[o]=[o];for(n=0;n<=t.length;n++)i[0][n]=n;for(o=1;o<=e.length;o++)for(n=1;n<=t.length;n++)e.charAt(o-1)==t.charAt(n-1)?i[o][n]=i[o-1][n-1]:i[o][n]=Math.min(i[o-1][n-1]+1,Math.min(i[o][n-1]+1,i[o-1][n]+1));return i[e.length][t.length]}};export{kt as Activator,l as Alea,Ct as ColorPicker,Ft as Configurator,i as DELETE,tt as HSVToHex,Z as HSVToRGB,_t as Hammer,wt as Popup,K as RGBToHSV,U as RGBToHex,Et as VALIDATOR_PRINT_STYLE,Dt as Validator,S as addClassName,X as addCssText,ct as binarySearchCustom,ht as binarySearchValue,st as bridgeObject,B as copyAndExtendArray,A as copyArray,x as deepExtend,n as deepObjectAssign,dt as easingFunctions,O as equalArray,_ as extend,F as fillIfDefined,M as forEach,j as getAbsoluteLeft,N as getAbsoluteRight,R as getAbsoluteTop,ut as getScrollBarWidth,q as getTarget,P as getType,W as hasParent,et as hexToHSV,$ as hexToRGB,at as insertSort,k as isDate,m as isNumber,y as isObject,b as isString,it as isValidHex,ot as isValidRGB,nt as isValidRGBA,lt as mergeOptions,z as option,V as overrideOpacity,J as parseColor,Q as preventDefault,o as pureDeepObjectAssign,v as recursiveDOMDelete,T as removeClassName,Y as removeCssText,rt as selectiveBridgeObject,E as selectiveDeepExtend,w as selectiveExtend,D as selectiveNotDeepExtend,H as throttle,I as toArray,pt as topMost,L as updateProperty};
//# sourceMappingURL=vis-util.min.js.map
