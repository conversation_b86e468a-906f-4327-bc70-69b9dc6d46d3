{"version": 3, "file": "vis-util.cjs", "sources": ["../../../src/deep-object-assign.ts", "../../../src/random/alea.ts", "../../src/shared/hammer.js", "../../src/shared/activator.js", "../../../src/util.ts", "../../src/shared/color-picker.js", "../../src/shared/configurator.js", "../../src/shared/popup.js", "../../src/shared/validator.js", "../../../src/shared/index.ts"], "sourcesContent": [null, null, "import RealHammer from \"@egjs/hammerjs\";\n\n/**\n * Setup a mock hammer.js object, for unit testing.\n *\n * Inspiration: https://github.com/uber/deck.gl/pull/658\n * @returns {{on: noop, off: noop, destroy: noop, emit: noop, get: get}}\n */\nfunction hammerMock() {\n  const noop = () => {};\n\n  return {\n    on: noop,\n    off: noop,\n    destroy: noop,\n    emit: noop,\n\n    get() {\n      return {\n        set: noop,\n      };\n    },\n  };\n}\n\nconst Hammer =\n  typeof window !== \"undefined\"\n    ? window.Hammer || RealHammer\n    : function () {\n        // hammer.js is only available in a browser, not in node.js. Replacing it with a mock object.\n        return hammerMock();\n      };\n\nexport { Hammer };\n", "import Emitter from \"component-emitter\";\nimport { <PERSON> } from \"./hammer.js\";\n\n/**\n * Turn an element into an clickToUse element.\n * When not active, the element has a transparent overlay. When the overlay is\n * clicked, the mode is changed to active.\n * When active, the element is displayed with a blue border around it, and\n * the interactive contents of the element can be used. When clicked outside\n * the element, the elements mode is changed to inactive.\n * @param {Element} container\n * @class Activator\n */\nexport function Activator(container) {\n  this._cleanupQueue = [];\n\n  this.active = false;\n\n  this._dom = {\n    container,\n    overlay: document.createElement(\"div\"),\n  };\n\n  this._dom.overlay.classList.add(\"vis-overlay\");\n\n  this._dom.container.appendChild(this._dom.overlay);\n  this._cleanupQueue.push(() => {\n    this._dom.overlay.parentNode.removeChild(this._dom.overlay);\n  });\n\n  const hammer = Hammer(this._dom.overlay);\n  hammer.on(\"tap\", this._onTapOverlay.bind(this));\n  this._cleanupQueue.push(() => {\n    hammer.destroy();\n    // FIXME: cleaning up hammer instances doesn't work (Timeline not removed\n    // from memory)\n  });\n\n  // block all touch events (except tap)\n  const events = [\n    \"tap\",\n    \"doubletap\",\n    \"press\",\n    \"pinch\",\n    \"pan\",\n    \"panstart\",\n    \"panmove\",\n    \"panend\",\n  ];\n  events.forEach((event) => {\n    hammer.on(event, (event) => {\n      event.srcEvent.stopPropagation();\n    });\n  });\n\n  // attach a click event to the window, in order to deactivate when clicking outside the timeline\n  if (document && document.body) {\n    this._onClick = (event) => {\n      if (!_hasParent(event.target, container)) {\n        this.deactivate();\n      }\n    };\n    document.body.addEventListener(\"click\", this._onClick);\n    this._cleanupQueue.push(() => {\n      document.body.removeEventListener(\"click\", this._onClick);\n    });\n  }\n\n  // prepare escape key listener for deactivating when active\n  this._escListener = (event) => {\n    if (\n      \"key\" in event\n        ? event.key === \"Escape\"\n        : event.keyCode === 27 /* the keyCode is for IE11 */\n    ) {\n      this.deactivate();\n    }\n  };\n}\n\n// turn into an event emitter\nEmitter(Activator.prototype);\n\n// The currently active activator\nActivator.current = null;\n\n/**\n * Destroy the activator. Cleans up all created DOM and event listeners\n */\nActivator.prototype.destroy = function () {\n  this.deactivate();\n\n  for (const callback of this._cleanupQueue.splice(0).reverse()) {\n    callback();\n  }\n};\n\n/**\n * Activate the element\n * Overlay is hidden, element is decorated with a blue shadow border\n */\nActivator.prototype.activate = function () {\n  // we allow only one active activator at a time\n  if (Activator.current) {\n    Activator.current.deactivate();\n  }\n  Activator.current = this;\n\n  this.active = true;\n  this._dom.overlay.style.display = \"none\";\n  this._dom.container.classList.add(\"vis-active\");\n\n  this.emit(\"change\");\n  this.emit(\"activate\");\n\n  // ugly hack: bind ESC after emitting the events, as the Network rebinds all\n  // keyboard events on a 'change' event\n  document.body.addEventListener(\"keydown\", this._escListener);\n};\n\n/**\n * Deactivate the element\n * Overlay is displayed on top of the element\n */\nActivator.prototype.deactivate = function () {\n  this.active = false;\n  this._dom.overlay.style.display = \"block\";\n  this._dom.container.classList.remove(\"vis-active\");\n  document.body.removeEventListener(\"keydown\", this._escListener);\n\n  this.emit(\"change\");\n  this.emit(\"deactivate\");\n};\n\n/**\n * Handle a tap event: activate the container\n * @param {Event}  event   The event\n * @private\n */\nActivator.prototype._onTapOverlay = function (event) {\n  // activate the container\n  this.activate();\n  event.srcEvent.stopPropagation();\n};\n\n/**\n * Test whether the element has the requested parent element somewhere in\n * its chain of parent nodes.\n * @param {HTMLElement} element\n * @param {HTMLElement} parent\n * @returns {boolean} Returns true when the parent is found somewhere in the\n *                    chain of parent nodes.\n * @private\n */\nfunction _hasParent(element, parent) {\n  while (element) {\n    if (element === parent) {\n      return true;\n    }\n    element = element.parentNode;\n  }\n  return false;\n}\n", null, "import { Hammer } from \"./hammer.js\";\nimport {\n  HSVToRGB,\n  RGBToHSV,\n  hexToRGB,\n  isString,\n  isValidHex,\n  isValidRGB,\n  isValidRGBA,\n} from \"../util.ts\";\n\nconst htmlColors = {\n  black: \"#000000\",\n  navy: \"#000080\",\n  darkblue: \"#00008B\",\n  mediumblue: \"#0000CD\",\n  blue: \"#0000FF\",\n  darkgreen: \"#006400\",\n  green: \"#008000\",\n  teal: \"#008080\",\n  darkcyan: \"#008B8B\",\n  deepskyblue: \"#00BFFF\",\n  darkturquoise: \"#00CED1\",\n  mediumspringgreen: \"#00FA9A\",\n  lime: \"#00FF00\",\n  springgreen: \"#00FF7F\",\n  aqua: \"#00FFFF\",\n  cyan: \"#00FFFF\",\n  midnightblue: \"#191970\",\n  dodgerblue: \"#1E90FF\",\n  lightseagreen: \"#20B2AA\",\n  forestgreen: \"#228B22\",\n  seagreen: \"#2E8B57\",\n  darkslategray: \"#2F4F4F\",\n  limegreen: \"#32CD32\",\n  mediumseagreen: \"#3CB371\",\n  turquoise: \"#40E0D0\",\n  royalblue: \"#4169E1\",\n  steelblue: \"#4682B4\",\n  darkslateblue: \"#483D8B\",\n  mediumturquoise: \"#48D1CC\",\n  indigo: \"#4B0082\",\n  darkolivegreen: \"#556B2F\",\n  cadetblue: \"#5F9EA0\",\n  cornflowerblue: \"#6495ED\",\n  mediumaquamarine: \"#66CDAA\",\n  dimgray: \"#696969\",\n  slateblue: \"#6A5ACD\",\n  olivedrab: \"#6B8E23\",\n  slategray: \"#708090\",\n  lightslategray: \"#778899\",\n  mediumslateblue: \"#7B68EE\",\n  lawngreen: \"#7CFC00\",\n  chartreuse: \"#7FFF00\",\n  aquamarine: \"#7FFFD4\",\n  maroon: \"#800000\",\n  purple: \"#800080\",\n  olive: \"#808000\",\n  gray: \"#808080\",\n  skyblue: \"#87CEEB\",\n  lightskyblue: \"#87CEFA\",\n  blueviolet: \"#8A2BE2\",\n  darkred: \"#8B0000\",\n  darkmagenta: \"#8B008B\",\n  saddlebrown: \"#8B4513\",\n  darkseagreen: \"#8FBC8F\",\n  lightgreen: \"#90EE90\",\n  mediumpurple: \"#9370D8\",\n  darkviolet: \"#9400D3\",\n  palegreen: \"#98FB98\",\n  darkorchid: \"#9932CC\",\n  yellowgreen: \"#9ACD32\",\n  sienna: \"#A0522D\",\n  brown: \"#A52A2A\",\n  darkgray: \"#A9A9A9\",\n  lightblue: \"#ADD8E6\",\n  greenyellow: \"#ADFF2F\",\n  paleturquoise: \"#AFEEEE\",\n  lightsteelblue: \"#B0C4DE\",\n  powderblue: \"#B0E0E6\",\n  firebrick: \"#B22222\",\n  darkgoldenrod: \"#B8860B\",\n  mediumorchid: \"#BA55D3\",\n  rosybrown: \"#BC8F8F\",\n  darkkhaki: \"#BDB76B\",\n  silver: \"#C0C0C0\",\n  mediumvioletred: \"#C71585\",\n  indianred: \"#CD5C5C\",\n  peru: \"#CD853F\",\n  chocolate: \"#D2691E\",\n  tan: \"#D2B48C\",\n  lightgrey: \"#D3D3D3\",\n  palevioletred: \"#D87093\",\n  thistle: \"#D8BFD8\",\n  orchid: \"#DA70D6\",\n  goldenrod: \"#DAA520\",\n  crimson: \"#DC143C\",\n  gainsboro: \"#DCDCDC\",\n  plum: \"#DDA0DD\",\n  burlywood: \"#DEB887\",\n  lightcyan: \"#E0FFFF\",\n  lavender: \"#E6E6FA\",\n  darksalmon: \"#E9967A\",\n  violet: \"#EE82EE\",\n  palegoldenrod: \"#EEE8AA\",\n  lightcoral: \"#F08080\",\n  khaki: \"#F0E68C\",\n  aliceblue: \"#F0F8FF\",\n  honeydew: \"#F0FFF0\",\n  azure: \"#F0FFFF\",\n  sandybrown: \"#F4A460\",\n  wheat: \"#F5DEB3\",\n  beige: \"#F5F5DC\",\n  whitesmoke: \"#F5F5F5\",\n  mintcream: \"#F5FFFA\",\n  ghostwhite: \"#F8F8FF\",\n  salmon: \"#FA8072\",\n  antiquewhite: \"#FAEBD7\",\n  linen: \"#FAF0E6\",\n  lightgoldenrodyellow: \"#FAFAD2\",\n  oldlace: \"#FDF5E6\",\n  red: \"#FF0000\",\n  fuchsia: \"#FF00FF\",\n  magenta: \"#FF00FF\",\n  deeppink: \"#FF1493\",\n  orangered: \"#FF4500\",\n  tomato: \"#FF6347\",\n  hotpink: \"#FF69B4\",\n  coral: \"#FF7F50\",\n  darkorange: \"#FF8C00\",\n  lightsalmon: \"#FFA07A\",\n  orange: \"#FFA500\",\n  lightpink: \"#FFB6C1\",\n  pink: \"#FFC0CB\",\n  gold: \"#FFD700\",\n  peachpuff: \"#FFDAB9\",\n  navajowhite: \"#FFDEAD\",\n  moccasin: \"#FFE4B5\",\n  bisque: \"#FFE4C4\",\n  mistyrose: \"#FFE4E1\",\n  blanchedalmond: \"#FFEBCD\",\n  papayawhip: \"#FFEFD5\",\n  lavenderblush: \"#FFF0F5\",\n  seashell: \"#FFF5EE\",\n  cornsilk: \"#FFF8DC\",\n  lemonchiffon: \"#FFFACD\",\n  floralwhite: \"#FFFAF0\",\n  snow: \"#FFFAFA\",\n  yellow: \"#FFFF00\",\n  lightyellow: \"#FFFFE0\",\n  ivory: \"#FFFFF0\",\n  white: \"#FFFFFF\",\n};\n\n/**\n * @param {number} [pixelRatio=1]\n */\nexport class ColorPicker {\n  /**\n   * @param {number} [pixelRatio]\n   */\n  constructor(pixelRatio = 1) {\n    this.pixelRatio = pixelRatio;\n    this.generated = false;\n    this.centerCoordinates = { x: 289 / 2, y: 289 / 2 };\n    this.r = 289 * 0.49;\n    this.color = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.hueCircle = undefined;\n    this.initialColor = { r: 255, g: 255, b: 255, a: 1.0 };\n    this.previousColor = undefined;\n    this.applied = false;\n\n    // bound by\n    this.updateCallback = () => {};\n    this.closeCallback = () => {};\n\n    // create all DOM elements\n    this._create();\n  }\n\n  /**\n   * this inserts the colorPicker into a div from the DOM\n   * @param {Element} container\n   */\n  insertTo(container) {\n    if (this.hammer !== undefined) {\n      this.hammer.destroy();\n      this.hammer = undefined;\n    }\n    this.container = container;\n    this.container.appendChild(this.frame);\n    this._bindHammer();\n\n    this._setSize();\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   * @param {Function} callback\n   */\n  setUpdateCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.updateCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker update callback is not a function.\",\n      );\n    }\n  }\n\n  /**\n   * the callback is executed on apply and save. Bind it to the application\n   * @param {Function} callback\n   */\n  setCloseCallback(callback) {\n    if (typeof callback === \"function\") {\n      this.closeCallback = callback;\n    } else {\n      throw new Error(\n        \"Function attempted to set as colorPicker closing callback is not a function.\",\n      );\n    }\n  }\n\n  /**\n   *\n   * @param {string} color\n   * @returns {string}\n   * @private\n   */\n  _isColorString(color) {\n    if (typeof color === \"string\") {\n      return htmlColors[color];\n    }\n  }\n\n  /**\n   * Set the color of the colorPicker\n   * Supported formats:\n   * 'red'                   --> HTML color string\n   * '#ffffff'               --> hex string\n   * 'rgb(255,255,255)'      --> rgb string\n   * 'rgba(255,255,255,1.0)' --> rgba string\n   * {r:255,g:255,b:255}     --> rgb object\n   * {r:255,g:255,b:255,a:1.0} --> rgba object\n   * @param {string | object} color\n   * @param {boolean} [setInitial]\n   */\n  setColor(color, setInitial = true) {\n    if (color === \"none\") {\n      return;\n    }\n\n    let rgba;\n\n    // if a html color shorthand is used, convert to hex\n    const htmlColor = this._isColorString(color);\n    if (htmlColor !== undefined) {\n      color = htmlColor;\n    }\n\n    // check format\n    if (isString(color) === true) {\n      if (isValidRGB(color) === true) {\n        const rgbaArray = color\n          .substr(4)\n          .substr(0, color.length - 5)\n          .split(\",\");\n        rgba = { r: rgbaArray[0], g: rgbaArray[1], b: rgbaArray[2], a: 1.0 };\n      } else if (isValidRGBA(color) === true) {\n        const rgbaArray = color\n          .substr(5)\n          .substr(0, color.length - 6)\n          .split(\",\");\n        rgba = {\n          r: rgbaArray[0],\n          g: rgbaArray[1],\n          b: rgbaArray[2],\n          a: rgbaArray[3],\n        };\n      } else if (isValidHex(color) === true) {\n        const rgbObj = hexToRGB(color);\n        rgba = { r: rgbObj.r, g: rgbObj.g, b: rgbObj.b, a: 1.0 };\n      }\n    } else {\n      if (color instanceof Object) {\n        if (\n          color.r !== undefined &&\n          color.g !== undefined &&\n          color.b !== undefined\n        ) {\n          const alpha = color.a !== undefined ? color.a : \"1.0\";\n          rgba = { r: color.r, g: color.g, b: color.b, a: alpha };\n        }\n      }\n    }\n\n    // set color\n    if (rgba === undefined) {\n      throw new Error(\n        \"Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: \" +\n          JSON.stringify(color),\n      );\n    } else {\n      this._setColor(rgba, setInitial);\n    }\n  }\n\n  /**\n   * this shows the color picker.\n   * The hue circle is constructed once and stored.\n   */\n  show() {\n    if (this.closeCallback !== undefined) {\n      this.closeCallback();\n      this.closeCallback = undefined;\n    }\n\n    this.applied = false;\n    this.frame.style.display = \"block\";\n    this._generateHueCircle();\n  }\n\n  // ------------------------------------------ PRIVATE ----------------------------- //\n\n  /**\n   * Hide the picker. Is called by the cancel button.\n   * Optional boolean to store the previous color for easy access later on.\n   * @param {boolean} [storePrevious]\n   * @private\n   */\n  _hide(storePrevious = true) {\n    // store the previous color for next time;\n    if (storePrevious === true) {\n      this.previousColor = Object.assign({}, this.color);\n    }\n\n    if (this.applied === true) {\n      this.updateCallback(this.initialColor);\n    }\n\n    this.frame.style.display = \"none\";\n\n    // call the closing callback, restoring the onclick method.\n    // this is in a setTimeout because it will trigger the show again before the click is done.\n    setTimeout(() => {\n      if (this.closeCallback !== undefined) {\n        this.closeCallback();\n        this.closeCallback = undefined;\n      }\n    }, 0);\n  }\n\n  /**\n   * bound to the save button. Saves and hides.\n   * @private\n   */\n  _save() {\n    this.updateCallback(this.color);\n    this.applied = false;\n    this._hide();\n  }\n\n  /**\n   * Bound to apply button. Saves but does not close. Is undone by the cancel button.\n   * @private\n   */\n  _apply() {\n    this.applied = true;\n    this.updateCallback(this.color);\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * load the color from the previous session.\n   * @private\n   */\n  _loadLast() {\n    if (this.previousColor !== undefined) {\n      this.setColor(this.previousColor, false);\n    } else {\n      alert(\"There is no last color to load...\");\n    }\n  }\n\n  /**\n   * set the color, place the picker\n   * @param {object} rgba\n   * @param {boolean} [setInitial]\n   * @private\n   */\n  _setColor(rgba, setInitial = true) {\n    // store the initial color\n    if (setInitial === true) {\n      this.initialColor = Object.assign({}, rgba);\n    }\n\n    this.color = rgba;\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n\n    const angleConvert = 2 * Math.PI;\n    const radius = this.r * hsv.s;\n    const x =\n      this.centerCoordinates.x + radius * Math.sin(angleConvert * hsv.h);\n    const y =\n      this.centerCoordinates.y + radius * Math.cos(angleConvert * hsv.h);\n\n    this.colorPickerSelector.style.left =\n      x - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n    this.colorPickerSelector.style.top =\n      y - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n\n    this._updatePicker(rgba);\n  }\n\n  /**\n   * bound to opacity control\n   * @param {number} value\n   * @private\n   */\n  _setOpacity(value) {\n    this.color.a = value / 100;\n    this._updatePicker(this.color);\n  }\n\n  /**\n   * bound to brightness control\n   * @param {number} value\n   * @private\n   */\n  _setBrightness(value) {\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.v = value / 100;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n    this._updatePicker();\n  }\n\n  /**\n   * update the color picker. A black circle overlays the hue circle to mimic the brightness decreasing.\n   * @param {object} rgba\n   * @private\n   */\n  _updatePicker(rgba = this.color) {\n    const hsv = RGBToHSV(rgba.r, rgba.g, rgba.b);\n    const ctx = this.colorPickerCanvas.getContext(\"2d\");\n    if (this.pixelRation === undefined) {\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n    }\n    ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n    // clear the canvas\n    const w = this.colorPickerCanvas.clientWidth;\n    const h = this.colorPickerCanvas.clientHeight;\n    ctx.clearRect(0, 0, w, h);\n\n    ctx.putImageData(this.hueCircle, 0, 0);\n    ctx.fillStyle = \"rgba(0,0,0,\" + (1 - hsv.v) + \")\";\n    ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n    ctx.fill();\n\n    this.brightnessRange.value = 100 * hsv.v;\n    this.opacityRange.value = 100 * rgba.a;\n\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n\n  /**\n   * used by create to set the size of the canvas.\n   * @private\n   */\n  _setSize() {\n    this.colorPickerCanvas.style.width = \"100%\";\n    this.colorPickerCanvas.style.height = \"100%\";\n\n    this.colorPickerCanvas.width = 289 * this.pixelRatio;\n    this.colorPickerCanvas.height = 289 * this.pixelRatio;\n  }\n\n  /**\n   * create all dom elements\n   * TODO: cleanup, lots of similar dom elements\n   * @private\n   */\n  _create() {\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-color-picker\";\n\n    this.colorPickerDiv = document.createElement(\"div\");\n    this.colorPickerSelector = document.createElement(\"div\");\n    this.colorPickerSelector.className = \"vis-selector\";\n    this.colorPickerDiv.appendChild(this.colorPickerSelector);\n\n    this.colorPickerCanvas = document.createElement(\"canvas\");\n    this.colorPickerDiv.appendChild(this.colorPickerCanvas);\n\n    if (!this.colorPickerCanvas.getContext) {\n      const noCanvas = document.createElement(\"DIV\");\n      noCanvas.style.color = \"red\";\n      noCanvas.style.fontWeight = \"bold\";\n      noCanvas.style.padding = \"10px\";\n      noCanvas.innerText = \"Error: your browser does not support HTML canvas\";\n      this.colorPickerCanvas.appendChild(noCanvas);\n    } else {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      this.pixelRatio =\n        (window.devicePixelRatio || 1) /\n        (ctx.webkitBackingStorePixelRatio ||\n          ctx.mozBackingStorePixelRatio ||\n          ctx.msBackingStorePixelRatio ||\n          ctx.oBackingStorePixelRatio ||\n          ctx.backingStorePixelRatio ||\n          1);\n      this.colorPickerCanvas\n        .getContext(\"2d\")\n        .setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n    }\n\n    this.colorPickerDiv.className = \"vis-color\";\n\n    this.opacityDiv = document.createElement(\"div\");\n    this.opacityDiv.className = \"vis-opacity\";\n\n    this.brightnessDiv = document.createElement(\"div\");\n    this.brightnessDiv.className = \"vis-brightness\";\n\n    this.arrowDiv = document.createElement(\"div\");\n    this.arrowDiv.className = \"vis-arrow\";\n\n    this.opacityRange = document.createElement(\"input\");\n    try {\n      this.opacityRange.type = \"range\"; // Not supported on IE9\n      this.opacityRange.min = \"0\";\n      this.opacityRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.opacityRange.value = \"100\";\n    this.opacityRange.className = \"vis-range\";\n\n    this.brightnessRange = document.createElement(\"input\");\n    try {\n      this.brightnessRange.type = \"range\"; // Not supported on IE9\n      this.brightnessRange.min = \"0\";\n      this.brightnessRange.max = \"100\";\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    this.brightnessRange.value = \"100\";\n    this.brightnessRange.className = \"vis-range\";\n\n    this.opacityDiv.appendChild(this.opacityRange);\n    this.brightnessDiv.appendChild(this.brightnessRange);\n\n    const me = this;\n    this.opacityRange.onchange = function () {\n      me._setOpacity(this.value);\n    };\n    this.opacityRange.oninput = function () {\n      me._setOpacity(this.value);\n    };\n    this.brightnessRange.onchange = function () {\n      me._setBrightness(this.value);\n    };\n    this.brightnessRange.oninput = function () {\n      me._setBrightness(this.value);\n    };\n\n    this.brightnessLabel = document.createElement(\"div\");\n    this.brightnessLabel.className = \"vis-label vis-brightness\";\n    this.brightnessLabel.innerText = \"brightness:\";\n\n    this.opacityLabel = document.createElement(\"div\");\n    this.opacityLabel.className = \"vis-label vis-opacity\";\n    this.opacityLabel.innerText = \"opacity:\";\n\n    this.newColorDiv = document.createElement(\"div\");\n    this.newColorDiv.className = \"vis-new-color\";\n    this.newColorDiv.innerText = \"new\";\n\n    this.initialColorDiv = document.createElement(\"div\");\n    this.initialColorDiv.className = \"vis-initial-color\";\n    this.initialColorDiv.innerText = \"initial\";\n\n    this.cancelButton = document.createElement(\"div\");\n    this.cancelButton.className = \"vis-button vis-cancel\";\n    this.cancelButton.innerText = \"cancel\";\n    this.cancelButton.onclick = this._hide.bind(this, false);\n\n    this.applyButton = document.createElement(\"div\");\n    this.applyButton.className = \"vis-button vis-apply\";\n    this.applyButton.innerText = \"apply\";\n    this.applyButton.onclick = this._apply.bind(this);\n\n    this.saveButton = document.createElement(\"div\");\n    this.saveButton.className = \"vis-button vis-save\";\n    this.saveButton.innerText = \"save\";\n    this.saveButton.onclick = this._save.bind(this);\n\n    this.loadButton = document.createElement(\"div\");\n    this.loadButton.className = \"vis-button vis-load\";\n    this.loadButton.innerText = \"load last\";\n    this.loadButton.onclick = this._loadLast.bind(this);\n\n    this.frame.appendChild(this.colorPickerDiv);\n    this.frame.appendChild(this.arrowDiv);\n    this.frame.appendChild(this.brightnessLabel);\n    this.frame.appendChild(this.brightnessDiv);\n    this.frame.appendChild(this.opacityLabel);\n    this.frame.appendChild(this.opacityDiv);\n    this.frame.appendChild(this.newColorDiv);\n    this.frame.appendChild(this.initialColorDiv);\n\n    this.frame.appendChild(this.cancelButton);\n    this.frame.appendChild(this.applyButton);\n    this.frame.appendChild(this.saveButton);\n    this.frame.appendChild(this.loadButton);\n  }\n\n  /**\n   * bind hammer to the color picker\n   * @private\n   */\n  _bindHammer() {\n    this.drag = {};\n    this.pinch = {};\n    this.hammer = new Hammer(this.colorPickerCanvas);\n    this.hammer.get(\"pinch\").set({ enable: true });\n\n    this.hammer.on(\"hammer.input\", (event) => {\n      if (event.isFirst) {\n        this._moveSelector(event);\n      }\n    });\n    this.hammer.on(\"tap\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panstart\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panmove\", (event) => {\n      this._moveSelector(event);\n    });\n    this.hammer.on(\"panend\", (event) => {\n      this._moveSelector(event);\n    });\n  }\n\n  /**\n   * generate the hue circle. This is relatively heavy (200ms) and is done only once on the first time it is shown.\n   * @private\n   */\n  _generateHueCircle() {\n    if (this.generated === false) {\n      const ctx = this.colorPickerCanvas.getContext(\"2d\");\n      if (this.pixelRation === undefined) {\n        this.pixelRatio =\n          (window.devicePixelRatio || 1) /\n          (ctx.webkitBackingStorePixelRatio ||\n            ctx.mozBackingStorePixelRatio ||\n            ctx.msBackingStorePixelRatio ||\n            ctx.oBackingStorePixelRatio ||\n            ctx.backingStorePixelRatio ||\n            1);\n      }\n      ctx.setTransform(this.pixelRatio, 0, 0, this.pixelRatio, 0, 0);\n\n      // clear the canvas\n      const w = this.colorPickerCanvas.clientWidth;\n      const h = this.colorPickerCanvas.clientHeight;\n      ctx.clearRect(0, 0, w, h);\n\n      // draw hue circle\n      let x, y, hue, sat;\n      this.centerCoordinates = { x: w * 0.5, y: h * 0.5 };\n      this.r = 0.49 * w;\n      const angleConvert = (2 * Math.PI) / 360;\n      const hfac = 1 / 360;\n      const sfac = 1 / this.r;\n      let rgb;\n      for (hue = 0; hue < 360; hue++) {\n        for (sat = 0; sat < this.r; sat++) {\n          x = this.centerCoordinates.x + sat * Math.sin(angleConvert * hue);\n          y = this.centerCoordinates.y + sat * Math.cos(angleConvert * hue);\n          rgb = HSVToRGB(hue * hfac, sat * sfac, 1);\n          ctx.fillStyle = \"rgb(\" + rgb.r + \",\" + rgb.g + \",\" + rgb.b + \")\";\n          ctx.fillRect(x - 0.5, y - 0.5, 2, 2);\n        }\n      }\n      ctx.strokeStyle = \"rgba(0,0,0,1)\";\n      ctx.circle(this.centerCoordinates.x, this.centerCoordinates.y, this.r);\n      ctx.stroke();\n\n      this.hueCircle = ctx.getImageData(0, 0, w, h);\n    }\n    this.generated = true;\n  }\n\n  /**\n   * move the selector. This is called by hammer functions.\n   * @param {Event}  event   The event\n   * @private\n   */\n  _moveSelector(event) {\n    const rect = this.colorPickerDiv.getBoundingClientRect();\n    const left = event.center.x - rect.left;\n    const top = event.center.y - rect.top;\n\n    const centerY = 0.5 * this.colorPickerDiv.clientHeight;\n    const centerX = 0.5 * this.colorPickerDiv.clientWidth;\n\n    const x = left - centerX;\n    const y = top - centerY;\n\n    const angle = Math.atan2(x, y);\n    const radius = 0.98 * Math.min(Math.sqrt(x * x + y * y), centerX);\n\n    const newTop = Math.cos(angle) * radius + centerY;\n    const newLeft = Math.sin(angle) * radius + centerX;\n\n    this.colorPickerSelector.style.top =\n      newTop - 0.5 * this.colorPickerSelector.clientHeight + \"px\";\n    this.colorPickerSelector.style.left =\n      newLeft - 0.5 * this.colorPickerSelector.clientWidth + \"px\";\n\n    // set color\n    let h = angle / (2 * Math.PI);\n    h = h < 0 ? h + 1 : h;\n    const s = radius / this.r;\n    const hsv = RGBToHSV(this.color.r, this.color.g, this.color.b);\n    hsv.h = h;\n    hsv.s = s;\n    const rgba = HSVToRGB(hsv.h, hsv.s, hsv.v);\n    rgba[\"a\"] = this.color.a;\n    this.color = rgba;\n\n    // update previews\n    this.initialColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.initialColor.r +\n      \",\" +\n      this.initialColor.g +\n      \",\" +\n      this.initialColor.b +\n      \",\" +\n      this.initialColor.a +\n      \")\";\n    this.newColorDiv.style.backgroundColor =\n      \"rgba(\" +\n      this.color.r +\n      \",\" +\n      this.color.g +\n      \",\" +\n      this.color.b +\n      \",\" +\n      this.color.a +\n      \")\";\n  }\n}\n", "import { copyAndExtendArray } from \"../util.ts\";\n\nimport { ColorPicker } from \"./color-picker.js\";\n\n/**\n * Wrap given text (last argument) in HTML elements (all preceding arguments).\n * @param {...any} rest - List of tag names followed by inner text.\n * @returns An element or a text node.\n */\nfunction wrapInTag(...rest) {\n  if (rest.length < 1) {\n    throw new TypeError(\"Invalid arguments.\");\n  } else if (rest.length === 1) {\n    return document.createTextNode(rest[0]);\n  } else {\n    const element = document.createElement(rest[0]);\n    element.appendChild(wrapInTag(...rest.slice(1)));\n    return element;\n  }\n}\n\n/**\n * The way this works is for all properties of this.possible options, you can supply the property name in any form to list the options.\n * Boolean options are recognised as Boolean\n * Number options should be written as array: [default value, min value, max value, stepsize]\n * Colors should be written as array: ['color', '#ffffff']\n * Strings with should be written as array: [option1, option2, option3, ..]\n *\n * The options are matched with their counterparts in each of the modules and the values used in the configuration are\n */\nexport class Configurator {\n  /**\n   * @param {object} parentModule        | the location where parentModule.setOptions() can be called\n   * @param {object} defaultContainer    | the default container of the module\n   * @param {object} configureOptions    | the fully configured and predefined options set found in allOptions.js\n   * @param {number} pixelRatio          | canvas pixel ratio\n   * @param {Function} hideOption        | custom logic to dynamically hide options\n   */\n  constructor(\n    parentModule,\n    defaultContainer,\n    configureOptions,\n    pixelRatio = 1,\n    hideOption = () => false,\n  ) {\n    this.parent = parentModule;\n    this.changedOptions = [];\n    this.container = defaultContainer;\n    this.allowCreation = false;\n    this.hideOption = hideOption;\n\n    this.options = {};\n    this.initialized = false;\n    this.popupCounter = 0;\n    this.defaultOptions = {\n      enabled: false,\n      filter: true,\n      container: undefined,\n      showButton: true,\n    };\n    Object.assign(this.options, this.defaultOptions);\n\n    this.configureOptions = configureOptions;\n    this.moduleOptions = {};\n    this.domElements = [];\n    this.popupDiv = {};\n    this.popupLimit = 5;\n    this.popupHistory = {};\n    this.colorPicker = new ColorPicker(pixelRatio);\n    this.wrapper = undefined;\n  }\n\n  /**\n   * refresh all options.\n   * Because all modules parse their options by themselves, we just use their options. We copy them here.\n   * @param {object} options\n   */\n  setOptions(options) {\n    if (options !== undefined) {\n      // reset the popup history because the indices may have been changed.\n      this.popupHistory = {};\n      this._removePopup();\n\n      let enabled = true;\n      if (typeof options === \"string\") {\n        this.options.filter = options;\n      } else if (Array.isArray(options)) {\n        this.options.filter = options.join();\n      } else if (typeof options === \"object\") {\n        if (options == null) {\n          throw new TypeError(\"options cannot be null\");\n        }\n        if (options.container !== undefined) {\n          this.options.container = options.container;\n        }\n        if (options.filter !== undefined) {\n          this.options.filter = options.filter;\n        }\n        if (options.showButton !== undefined) {\n          this.options.showButton = options.showButton;\n        }\n        if (options.enabled !== undefined) {\n          enabled = options.enabled;\n        }\n      } else if (typeof options === \"boolean\") {\n        this.options.filter = true;\n        enabled = options;\n      } else if (typeof options === \"function\") {\n        this.options.filter = options;\n        enabled = true;\n      }\n      if (this.options.filter === false) {\n        enabled = false;\n      }\n\n      this.options.enabled = enabled;\n    }\n    this._clean();\n  }\n\n  /**\n   *\n   * @param {object} moduleOptions\n   */\n  setModuleOptions(moduleOptions) {\n    this.moduleOptions = moduleOptions;\n    if (this.options.enabled === true) {\n      this._clean();\n      if (this.options.container !== undefined) {\n        this.container = this.options.container;\n      }\n      this._create();\n    }\n  }\n\n  /**\n   * Create all DOM elements\n   * @private\n   */\n  _create() {\n    this._clean();\n    this.changedOptions = [];\n\n    const filter = this.options.filter;\n    let counter = 0;\n    let show = false;\n    for (const option in this.configureOptions) {\n      if (Object.prototype.hasOwnProperty.call(this.configureOptions, option)) {\n        this.allowCreation = false;\n        show = false;\n        if (typeof filter === \"function\") {\n          show = filter(option, []);\n          show =\n            show ||\n            this._handleObject(this.configureOptions[option], [option], true);\n        } else if (filter === true || filter.indexOf(option) !== -1) {\n          show = true;\n        }\n\n        if (show !== false) {\n          this.allowCreation = true;\n\n          // linebreak between categories\n          if (counter > 0) {\n            this._makeItem([]);\n          }\n          // a header for the category\n          this._makeHeader(option);\n\n          // get the sub options\n          this._handleObject(this.configureOptions[option], [option]);\n        }\n        counter++;\n      }\n    }\n    this._makeButton();\n    this._push();\n    //~ this.colorPicker.insertTo(this.container);\n  }\n\n  /**\n   * draw all DOM elements on the screen\n   * @private\n   */\n  _push() {\n    this.wrapper = document.createElement(\"div\");\n    this.wrapper.className = \"vis-configuration-wrapper\";\n    this.container.appendChild(this.wrapper);\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.appendChild(this.domElements[i]);\n    }\n\n    this._showPopupIfNeeded();\n  }\n\n  /**\n   * delete all DOM elements\n   * @private\n   */\n  _clean() {\n    for (let i = 0; i < this.domElements.length; i++) {\n      this.wrapper.removeChild(this.domElements[i]);\n    }\n\n    if (this.wrapper !== undefined) {\n      this.container.removeChild(this.wrapper);\n      this.wrapper = undefined;\n    }\n    this.domElements = [];\n\n    this._removePopup();\n  }\n\n  /**\n   * get the value from the actualOptions if it exists\n   * @param {Array} path    | where to look for the actual option\n   * @returns {*}\n   * @private\n   */\n  _getValue(path) {\n    let base = this.moduleOptions;\n    for (let i = 0; i < path.length; i++) {\n      if (base[path[i]] !== undefined) {\n        base = base[path[i]];\n      } else {\n        base = undefined;\n        break;\n      }\n    }\n    return base;\n  }\n\n  /**\n   * all option elements are wrapped in an item\n   * @param {Array} path    | where to look for the actual option\n   * @param {Array.<Element>} domElements\n   * @returns {number}\n   * @private\n   */\n  _makeItem(path, ...domElements) {\n    if (this.allowCreation === true) {\n      const item = document.createElement(\"div\");\n      item.className =\n        \"vis-configuration vis-config-item vis-config-s\" + path.length;\n      domElements.forEach((element) => {\n        item.appendChild(element);\n      });\n      this.domElements.push(item);\n      return this.domElements.length;\n    }\n    return 0;\n  }\n\n  /**\n   * header for major subjects\n   * @param {string} name\n   * @private\n   */\n  _makeHeader(name) {\n    const div = document.createElement(\"div\");\n    div.className = \"vis-configuration vis-config-header\";\n    div.innerText = name;\n    this._makeItem([], div);\n  }\n\n  /**\n   * make a label, if it is an object label, it gets different styling.\n   * @param {string} name\n   * @param {Array} path    | where to look for the actual option\n   * @param {string} objectLabel\n   * @returns {HTMLElement}\n   * @private\n   */\n  _makeLabel(name, path, objectLabel = false) {\n    const div = document.createElement(\"div\");\n    div.className =\n      \"vis-configuration vis-config-label vis-config-s\" + path.length;\n    if (objectLabel === true) {\n      while (div.firstChild) {\n        div.removeChild(div.firstChild);\n      }\n      div.appendChild(wrapInTag(\"i\", \"b\", name));\n    } else {\n      div.innerText = name + \":\";\n    }\n    return div;\n  }\n\n  /**\n   * make a dropdown list for multiple possible string optoins\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeDropdown(arr, value, path) {\n    const select = document.createElement(\"select\");\n    select.className = \"vis-configuration vis-config-select\";\n    let selectedValue = 0;\n    if (value !== undefined) {\n      if (arr.indexOf(value) !== -1) {\n        selectedValue = arr.indexOf(value);\n      }\n    }\n\n    for (let i = 0; i < arr.length; i++) {\n      const option = document.createElement(\"option\");\n      option.value = arr[i];\n      if (i === selectedValue) {\n        option.selected = \"selected\";\n      }\n      option.innerText = arr[i];\n      select.appendChild(option);\n    }\n\n    const me = this;\n    select.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, select);\n  }\n\n  /**\n   * make a range object for numeric options\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeRange(arr, value, path) {\n    const defaultValue = arr[0];\n    const min = arr[1];\n    const max = arr[2];\n    const step = arr[3];\n    const range = document.createElement(\"input\");\n    range.className = \"vis-configuration vis-config-range\";\n    try {\n      range.type = \"range\"; // not supported on IE9\n      range.min = min;\n      range.max = max;\n    } catch (err) {\n      // TODO: Add some error handling.\n    }\n    range.step = step;\n\n    // set up the popup settings in case they are needed.\n    let popupString = \"\";\n    let popupValue = 0;\n\n    if (value !== undefined) {\n      const factor = 1.2;\n      if (value < 0 && value * factor < min) {\n        range.min = Math.ceil(value * factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      } else if (value / factor < min) {\n        range.min = Math.ceil(value / factor);\n        popupValue = range.min;\n        popupString = \"range increased\";\n      }\n      if (value * factor > max && max !== 1) {\n        range.max = Math.ceil(value * factor);\n        popupValue = range.max;\n        popupString = \"range increased\";\n      }\n      range.value = value;\n    } else {\n      range.value = defaultValue;\n    }\n\n    const input = document.createElement(\"input\");\n    input.className = \"vis-configuration vis-config-rangeinput\";\n    input.value = range.value;\n\n    const me = this;\n    range.onchange = function () {\n      input.value = this.value;\n      me._update(Number(this.value), path);\n    };\n    range.oninput = function () {\n      input.value = this.value;\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    const itemIndex = this._makeItem(path, label, range, input);\n\n    // if a popup is needed AND it has not been shown for this value, show it.\n    if (popupString !== \"\" && this.popupHistory[itemIndex] !== popupValue) {\n      this.popupHistory[itemIndex] = popupValue;\n      this._setupPopup(popupString, itemIndex);\n    }\n  }\n\n  /**\n   * make a button object\n   * @private\n   */\n  _makeButton() {\n    if (this.options.showButton === true) {\n      const generateButton = document.createElement(\"div\");\n      generateButton.className = \"vis-configuration vis-config-button\";\n      generateButton.innerText = \"generate options\";\n      generateButton.onclick = () => {\n        this._printOptions();\n      };\n      generateButton.onmouseover = () => {\n        generateButton.className = \"vis-configuration vis-config-button hover\";\n      };\n      generateButton.onmouseout = () => {\n        generateButton.className = \"vis-configuration vis-config-button\";\n      };\n\n      this.optionsContainer = document.createElement(\"div\");\n      this.optionsContainer.className =\n        \"vis-configuration vis-config-option-container\";\n\n      this.domElements.push(this.optionsContainer);\n      this.domElements.push(generateButton);\n    }\n  }\n\n  /**\n   * prepare the popup\n   * @param {string} string\n   * @param {number} index\n   * @private\n   */\n  _setupPopup(string, index) {\n    if (\n      this.initialized === true &&\n      this.allowCreation === true &&\n      this.popupCounter < this.popupLimit\n    ) {\n      const div = document.createElement(\"div\");\n      div.id = \"vis-configuration-popup\";\n      div.className = \"vis-configuration-popup\";\n      div.innerText = string;\n      div.onclick = () => {\n        this._removePopup();\n      };\n      this.popupCounter += 1;\n      this.popupDiv = { html: div, index: index };\n    }\n  }\n\n  /**\n   * remove the popup from the dom\n   * @private\n   */\n  _removePopup() {\n    if (this.popupDiv.html !== undefined) {\n      this.popupDiv.html.parentNode.removeChild(this.popupDiv.html);\n      clearTimeout(this.popupDiv.hideTimeout);\n      clearTimeout(this.popupDiv.deleteTimeout);\n      this.popupDiv = {};\n    }\n  }\n\n  /**\n   * Show the popup if it is needed.\n   * @private\n   */\n  _showPopupIfNeeded() {\n    if (this.popupDiv.html !== undefined) {\n      const correspondingElement = this.domElements[this.popupDiv.index];\n      const rect = correspondingElement.getBoundingClientRect();\n      this.popupDiv.html.style.left = rect.left + \"px\";\n      this.popupDiv.html.style.top = rect.top - 30 + \"px\"; // 30 is the height;\n      document.body.appendChild(this.popupDiv.html);\n      this.popupDiv.hideTimeout = setTimeout(() => {\n        this.popupDiv.html.style.opacity = 0;\n      }, 1500);\n      this.popupDiv.deleteTimeout = setTimeout(() => {\n        this._removePopup();\n      }, 1800);\n    }\n  }\n\n  /**\n   * make a checkbox for boolean options.\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeCheckbox(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"checkbox\";\n    checkbox.className = \"vis-configuration vis-config-checkbox\";\n    checkbox.checked = defaultValue;\n    if (value !== undefined) {\n      checkbox.checked = value;\n      if (value !== defaultValue) {\n        if (typeof defaultValue === \"object\") {\n          if (value !== defaultValue.enabled) {\n            this.changedOptions.push({ path: path, value: value });\n          }\n        } else {\n          this.changedOptions.push({ path: path, value: value });\n        }\n      }\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.checked, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a text input field for string options.\n   * @param {number} defaultValue\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeTextInput(defaultValue, value, path) {\n    const checkbox = document.createElement(\"input\");\n    checkbox.type = \"text\";\n    checkbox.className = \"vis-configuration vis-config-text\";\n    checkbox.value = value;\n    if (value !== defaultValue) {\n      this.changedOptions.push({ path: path, value: value });\n    }\n\n    const me = this;\n    checkbox.onchange = function () {\n      me._update(this.value, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, checkbox);\n  }\n\n  /**\n   * make a color field with a color picker for color fields\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _makeColorField(arr, value, path) {\n    const defaultColor = arr[1];\n    const div = document.createElement(\"div\");\n    value = value === undefined ? defaultColor : value;\n\n    if (value !== \"none\") {\n      div.className = \"vis-configuration vis-config-colorBlock\";\n      div.style.backgroundColor = value;\n    } else {\n      div.className = \"vis-configuration vis-config-colorBlock none\";\n    }\n\n    value = value === undefined ? defaultColor : value;\n    div.onclick = () => {\n      this._showColorPicker(value, div, path);\n    };\n\n    const label = this._makeLabel(path[path.length - 1], path);\n    this._makeItem(path, label, div);\n  }\n\n  /**\n   * used by the color buttons to call the color picker.\n   * @param {number} value\n   * @param {HTMLElement} div\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _showColorPicker(value, div, path) {\n    // clear the callback from this div\n    div.onclick = function () {};\n\n    this.colorPicker.insertTo(div);\n    this.colorPicker.show();\n\n    this.colorPicker.setColor(value);\n    this.colorPicker.setUpdateCallback((color) => {\n      const colorString =\n        \"rgba(\" + color.r + \",\" + color.g + \",\" + color.b + \",\" + color.a + \")\";\n      div.style.backgroundColor = colorString;\n      this._update(colorString, path);\n    });\n\n    // on close of the colorpicker, restore the callback.\n    this.colorPicker.setCloseCallback(() => {\n      div.onclick = () => {\n        this._showColorPicker(value, div, path);\n      };\n    });\n  }\n\n  /**\n   * parse an object and draw the correct items\n   * @param {object} obj\n   * @param {Array} [path]    | where to look for the actual option\n   * @param {boolean} [checkOnly]\n   * @returns {boolean}\n   * @private\n   */\n  _handleObject(obj, path = [], checkOnly = false) {\n    let show = false;\n    const filter = this.options.filter;\n    let visibleInSet = false;\n    for (const subObj in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, subObj)) {\n        show = true;\n        const item = obj[subObj];\n        const newPath = copyAndExtendArray(path, subObj);\n        if (typeof filter === \"function\") {\n          show = filter(subObj, path);\n\n          // if needed we must go deeper into the object.\n          if (show === false) {\n            if (\n              !Array.isArray(item) &&\n              typeof item !== \"string\" &&\n              typeof item !== \"boolean\" &&\n              item instanceof Object\n            ) {\n              this.allowCreation = false;\n              show = this._handleObject(item, newPath, true);\n              this.allowCreation = checkOnly === false;\n            }\n          }\n        }\n\n        if (show !== false) {\n          visibleInSet = true;\n          const value = this._getValue(newPath);\n\n          if (Array.isArray(item)) {\n            this._handleArray(item, value, newPath);\n          } else if (typeof item === \"string\") {\n            this._makeTextInput(item, value, newPath);\n          } else if (typeof item === \"boolean\") {\n            this._makeCheckbox(item, value, newPath);\n          } else if (item instanceof Object) {\n            // skip the options that are not enabled\n            if (!this.hideOption(path, subObj, this.moduleOptions)) {\n              // initially collapse options with an disabled enabled option.\n              if (item.enabled !== undefined) {\n                const enabledPath = copyAndExtendArray(newPath, \"enabled\");\n                const enabledValue = this._getValue(enabledPath);\n                if (enabledValue === true) {\n                  const label = this._makeLabel(subObj, newPath, true);\n                  this._makeItem(newPath, label);\n                  visibleInSet =\n                    this._handleObject(item, newPath) || visibleInSet;\n                } else {\n                  this._makeCheckbox(item, enabledValue, newPath);\n                }\n              } else {\n                const label = this._makeLabel(subObj, newPath, true);\n                this._makeItem(newPath, label);\n                visibleInSet =\n                  this._handleObject(item, newPath) || visibleInSet;\n              }\n            }\n          } else {\n            console.error(\"dont know how to handle\", item, subObj, newPath);\n          }\n        }\n      }\n    }\n    return visibleInSet;\n  }\n\n  /**\n   * handle the array type of option\n   * @param {Array.<number>} arr\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _handleArray(arr, value, path) {\n    if (typeof arr[0] === \"string\" && arr[0] === \"color\") {\n      this._makeColorField(arr, value, path);\n      if (arr[1] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"string\") {\n      this._makeDropdown(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: value });\n      }\n    } else if (typeof arr[0] === \"number\") {\n      this._makeRange(arr, value, path);\n      if (arr[0] !== value) {\n        this.changedOptions.push({ path: path, value: Number(value) });\n      }\n    }\n  }\n\n  /**\n   * called to update the network with the new settings.\n   * @param {number} value\n   * @param {Array} path    | where to look for the actual option\n   * @private\n   */\n  _update(value, path) {\n    const options = this._constructOptions(value, path);\n\n    if (\n      this.parent.body &&\n      this.parent.body.emitter &&\n      this.parent.body.emitter.emit\n    ) {\n      this.parent.body.emitter.emit(\"configChange\", options);\n    }\n    this.initialized = true;\n    this.parent.setOptions(options);\n  }\n\n  /**\n   *\n   * @param {string | boolean} value\n   * @param {Array.<string>} path\n   * @param {{}} optionsObj\n   * @returns {{}}\n   * @private\n   */\n  _constructOptions(value, path, optionsObj = {}) {\n    let pointer = optionsObj;\n\n    // when dropdown boxes can be string or boolean, we typecast it into correct types\n    value = value === \"true\" ? true : value;\n    value = value === \"false\" ? false : value;\n\n    for (let i = 0; i < path.length; i++) {\n      if (path[i] !== \"global\") {\n        if (pointer[path[i]] === undefined) {\n          pointer[path[i]] = {};\n        }\n        if (i !== path.length - 1) {\n          pointer = pointer[path[i]];\n        } else {\n          pointer[path[i]] = value;\n        }\n      }\n    }\n    return optionsObj;\n  }\n\n  /**\n   * @private\n   */\n  _printOptions() {\n    const options = this.getOptions();\n\n    while (this.optionsContainer.firstChild) {\n      this.optionsContainer.removeChild(this.optionsContainer.firstChild);\n    }\n    this.optionsContainer.appendChild(\n      wrapInTag(\"pre\", \"const options = \" + JSON.stringify(options, null, 2)),\n    );\n  }\n\n  /**\n   *\n   * @returns {{}} options\n   */\n  getOptions() {\n    const options = {};\n    for (let i = 0; i < this.changedOptions.length; i++) {\n      this._constructOptions(\n        this.changedOptions[i].value,\n        this.changedOptions[i].path,\n        options,\n      );\n    }\n    return options;\n  }\n}\n", "/**\n * Popup is a class to create a popup window with some text\n */\nexport class Popup {\n  /**\n   * @param {Element} container       The container object.\n   * @param {string}  overflowMethod  How the popup should act to overflowing ('flip' or 'cap')\n   */\n  constructor(container, overflowMethod) {\n    this.container = container;\n    this.overflowMethod = overflowMethod || \"cap\";\n\n    this.x = 0;\n    this.y = 0;\n    this.padding = 5;\n    this.hidden = false;\n\n    // create the frame\n    this.frame = document.createElement(\"div\");\n    this.frame.className = \"vis-tooltip\";\n    this.container.appendChild(this.frame);\n  }\n\n  /**\n   * @param {number} x   Horizontal position of the popup window\n   * @param {number} y   Vertical position of the popup window\n   */\n  setPosition(x, y) {\n    this.x = parseInt(x);\n    this.y = parseInt(y);\n  }\n\n  /**\n   * Set the content for the popup window. This can be HTML code or text.\n   * @param {string | Element} content\n   */\n  setText(content) {\n    if (content instanceof Element) {\n      while (this.frame.firstChild) {\n        this.frame.removeChild(this.frame.firstChild);\n      }\n      this.frame.appendChild(content);\n    } else {\n      // String containing literal text, element has to be used for HTML due to\n      // XSS risks associated with innerHTML (i.e. prevent XSS by accident).\n      this.frame.innerText = content;\n    }\n  }\n\n  /**\n   * Show the popup window\n   * @param {boolean} [doShow]    Show or hide the window\n   */\n  show(doShow) {\n    if (doShow === undefined) {\n      doShow = true;\n    }\n\n    if (doShow === true) {\n      const height = this.frame.clientHeight;\n      const width = this.frame.clientWidth;\n      const maxHeight = this.frame.parentNode.clientHeight;\n      const maxWidth = this.frame.parentNode.clientWidth;\n\n      let left = 0,\n        top = 0;\n\n      if (this.overflowMethod == \"flip\") {\n        let isLeft = false,\n          isTop = true; // Where around the position it's located\n\n        if (this.y - height < this.padding) {\n          isTop = false;\n        }\n\n        if (this.x + width > maxWidth - this.padding) {\n          isLeft = true;\n        }\n\n        if (isLeft) {\n          left = this.x - width;\n        } else {\n          left = this.x;\n        }\n\n        if (isTop) {\n          top = this.y - height;\n        } else {\n          top = this.y;\n        }\n      } else {\n        top = this.y - height;\n        if (top + height + this.padding > maxHeight) {\n          top = maxHeight - height - this.padding;\n        }\n        if (top < this.padding) {\n          top = this.padding;\n        }\n\n        left = this.x;\n        if (left + width + this.padding > maxWidth) {\n          left = maxWidth - width - this.padding;\n        }\n        if (left < this.padding) {\n          left = this.padding;\n        }\n      }\n\n      this.frame.style.left = left + \"px\";\n      this.frame.style.top = top + \"px\";\n      this.frame.style.visibility = \"visible\";\n      this.hidden = false;\n    } else {\n      this.hide();\n    }\n  }\n\n  /**\n   * Hide the popup window\n   */\n  hide() {\n    this.hidden = true;\n    this.frame.style.left = \"0\";\n    this.frame.style.top = \"0\";\n    this.frame.style.visibility = \"hidden\";\n  }\n\n  /**\n   * Remove the popup window\n   */\n  destroy() {\n    this.frame.parentNode.removeChild(this.frame); // Remove element from DOM\n  }\n}\n", "import { copyAndExtendArray, copyArray } from \"../util.ts\";\n\nlet errorFound = false;\nlet allOptions;\n\nexport const VALIDATOR_PRINT_STYLE = \"background: #FFeeee; color: #dd0000\";\n\n/**\n *  Used to validate options.\n */\nexport class Validator {\n  /**\n   * Main function to be called\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {object} subObject\n   * @returns {boolean}\n   * @static\n   */\n  static validate(options, referenceOptions, subObject) {\n    errorFound = false;\n    allOptions = referenceOptions;\n    let usedOptions = referenceOptions;\n    if (subObject !== undefined) {\n      usedOptions = referenceOptions[subObject];\n    }\n    Validator.parse(options, usedOptions, []);\n    return errorFound;\n  }\n\n  /**\n   * Will traverse an object recursively and check every value\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static parse(options, referenceOptions, path) {\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option)) {\n        Validator.check(option, options, referenceOptions, path);\n      }\n    }\n  }\n\n  /**\n   * Check every value. If the value is an object, call the parse function on that object.\n   * @param {string} option\n   * @param {object} options\n   * @param {object} referenceOptions\n   * @param {Array} path    | where to look for the actual option\n   * @static\n   */\n  static check(option, options, referenceOptions, path) {\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ === undefined\n    ) {\n      Validator.getSuggestion(option, referenceOptions, path);\n      return;\n    }\n\n    let referenceOption = option;\n    let is_object = true;\n\n    if (\n      referenceOptions[option] === undefined &&\n      referenceOptions.__any__ !== undefined\n    ) {\n      // NOTE: This only triggers if the __any__ is in the top level of the options object.\n      //       THAT'S A REALLY BAD PLACE TO ALLOW IT!!!!\n      // TODO: Examine if needed, remove if possible\n\n      // __any__ is a wildcard. Any value is accepted and will be further analysed by reference.\n      referenceOption = \"__any__\";\n\n      // if the any-subgroup is not a predefined object in the configurator,\n      // we do not look deeper into the object.\n      is_object = Validator.getType(options[option]) === \"object\";\n    } else {\n      // Since all options in the reference are objects, we can check whether\n      // they are supposed to be the object to look for the __type__ field.\n      // if this is an object, we check if the correct type has been supplied to account for shorthand options.\n    }\n\n    let refOptionObj = referenceOptions[referenceOption];\n    if (is_object && refOptionObj.__type__ !== undefined) {\n      refOptionObj = refOptionObj.__type__;\n    }\n\n    Validator.checkFields(\n      option,\n      options,\n      referenceOptions,\n      referenceOption,\n      refOptionObj,\n      path,\n    );\n  }\n\n  /**\n   *\n   * @param {string}  option           | the option property\n   * @param {object}  options          | The supplied options object\n   * @param {object}  referenceOptions | The reference options containing all options and their allowed formats\n   * @param {string}  referenceOption  | Usually this is the same as option, except when handling an __any__ tag.\n   * @param {string}  refOptionObj     | This is the type object from the reference options\n   * @param {Array}   path             | where in the object is the option\n   * @static\n   */\n  static checkFields(\n    option,\n    options,\n    referenceOptions,\n    referenceOption,\n    refOptionObj,\n    path,\n  ) {\n    const log = function (message) {\n      console.error(\n        \"%c\" + message + Validator.printLocation(path, option),\n        VALIDATOR_PRINT_STYLE,\n      );\n    };\n\n    const optionType = Validator.getType(options[option]);\n    const refOptionType = refOptionObj[optionType];\n\n    if (refOptionType !== undefined) {\n      // if the type is correct, we check if it is supposed to be one of a few select values\n      if (\n        Validator.getType(refOptionType) === \"array\" &&\n        refOptionType.indexOf(options[option]) === -1\n      ) {\n        log(\n          'Invalid option detected in \"' +\n            option +\n            '\".' +\n            \" Allowed values are:\" +\n            Validator.print(refOptionType) +\n            ' not \"' +\n            options[option] +\n            '\". ',\n        );\n        errorFound = true;\n      } else if (optionType === \"object\" && referenceOption !== \"__any__\") {\n        path = copyAndExtendArray(path, option);\n        Validator.parse(\n          options[option],\n          referenceOptions[referenceOption],\n          path,\n        );\n      }\n    } else if (refOptionObj[\"any\"] === undefined) {\n      // type of the field is incorrect and the field cannot be any\n      log(\n        'Invalid type received for \"' +\n          option +\n          '\". Expected: ' +\n          Validator.print(Object.keys(refOptionObj)) +\n          \". Received [\" +\n          optionType +\n          '] \"' +\n          options[option] +\n          '\"',\n      );\n      errorFound = true;\n    }\n  }\n\n  /**\n   *\n   * @param {object | boolean | number | string | Array.<number> | Date | Node | Moment | undefined | null} object\n   * @returns {string}\n   * @static\n   */\n  static getType(object) {\n    const type = typeof object;\n\n    if (type === \"object\") {\n      if (object === null) {\n        return \"null\";\n      }\n      if (object instanceof Boolean) {\n        return \"boolean\";\n      }\n      if (object instanceof Number) {\n        return \"number\";\n      }\n      if (object instanceof String) {\n        return \"string\";\n      }\n      if (Array.isArray(object)) {\n        return \"array\";\n      }\n      if (object instanceof Date) {\n        return \"date\";\n      }\n      if (object.nodeType !== undefined) {\n        return \"dom\";\n      }\n      if (object._isAMomentObject === true) {\n        return \"moment\";\n      }\n      return \"object\";\n    } else if (type === \"number\") {\n      return \"number\";\n    } else if (type === \"boolean\") {\n      return \"boolean\";\n    } else if (type === \"string\") {\n      return \"string\";\n    } else if (type === undefined) {\n      return \"undefined\";\n    }\n    return type;\n  }\n\n  /**\n   * @param {string} option\n   * @param {object} options\n   * @param {Array.<string>} path\n   * @static\n   */\n  static getSuggestion(option, options, path) {\n    const localSearch = Validator.findInOptions(option, options, path, false);\n    const globalSearch = Validator.findInOptions(option, allOptions, [], true);\n\n    const localSearchThreshold = 8;\n    const globalSearchThreshold = 4;\n\n    let msg;\n    if (localSearch.indexMatch !== undefined) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        'Perhaps it was incomplete? Did you mean: \"' +\n        localSearch.indexMatch +\n        '\"?\\n\\n';\n    } else if (\n      globalSearch.distance <= globalSearchThreshold &&\n      localSearch.distance > globalSearch.distance\n    ) {\n      msg =\n        \" in \" +\n        Validator.printLocation(localSearch.path, option, \"\") +\n        \"Perhaps it was misplaced? Matching option found at: \" +\n        Validator.printLocation(\n          globalSearch.path,\n          globalSearch.closestMatch,\n          \"\",\n        );\n    } else if (localSearch.distance <= localSearchThreshold) {\n      msg =\n        '. Did you mean \"' +\n        localSearch.closestMatch +\n        '\"?' +\n        Validator.printLocation(localSearch.path, option);\n    } else {\n      msg =\n        \". Did you mean one of these: \" +\n        Validator.print(Object.keys(options)) +\n        Validator.printLocation(path, option);\n    }\n\n    console.error(\n      '%cUnknown option detected: \"' + option + '\"' + msg,\n      VALIDATOR_PRINT_STYLE,\n    );\n    errorFound = true;\n  }\n\n  /**\n   * traverse the options in search for a match.\n   * @param {string} option\n   * @param {object} options\n   * @param {Array} path    | where to look for the actual option\n   * @param {boolean} [recursive]\n   * @returns {{closestMatch: string, path: Array, distance: number}}\n   * @static\n   */\n  static findInOptions(option, options, path, recursive = false) {\n    let min = 1e9;\n    let closestMatch = \"\";\n    let closestMatchPath = [];\n    const lowerCaseOption = option.toLowerCase();\n    let indexMatch = undefined;\n    for (const op in options) {\n      let distance;\n      if (options[op].__type__ !== undefined && recursive === true) {\n        const result = Validator.findInOptions(\n          option,\n          options[op],\n          copyAndExtendArray(path, op),\n        );\n        if (min > result.distance) {\n          closestMatch = result.closestMatch;\n          closestMatchPath = result.path;\n          min = result.distance;\n          indexMatch = result.indexMatch;\n        }\n      } else {\n        if (op.toLowerCase().indexOf(lowerCaseOption) !== -1) {\n          indexMatch = op;\n        }\n        distance = Validator.levenshteinDistance(option, op);\n        if (min > distance) {\n          closestMatch = op;\n          closestMatchPath = copyArray(path);\n          min = distance;\n        }\n      }\n    }\n    return {\n      closestMatch: closestMatch,\n      path: closestMatchPath,\n      distance: min,\n      indexMatch: indexMatch,\n    };\n  }\n\n  /**\n   * @param {Array.<string>} path\n   * @param {object} option\n   * @param {string} prefix\n   * @returns {string}\n   * @static\n   */\n  static printLocation(path, option, prefix = \"Problem value found at: \\n\") {\n    let str = \"\\n\\n\" + prefix + \"options = {\\n\";\n    for (let i = 0; i < path.length; i++) {\n      for (let j = 0; j < i + 1; j++) {\n        str += \"  \";\n      }\n      str += path[i] + \": {\\n\";\n    }\n    for (let j = 0; j < path.length + 1; j++) {\n      str += \"  \";\n    }\n    str += option + \"\\n\";\n    for (let i = 0; i < path.length + 1; i++) {\n      for (let j = 0; j < path.length - i; j++) {\n        str += \"  \";\n      }\n      str += \"}\\n\";\n    }\n    return str + \"\\n\\n\";\n  }\n\n  /**\n   * @param {object} options\n   * @returns {string}\n   * @static\n   */\n  static print(options) {\n    return JSON.stringify(options)\n      .replace(/(\")|(\\[)|(\\])|(,\"__type__\")/g, \"\")\n      .replace(/(,)/g, \", \");\n  }\n\n  /**\n   *  Compute the edit distance between the two given strings\n   *  http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#JavaScript\n   *\n   *  Copyright (c) 2011 Andrei Mackenzie\n   *\n   *  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n   *\n   *  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n   *\n   *  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n   * @param {string} a\n   * @param {string} b\n   * @returns {Array.<Array.<number>>}}\n   * @static\n   */\n  static levenshteinDistance(a, b) {\n    if (a.length === 0) return b.length;\n    if (b.length === 0) return a.length;\n\n    const matrix = [];\n\n    // increment along the first column of each row\n    let i;\n    for (i = 0; i <= b.length; i++) {\n      matrix[i] = [i];\n    }\n\n    // increment each column in the first row\n    let j;\n    for (j = 0; j <= a.length; j++) {\n      matrix[0][j] = j;\n    }\n\n    // Fill in the rest of the matrix\n    for (i = 1; i <= b.length; i++) {\n      for (j = 1; j <= a.length; j++) {\n        if (b.charAt(i - 1) == a.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // substitution\n            Math.min(\n              matrix[i][j - 1] + 1, // insertion\n              matrix[i - 1][j] + 1,\n            ),\n          ); // deletion\n        }\n      }\n    }\n\n    return matrix[b.length][a.length];\n  }\n}\n", null], "names": ["Hammer", "Activator", "ColorPicker", "VALIDATOR_PRINT_STYLE", "ActivatorJS", "ColorPickerJS", "ConfiguratorJS", "HammerJS", "PopupJS", "VALIDATOR_PRINT_STYLE_JS", "ValidatorJS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEG;MACU,MAAM,GAAG,MAAM,CAAC,QAAQ;AAoBrC;;;;;AAKG;SACa,oBAAoB,CAClC,IAAO,EACP,GAAG,OAAwB,EAAA;IAE3B,OAAO,gBAAgB,CAAC,EAAS,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACtD;AASA;;;;AAIG;AACG,SAAU,gBAAgB,CAAC,GAAG,MAAsB,EAAA;AACxD,IAAA,MAAM,MAAM,GAAG,wBAAwB,CAAC,GAAG,MAAM,CAAC;IAClD,WAAW,CAAC,MAAM,CAAC;AACnB,IAAA,OAAO,MAAM;AACf;AAEA;;;;;;AAMG;AACH,SAAS,wBAAwB,CAAC,GAAG,MAAsB,EAAA;AACzD,IAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACrB,QAAA,OAAO,MAAM,CAAC,CAAC,CAAC;IAClB;AAAO,SAAA,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAO,wBAAwB,CAC7B,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EACtC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CACnB;IACH;AAEA,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACnB,IAAA,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAEnB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,EAAE;QAC1C,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACtB,QAAA,OAAO,CAAC;IACV;IAEA,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACrC,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;AAEnD,aAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;AAC7B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC;QAChB;AAAO,aAAA,IACL,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,YAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;YAC3B,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EACvB;AACA,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;QACtD;aAAO;YACL,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1B;IACF;AAEA,IAAA,OAAO,CAAC;AACV;AAEA;;;;AAIG;AACH,SAAS,KAAK,CAAC,CAAM,EAAA;AACnB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,KAAU,KAAK,CAAC,KAAK,CAAC,CAAC;IACjD;SAAO,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;AAC9C,QAAA,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9B;AACA,QAAA,OAAO,wBAAwB,CAAC,EAAE,EAAE,CAAC,CAAC;IACxC;SAAO;AACL,QAAA,OAAO,CAAC;IACV;AACF;AAEA;;;AAGG;AACH,SAAS,WAAW,CAAC,CAAM,EAAA;IACzB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACjC,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;AACtB,YAAA,OAAO,CAAC,CAAC,IAAI,CAAC;QAChB;AAAO,aAAA,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;AAC1D,YAAA,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACtB;IACF;AACF;;ACnIA;;;;;;AAMG;AAqBH;;;;;AAKG;AACG,SAAU,IAAI,CAAC,GAAG,IAAgB,EAAA;AACtC,IAAA,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9D;AAEA;;;;AAIG;AACH,SAAS,kBAAkB,CAAC,IAAgB,EAAA;AAC1C,IAAA,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC;IACjC,IAAI,CAAC,GAAG,CAAC;IAET,MAAM,MAAM,GAAQ,MAAa;QAC/B,MAAM,CAAC,GAAG,OAAO,GAAG,EAAE,GAAG,CAAC,GAAG,sBAAsB,CAAC;QACpD,EAAE,GAAG,EAAE;QACP,EAAE,GAAG,EAAE;AACP,QAAA,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9B,IAAA,CAAC;AAED,IAAA,MAAM,CAAC,MAAM,GAAG,MAAc,MAAM,EAAE,GAAG,WAAW,CAAC;IAErD,MAAM,CAAC,OAAO,GAAG,MACf,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,QAAQ,IAAI,CAAC,IAAI,sBAAsB,CAAC;AAElE,IAAA,MAAM,CAAC,SAAS,GAAG,MAAM;AACzB,IAAA,MAAM,CAAC,IAAI,GAAG,IAAI;AAClB,IAAA,MAAM,CAAC,OAAO,GAAG,KAAK;AAEtB,IAAA,OAAO,MAAM;AACf;AAEA;;;;;AAKG;AACH,SAAS,QAAQ,CAAC,GAAG,IAAgB,EAAA;AACnC,IAAA,MAAM,IAAI,GAAG,IAAI,EAAE;AAEnB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AAClB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AAClB,IAAA,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;AAElB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC;QACT;QACA,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC;QACT;QACA,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnB,QAAA,IAAI,EAAE,GAAG,CAAC,EAAE;YACV,EAAE,IAAI,CAAC;QACT;IACF;AAEA,IAAA,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACrB;AAOA;;;;AAIG;AACH,SAAS,IAAI,GAAA;IACX,IAAI,CAAC,GAAG,UAAU;AAElB,IAAA,OAAO,UAAU,IAAI,EAAA;AACnB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC9B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AACzB,YAAA,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAC/B,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC;YACX,CAAC,IAAI,CAAC;YACN,CAAC,IAAI,CAAC;AACN,YAAA,CAAC,GAAG,CAAC,KAAK,CAAC;YACX,CAAC,IAAI,CAAC;AACN,YAAA,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;QACvB;QACA,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC;AAC5C,IAAA,CAAC;AACH;;ACzHA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,GAAG;AACtB,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC;;AAEvB,EAAE,OAAO;AACT,IAAI,EAAE,EAAE,IAAI;AACZ,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,IAAI,EAAE,IAAI;;AAEd,IAAI,GAAG,GAAG;AACV,MAAM,OAAO;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,IAAI,CAAC;AACL,GAAG;AACH;;AAEA,MAAMA,QAAM;AACZ,EAAE,OAAO,MAAM,KAAK;AACpB,MAAM,MAAM,CAAC,MAAM,IAAI;AACvB,MAAM,YAAY;AAClB;AACA,QAAQ,OAAO,UAAU,EAAE;AAC3B,MAAM,CAAC;;AC5BP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,WAAS,CAAC,SAAS,EAAE;AACrC,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE;;AAEzB,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK;;AAErB,EAAE,IAAI,CAAC,IAAI,GAAG;AACd,IAAI,SAAS;AACb,IAAI,OAAO,EAAE,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1C,GAAG;;AAEH,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC;;AAEhD,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/D,EAAE,CAAC,CAAC;;AAEJ,EAAE,MAAM,MAAM,GAAGD,QAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1C,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAChC,IAAI,MAAM,CAAC,OAAO,EAAE;AACpB;AACA;AACA,EAAE,CAAC,CAAC;;AAEJ;AACA,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC5B,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;AAChC,MAAM,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE;AACtC,IAAI,CAAC,CAAC;AACN,EAAE,CAAC,CAAC;;AAEJ;AACA,EAAE,IAAI,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC/B,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AAChD,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC1D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;AAClC,MAAM,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC/D,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI;AACJ,MAAM,KAAK,IAAI;AACf,UAAU,KAAK,CAAC,GAAG,KAAK;AACxB,UAAU,KAAK,CAAC,OAAO,KAAK,EAAE;AAC9B,MAAM;AACN,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI;AACJ,EAAE,CAAC;AACH;;AAEA;AACA,OAAO,CAACC,WAAS,CAAC,SAAS,CAAC;;AAE5B;AACAA,WAAS,CAAC,OAAO,GAAG,IAAI;;AAExB;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAC1C,EAAE,IAAI,CAAC,UAAU,EAAE;;AAEnB,EAAE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE;AACjE,IAAI,QAAQ,EAAE;AACd,EAAE;AACF,CAAC;;AAED;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC3C;AACA,EAAE,IAAIA,WAAS,CAAC,OAAO,EAAE;AACzB,IAAIA,WAAS,CAAC,OAAO,CAAC,UAAU,EAAE;AAClC,EAAE;AACF,EAAEA,WAAS,CAAC,OAAO,GAAG,IAAI;;AAE1B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI;AACpB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM;AAC1C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC;;AAEjD,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACrB,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;;AAEvB;AACA;AACA,EAAE,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC;AAC9D,CAAC;;AAED;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAC7C,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK;AACrB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AAC3C,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;AACpD,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC;;AAEjE,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACrB,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAA,WAAS,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,KAAK,EAAE;AACrD;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;AACrC,EAAE,OAAO,OAAO,EAAE;AAClB,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU;AAChC,EAAE;AACF,EAAE,OAAO,KAAK;AACd;;AClKA;AAEA;AACA;AACA;AACA,MAAM,YAAY,GAAG,oBAAoB;AAEzC;AACA,MAAM,SAAS,GAAG,2CAA2C;AAC7D,MAAM,UAAU,GAAG,kCAAkC;AACrD,MAAM,KAAK,GACT,8GAA8G;AAChH,MAAM,MAAM,GACV,kIAAkI;AA4DpI;;;;AAIG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ;AAC7D;AAEA;;;AAGG;AACG,SAAU,kBAAkB,CAAC,SAAkC,EAAA;IACnE,IAAI,SAAS,EAAE;AACb,QAAA,OAAO,SAAS,CAAC,aAAa,EAAE,KAAK,IAAI,EAAE;AACzC,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU;YAClC,IAAI,KAAK,EAAE;gBACT,kBAAkB,CAAC,KAAK,CAAC;AACzB,gBAAA,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC;YAC9B;QACF;IACF;AACF;AAEA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ;AAC7D;AAEA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI;AACpD;AAEA;;;;AAIG;AACG,SAAU,MAAM,CAAC,KAAc,EAAA;AACnC,IAAA,IAAI,KAAK,YAAY,IAAI,EAAE;AACzB,QAAA,OAAO,IAAI;IACb;AAAO,SAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;;QAE1B,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,IAAI,KAAK,EAAE;AACT,YAAA,OAAO,IAAI;QACb;aAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;AACpC,YAAA,OAAO,IAAI;QACb;IACF;AAEA,IAAA,OAAO,KAAK;AACd;AAEA;;;;;;;;;AASG;AACH,SAAS,YAAY,CACnB,CAAM,EACN,CAAM,EACN,IAAY,EACZ,aAAsB,EAAA;IAEtB,IAAI,UAAU,GAAG,KAAK;AACtB,IAAA,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,QAAA,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS;IACxD;IAEA,IAAI,UAAU,EAAE;AACd,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC;IAChB;SAAO;QACL,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;IACpB;AACF;AAEA;;;;;;;;AAQG;AACG,SAAU,aAAa,CAC3B,CAAI,EACJ,CAAa,EACb,aAAa,GAAG,KAAK,EAAA;;;AAIrB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;;gBAEnD,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;YACzC;iBAAO;AACL,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AACrB,gBAAA,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,oBAAA,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC;gBAC5C;YACF;QACF;IACF;AACF;AAEA;;;;;;AAMG;AACI,MAAM,MAAM,GAAG,MAAM,CAAC;AAE7B;;;;;;;;AAQG;AACG,SAAU,eAAe,CAC7B,KAAe,EACf,CAAM,EACN,GAAG,MAAa,EAAA;IAEhB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,QAAA,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC;IACzE;AAEA,IAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAC1B,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,YAAA,IAAI,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBAC9D,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;YACvB;QACF;IACF;AACA,IAAA,OAAO,CAAC;AACV;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,mBAAmB,CACjC,KAAe,EACf,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;AAGrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;IAC/D;AAEA,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AACrB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAC7C,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACd;gBACA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAClC,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC;gBACpD;qBAAO;oBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;gBACzC;YACF;iBAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;AACjC,gBAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;YAC/D;iBAAO;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;YACzC;QACF;IACF;AACA,IAAA,OAAO,CAAC;AACV;AAEA;;;;;;;;;;;;;AAaG;AACG,SAAU,sBAAsB,CACpC,cAAwB,EACxB,CAAM,EACN,CAAM,EACN,aAAa,GAAG,KAAK,EAAA;;;AAIrB,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC;IAC/D;AAEA,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;YAClD;AACF,QAAA,CAAC;AACD,QAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACjC;AACF,QAAA,CAAC;AAED,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAC7C,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,gBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACd;YACA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE;AAClC,gBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/B;iBAAO;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;YACzC;QACF;aAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;AACjC,YAAA,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;AACZ,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,gBAAA,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B;QACF;aAAO;YACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;QACzC;IACF;AAEA,IAAA,OAAO,CAAC;AACV;AAEA;;;;;;;;;AASG;AACG,SAAU,UAAU,CACxB,CAAM,EACN,CAAM,EACN,WAAW,GAAG,KAAK,EACnB,aAAa,GAAG,KAAK,EAAA;AAErB,IAAA,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE;AACpB,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,WAAW,KAAK,IAAI,EAAE;AACzE,YAAA,IACE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,gBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,gBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;AACA,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzB,oBAAA,CAAC,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;gBACjD;AAAO,qBAAA,IACL,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,QAAQ;AAC3B,oBAAA,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AAChB,oBAAA,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,SAAS,EACnD;AACA,oBAAA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;gBAC5C;qBAAO;oBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;gBACzC;YACF;iBAAO,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gBACjC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;YAC3B;iBAAO;gBACL,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;YACzC;QACF;IACF;AACA,IAAA,OAAO,CAAC;AACV;AAEA;;;;;AAKG;AACG,SAAU,UAAU,CAAC,CAAY,EAAE,CAAY,EAAA;IACnD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;AACzB,QAAA,OAAO,KAAK;IACd;AAEA,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5C,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AAChB,YAAA,OAAO,KAAK;QACd;IACF;AAEA,IAAA,OAAO,IAAI;AACb;AAEA;;;;AAIG;AACG,SAAU,OAAO,CAAC,MAAe,EAAA;AACrC,IAAA,MAAM,IAAI,GAAG,OAAO,MAAM;AAE1B,IAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,OAAO,MAAM;QACf;AACA,QAAA,IAAI,MAAM,YAAY,OAAO,EAAE;AAC7B,YAAA,OAAO,SAAS;QAClB;AACA,QAAA,IAAI,MAAM,YAAY,MAAM,EAAE;AAC5B,YAAA,OAAO,QAAQ;QACjB;AACA,QAAA,IAAI,MAAM,YAAY,MAAM,EAAE;AAC5B,YAAA,OAAO,QAAQ;QACjB;AACA,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,OAAO,OAAO;QAChB;AACA,QAAA,IAAI,MAAM,YAAY,IAAI,EAAE;AAC1B,YAAA,OAAO,MAAM;QACf;AAEA,QAAA,OAAO,QAAQ;IACjB;AACA,IAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,QAAA,OAAO,QAAQ;IACjB;AACA,IAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,OAAO,SAAS;IAClB;AACA,IAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,QAAA,OAAO,QAAQ;IACjB;AACA,IAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,QAAA,OAAO,WAAW;IACpB;AAEA,IAAA,OAAO,IAAI;AACb;AAOA;;;;;AAKG;AACG,SAAU,kBAAkB,CAChC,GAAqB,EACrB,QAAW,EAAA;AAEX,IAAA,OAAO,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC;AAC3B;AAEA;;;;AAIG;AACG,SAAU,SAAS,CAAI,GAAqB,EAAA;AAChD,IAAA,OAAO,GAAG,CAAC,KAAK,EAAE;AACpB;AAEA;;;;AAIG;AACG,SAAU,eAAe,CAAC,IAAa,EAAA;AAC3C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,IAAI;AAC1C;AAEA;;;;AAIG;AACG,SAAU,gBAAgB,CAAC,IAAa,EAAA;AAC5C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,KAAK;AAC3C;AAEA;;;;AAIG;AACG,SAAU,cAAc,CAAC,IAAa,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,GAAG;AACzC;AAEA;;;;AAIG;AACG,SAAU,YAAY,CAAC,IAAa,EAAE,UAAkB,EAAA;IAC5D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;IACxC,OAAO,GAAG,OAAO,CAAC,MAAM,CACtB,UAAU,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;AACnC,QAAA,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;IACrC,CAAC,CAAC,CACH;IACD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACpC;AAEA;;;;AAIG;AACG,SAAU,eAAe,CAAC,IAAa,EAAE,UAAkB,EAAA;IAC/D,IAAI,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;AACxC,IAAA,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,SAAS,EAAA;AAC1C,QAAA,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;AACxC,IAAA,CAAC,CAAC;IACF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACpC;AAUA;;;;;;AAMG;AACG,SAAU,OAAO,CAAC,MAAW,EAAE,QAAa,EAAA;AAChD,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;AAEzB,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM;AACzB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;QAChC;IACF;SAAO;;AAEL,QAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AACxB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;gBACrD,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;YACpC;QACF;IACF;AACF;AAEA;;;;AAIG;AACI,MAAM,OAAO,GAAG,MAAM,CAAC;AAE9B;;;;;;AAMG;SACa,cAAc,CAC5B,MAAoB,EACpB,GAAM,EACN,KAAQ,EAAA;AAER,IAAA,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK,EAAE;AACzB,QAAA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK;AACnB,QAAA,OAAO,IAAI;IACb;SAAO;AACL,QAAA,OAAO,KAAK;IACd;AACF;AAEA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,EAAc,EAAA;IACrC,IAAI,SAAS,GAAG,KAAK;AAErB,IAAA,OAAO,MAAW;QAChB,IAAI,CAAC,SAAS,EAAE;YACd,SAAS,GAAG,IAAI;YAChB,qBAAqB,CAAC,MAAW;gBAC/B,SAAS,GAAG,KAAK;AACjB,gBAAA,EAAE,EAAE;AACN,YAAA,CAAC,CAAC;QACJ;AACF,IAAA,CAAC;AACH;AAEA;;;AAGG;AACG,SAAU,cAAc,CAAC,KAAwB,EAAA;IACrD,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,KAAK,GAAG,MAAM,CAAC,KAAK;IACtB;IAEA,IAAI,CAAC,KAAK,EAAE;AAEL,SAAA,IAAI,KAAK,CAAC,cAAc,EAAE;AAC/B,QAAA,KAAK,CAAC,cAAc,EAAE,CAAC;IACzB;SAAO;;AAEJ,QAAA,KAAa,CAAC,WAAW,GAAG,KAAK,CAAC;IACrC;AACF;AAEA;;;;AAIG;SACa,SAAS,CACvB,KAAA,GAA2B,MAAM,CAAC,KAAK,EAAA;;;IAKvC,IAAI,MAAM,GAAuB,IAAI;IACrC,IAAI,CAAC,KAAK,EAAE;AAEL,SAAA,IAAI,KAAK,CAAC,MAAM,EAAE;AACvB,QAAA,MAAM,GAAG,KAAK,CAAC,MAAM;IACvB;AAAO,SAAA,IAAI,KAAK,CAAC,UAAU,EAAE;AAC3B,QAAA,MAAM,GAAG,KAAK,CAAC,UAAU;IAC3B;AAEA,IAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AAChC,QAAA,OAAO,IAAI;IACb;AAEA,IAAA,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,EAAE;;AAEnD,QAAA,MAAM,GAAG,MAAM,CAAC,UAAU;AAC1B,QAAA,IAAI,EAAE,MAAM,YAAY,OAAO,CAAC,EAAE;AAChC,YAAA,OAAO,IAAI;QACb;IACF;AAEA,IAAA,OAAO,MAAM;AACf;AAEA;;;;;AAKG;AACG,SAAU,SAAS,CAAC,OAAgB,EAAE,MAAe,EAAA;IACzD,IAAI,IAAI,GAAS,OAAO;IAExB,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,IAAI,KAAK,MAAM,EAAE;AACnB,YAAA,OAAO,IAAI;QACb;AAAO,aAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AAC1B,YAAA,IAAI,GAAG,IAAI,CAAC,UAAU;QACxB;aAAO;AACL,YAAA,OAAO,KAAK;QACd;IACF;AAEA,IAAA,OAAO,KAAK;AACd;AAEO,MAAM,MAAM,GAAG;AACpB;;;;;AAKG;IACH,SAAS,CAAC,KAAc,EAAE,YAAsB,EAAA;AAC9C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE;QACjB;AAEA,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,KAAK,IAAI,KAAK;QACvB;QAEA,OAAO,YAAY,IAAI,IAAI;IAC7B,CAAC;AAED;;;;;AAKG;IACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE;QACjB;AAEA,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,YAAY,IAAI,IAAI;QAC9C;QAEA,OAAO,YAAY,IAAI,IAAI;IAC7B,CAAC;AAED;;;;;AAKG;IACH,QAAQ,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC5C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE;QACjB;AAEA,QAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,YAAA,OAAO,MAAM,CAAC,KAAK,CAAC;QACtB;QAEA,OAAO,YAAY,IAAI,IAAI;IAC7B,CAAC;AAED;;;;;AAKG;IACH,MAAM,CAAC,KAAc,EAAE,YAAqB,EAAA;AAC1C,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE;QACjB;AAEA,QAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACnB,YAAA,OAAO,KAAK;QACd;AAAO,aAAA,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,OAAO,KAAK,GAAG,IAAI;QACrB;aAAO;YACL,OAAO,YAAY,IAAI,IAAI;QAC7B;IACF,CAAC;AAED;;;;;AAKG;IACH,SAAS,CACP,KAA4C,EAC5C,YAAe,EAAA;AAEf,QAAA,IAAI,OAAO,KAAK,IAAI,UAAU,EAAE;YAC9B,KAAK,GAAG,KAAK,EAAE;QACjB;AAEA,QAAA,OAAO,KAAK,IAAI,YAAY,IAAI,IAAI;IACtC,CAAC;;AAGH;;;;;;AAMG;AACG,SAAU,QAAQ,CAAC,GAAW,EAAA;AAClC,IAAA,IAAI,MAAM;AACV,IAAA,QAAQ,GAAG,CAAC,MAAM;AAChB,QAAA,KAAK,CAAC;AACN,QAAA,KAAK,CAAC;AACJ,YAAA,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;AAC7B,YAAA,OAAO;AACL,kBAAE;AACE,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACtC,oBAAA,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AACvC;kBACD,IAAI;AACV,QAAA,KAAK,CAAC;AACN,QAAA,KAAK,CAAC;AACJ,YAAA,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;AAC5B,YAAA,OAAO;AACL,kBAAE;oBACE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;oBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;oBAC1B,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AAC3B;kBACD,IAAI;AACV,QAAA;AACE,YAAA,OAAO,IAAI;;AAEjB;AAEA;;;;;AAKG;AACG,SAAU,eAAe,CAAC,KAAa,EAAE,OAAe,EAAA;AAC5D,IAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAA,OAAO,KAAK;IACd;AAAO,SAAA,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAChC,MAAM,GAAG,GAAG;aACT,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7B,aAAA,OAAO,CAAC,GAAG,EAAE,EAAE;aACf,KAAK,CAAC,GAAG,CAAC;QACb,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;IAC7E;SAAO;AACL,QAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC3B,QAAA,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,YAAA,OAAO,KAAK;QACd;aAAO;YACL,OAAO,OAAO,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;QAC1E;IACF;AACF;AAEA;;;;;;AAMG;SACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;AAC/D,IAAA,QACE,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAE/E;AAsCA;;;;;AAKG;AACG,SAAU,UAAU,CACxB,UAAgC,EAChC,YAA8B,EAAA;AAE9B,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxB,IAAI,QAAQ,GAAW,UAAU;AACjC,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE;YACxB,MAAM,GAAG,GAAG;iBACT,MAAM,CAAC,CAAC;iBACR,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;iBAC7B,KAAK,CAAC,GAAG;iBACT,GAAG,CAAC,UAAU,KAAK,EAAA;AAClB,gBAAA,OAAO,QAAQ,CAAC,KAAK,CAAC;AACxB,YAAA,CAAC,CAAC;AACJ,YAAA,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7C;AACA,QAAA,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;AACjC,YAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;AAC9B,YAAA,MAAM,eAAe,GAAG;gBACtB,CAAC,EAAE,GAAG,CAAC,CAAC;AACR,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;AACd,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;aAC7B;AACD,YAAA,MAAM,cAAc,GAAG;gBACrB,CAAC,EAAE,GAAG,CAAC,CAAC;AACR,gBAAA,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC5B,gBAAA,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;aACf;AACD,YAAA,MAAM,cAAc,GAAG,QAAQ,CAC7B,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,EAChB,cAAc,CAAC,CAAC,CACjB;AACD,YAAA,MAAM,eAAe,GAAG,QAAQ,CAC9B,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,EACjB,eAAe,CAAC,CAAC,CAClB;YACD,OAAO;AACL,gBAAA,UAAU,EAAE,QAAQ;AACpB,gBAAA,MAAM,EAAE,cAAc;AACtB,gBAAA,SAAS,EAAE;AACT,oBAAA,UAAU,EAAE,eAAe;AAC3B,oBAAA,MAAM,EAAE,cAAc;AACvB,iBAAA;AACD,gBAAA,KAAK,EAAE;AACL,oBAAA,UAAU,EAAE,eAAe;AAC3B,oBAAA,MAAM,EAAE,cAAc;AACvB,iBAAA;aACF;QACH;aAAO;YACL,OAAO;AACL,gBAAA,UAAU,EAAE,QAAQ;AACpB,gBAAA,MAAM,EAAE,QAAQ;AAChB,gBAAA,SAAS,EAAE;AACT,oBAAA,UAAU,EAAE,QAAQ;AACpB,oBAAA,MAAM,EAAE,QAAQ;AACjB,iBAAA;AACD,gBAAA,KAAK,EAAE;AACL,oBAAA,UAAU,EAAE,QAAQ;AACpB,oBAAA,MAAM,EAAE,QAAQ;AACjB,iBAAA;aACF;QACH;IACF;SAAO;QACL,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,KAAK,GAAoB;AAC7B,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU;AAC5D,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,YAAY,CAAC,MAAM;AAChD,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;AACtC,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,SAAS;wBAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;AACjC;AACH,sBAAE;wBACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;4BACxD,YAAY,CAAC,SAAS,CAAC,UAAU;wBACnC,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;4BACpD,YAAY,CAAC,SAAS,CAAC,MAAM;AAChC,qBAAA;AACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK;AAC9B,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,KAAK;wBACxB,UAAU,EAAE,UAAU,CAAC,KAAK;AAC7B;AACH,sBAAE;wBACE,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM;4BAC5C,YAAY,CAAC,KAAK,CAAC,MAAM;wBAC3B,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU;4BAChD,YAAY,CAAC,KAAK,CAAC,UAAU;AAChC,qBAAA;aACN;AACD,YAAA,OAAO,KAAK;QACd;aAAO;AACL,YAAA,MAAM,KAAK,GAAgB;AACzB,gBAAA,UAAU,EAAE,UAAU,CAAC,UAAU,IAAI,SAAS;AAC9C,gBAAA,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,SAAS;AACtC,gBAAA,SAAS,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS;AACtC,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,SAAS;wBAC5B,UAAU,EAAE,UAAU,CAAC,SAAS;AACjC;AACH,sBAAE;wBACE,UAAU,EACR,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,UAAU;4BACxD,SAAS;wBACX,MAAM,EACJ,CAAC,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM;4BACpD,SAAS;AACZ,qBAAA;AACL,gBAAA,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK;AAC9B,sBAAE;wBACE,MAAM,EAAE,UAAU,CAAC,KAAK;wBACxB,UAAU,EAAE,UAAU,CAAC,KAAK;AAC7B;AACH,sBAAE;AACE,wBAAA,MAAM,EACJ,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS;AAC5D,wBAAA,UAAU,EACR,CAAC,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,KAAK,SAAS;AACjE,qBAAA;aACN;AACD,YAAA,OAAO,KAAK;QACd;IACF;AACF;AAEA;;;;;;;;AAQG;SACa,QAAQ,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAA;AAC/D,IAAA,GAAG,GAAG,GAAG,GAAG,GAAG;AACf,IAAA,KAAK,GAAG,KAAK,GAAG,GAAG;AACnB,IAAA,IAAI,GAAG,IAAI,GAAG,GAAG;AACjB,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACnD,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;;AAGnD,IAAA,IAAI,MAAM,KAAK,MAAM,EAAE;AACrB,QAAA,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE;IAClC;;AAGA,IAAA,MAAM,CAAC,GACL,GAAG,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG;IAC5E,MAAM,CAAC,GAAG,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AACtD,IAAA,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG;IACpD,MAAM,UAAU,GAAG,CAAC,MAAM,GAAG,MAAM,IAAI,MAAM;IAC7C,MAAM,KAAK,GAAG,MAAM;AACpB,IAAA,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE;AAC5C;AAMA;;;;AAIG;AACH,SAAS,YAAY,CAAC,OAAe,EAAA;IACnC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;IAEjD,MAAM,MAAM,GAAc,EAAE;AAE5B,IAAA,WAAW,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AAEnC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;QACjD,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAC/D,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CACrB;IACH;AAEA,IAAA,OAAO,MAAM;AACf;AAEA;;;;AAIG;AACG,SAAU,UAAU,CAAC,OAAoB,EAAE,OAAe,EAAA;AAC9D,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC;AACtC,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACnD,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC;IACvC;AACF;AAEA;;;;AAIG;AACG,SAAU,aAAa,CAAC,OAAoB,EAAE,OAAe,EAAA;AACjE,IAAA,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC;IACtC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACvC,QAAA,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC;IACnC;AACF;AAEA;;;;;;;;AAQG;SACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;AACtD,IAAA,IAAI,CAAqB;AACzB,IAAA,IAAI,CAAqB;AACzB,IAAA,IAAI,CAAqB;IAEzB,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3B,IAAA,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACnB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,IAAA,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAE/B,IAAA,QAAQ,CAAC,GAAG,CAAC;AACX,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;AACF,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;AACF,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;AACF,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;AACF,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;AACF,QAAA,KAAK,CAAC;AACJ,YAAA,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1B;;IAGJ,OAAO;QACL,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;QAClC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAE,CAAY,GAAG,GAAG,CAAC;KACnC;AACH;AAEA;;;;;;AAMG;SACa,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;IACtD,MAAM,GAAG,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACtC;AAEA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,GAAW,EAAA;AAClC,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;IACzB,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,MAAM,IAAI,SAAS,CAAC,IAAI,GAAG,CAAA,uBAAA,CAAyB,CAAC;IACvD;AACA,IAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACtC;AAEA;;;;AAIG;AACG,SAAU,UAAU,CAAC,GAAW,EAAA;IACpC,MAAM,IAAI,GAAG,oCAAoC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC3D,IAAA,OAAO,IAAI;AACb;AAEA;;;;AAIG;AACG,SAAU,UAAU,CAAC,GAAW,EAAA;AACpC,IAAA,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACxB;AAEA;;;;AAIG;AACG,SAAU,WAAW,CAAC,IAAY,EAAA;AACtC,IAAA,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1B;AAEA;;;;;;AAMG;AACG,SAAU,qBAAqB,CACnC,MAAW,EACX,eAA6B,EAAA;IAE7B,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;;QAEnE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC;AAC/C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;gBACpE,IAAI,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;AACjD,oBAAA,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE;YACF;QACF;AACA,QAAA,OAAO,QAAQ;IACjB;SAAO;AACL,QAAA,OAAO,IAAI;IACb;AACF;AAIA;;;;;AAKG;AACG,SAAU,YAAY,CAC1B,eAAkB,EAAA;IAElB,IAAI,eAAe,KAAK,IAAI,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;AACnE,QAAA,OAAO,IAAI;IACb;AAEA,IAAA,IAAI,eAAe,YAAY,OAAO,EAAE;;AAEtC,QAAA,OAAO,eAAe;IACxB;IAEA,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC;AAC/C,IAAA,KAAK,MAAM,CAAC,IAAI,eAAe,EAAE;AAC/B,QAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE;YAC5D,IAAI,OAAQ,eAAuB,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;gBAClD,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAE,eAAuB,CAAC,CAAC,CAAC,CAAC;YACzD;QACF;IACF;AAEA,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;;AAKG;AACG,SAAU,UAAU,CAAI,CAAM,EAAE,OAA+B,EAAA;AACnE,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,QAAA,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACd,QAAA,IAAI,CAAC;QACL,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAClD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACjB;AACA,QAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IACV;AACA,IAAA,OAAO,CAAC;AACV;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,YAAY,CAC1B,WAAgB,EAChB,OAAY,EACZ,MAAc,EACd,aAAA,GAAqB,EAAE,EAAA;;IAGvB,MAAM,SAAS,GAAG,UAAU,GAAQ,EAAA;AAClC,QAAA,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS;AAC1C,IAAA,CAAC;IAED,MAAM,QAAQ,GAAG,UAAU,GAAY,EAAA;QACrC,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;AAChD,IAAA,CAAC;;IAGD,MAAM,OAAO,GAAG,UAAU,GAAW,EAAA;AACnC,QAAA,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE;AACnB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;AAChD,gBAAA,OAAO,KAAK;YACd;QACF;AACA,QAAA,OAAO,IAAI;AACb,IAAA,CAAC;;AAGD,IAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC;IAC5D;AAEA,IAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;IACxD;AAEA,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC;IACvD;AAEA,IAAA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC5B,QAAA,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC;IAC9D;;;;;AAMA,IAAA,MAAM,OAAO,GAAG,UAAU,MAAW,EAAE,OAAY,EAAE,MAAc,EAAA;QACjE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;AAC7B,YAAA,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;QACrB;AAEA,QAAA,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;AAC3B,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;AAC1B,QAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AACtB,YAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;gBACnD,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;YACvB;QACF;AACF,IAAA,CAAC;;AAGD,IAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;AACjC,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;AACvE,IAAA,MAAM,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS;AACrE,IAAA,MAAM,aAAa,GAAG,YAAY,GAAG,YAAY,CAAC,OAAO,GAAG,SAAS;;;;AAKrE,IAAA,IAAI,SAAS,KAAK,SAAS,EAAE;AAC3B,QAAA,OAAO;IACT;AAEA,IAAA,IAAI,OAAO,SAAS,KAAK,SAAS,EAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;AAClC,YAAA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;QAC1B;AAEA,QAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,SAAS;QACvC;IACF;AAEA,IAAA,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE;;AAExD,QAAA,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;YAC3B,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;QACnD;aAAO;AACL,YAAA,OAAO;QACT;IACF;AAEA,IAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACxB;IACF;;;;;AAMA,IAAA,IAAI,OAAO,GAAG,IAAI,CAAC;AAEnB,IAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;AACnC,QAAA,OAAO,GAAG,SAAS,CAAC,OAAO;IAC7B;SAAO;;AAEL,QAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,OAAO,GAAG,YAAY,CAAC,OAAO;QAChC;IACF;AAEA,IAAA,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC;AACrC,IAAA,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,GAAG,OAAO;AACvC;AAiBA;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAChC,YAAmB,EACnB,UAAsC,EACtC,KAAa,EACb,MAAe,EAAA;IAEf,MAAM,aAAa,GAAG,KAAK;IAC3B,IAAI,SAAS,GAAG,CAAC;IACjB,IAAI,GAAG,GAAG,CAAC;AACX,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;IAElC,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;AAC/C,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC;AAE3C,QAAA,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC;QACjC,MAAM,KAAK,GAAG,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAEtE,QAAA,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC;AACtC,QAAA,IAAI,YAAY,IAAI,CAAC,EAAE;;AAErB,YAAA,OAAO,MAAM;QACf;AAAO,aAAA,IAAI,YAAY,IAAI,EAAE,EAAE;;AAE7B,YAAA,GAAG,GAAG,MAAM,GAAG,CAAC;QAClB;aAAO;;AAEL,YAAA,IAAI,GAAG,MAAM,GAAG,CAAC;QACnB;AAEA,QAAA,SAAS,EAAE;IACb;IAEA,OAAO,EAAE;AACX;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,iBAAiB,CAC/B,YAAoC,EACpC,MAAc,EACd,KAAQ,EACR,cAAkC,EAClC,UAAiD,EAAA;IAEjD,MAAM,aAAa,GAAG,KAAK;IAC3B,IAAI,SAAS,GAAG,CAAC;IACjB,IAAI,GAAG,GAAG,CAAC;AACX,IAAA,IAAI,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;AAClC,IAAA,IAAI,SAAS;AACb,IAAA,IAAI,KAAK;AACT,IAAA,IAAI,SAAS;AACb,IAAA,IAAI,MAAM;IAEV,UAAU;AACR,QAAA,UAAU,IAAI;AACZ,cAAE;AACF,cAAE,UAAU,CAAS,EAAE,CAAS,EAAA;gBAC5B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACpC,YAAA,CAAC;IAEP,OAAO,GAAG,IAAI,IAAI,IAAI,SAAS,GAAG,aAAa,EAAE;;AAE/C,QAAA,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AACvC,QAAA,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACxD,KAAK,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;QACnC,SAAS;AACP,YAAA,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpE,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;;AAElC,YAAA,OAAO,MAAM;QACf;AAAO,aAAA,IACL,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC;YACjC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAC7B;;YAEA,OAAO,cAAc,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM;QACtE;AAAO,aAAA,IACL,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;YAC7B,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,EACjC;;YAEA,OAAO,cAAc,IAAI;AACvB,kBAAE;AACF,kBAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC;QACnD;aAAO;;YAEL,IAAI,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;;AAEjC,gBAAA,GAAG,GAAG,MAAM,GAAG,CAAC;YAClB;iBAAO;;AAEL,gBAAA,IAAI,GAAG,MAAM,GAAG,CAAC;YACnB;QACF;AACA,QAAA,SAAS,EAAE;IACb;;IAGA,OAAO,EAAE;AACX;AAEA;;;;;;AAMG;AACI,MAAM,eAAe,GAAG;AAC7B;;;;AAIG;AACH,IAAA,MAAM,CAAC,CAAS,EAAA;AACd,QAAA,OAAO,CAAC;IACV,CAAC;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,CAAS,EAAA;QAClB,OAAO,CAAC,GAAG,CAAC;IACd,CAAC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;AAED;;;;AAIG;AACH,IAAA,aAAa,CAAC,CAAS,EAAA;QACrB,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;IACnD,CAAC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IAClB,CAAC;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;QACpB,OAAO,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACxB,CAAC;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1E,CAAC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;AACnB,QAAA,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACtB,CAAC;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;QACpB,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5B,CAAC;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAC9D,CAAC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,CAAS,EAAA;QACnB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1B,CAAC;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,CAAS,EAAA;AACpB,QAAA,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,CAAC;AAED;;;;AAIG;AACH,IAAA,cAAc,CAAC,CAAS,EAAA;AACtB,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACxE,CAAC;;AAGH;;;AAGG;SACa,iBAAiB,GAAA;IAC/B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC;AACzC,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;AAC1B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO;IAE5B,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC3C,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU;AACjC,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK;AACvB,IAAA,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK;AACxB,IAAA,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;AACjC,IAAA,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO;AAC3B,IAAA,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO;AAC5B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ;AAC/B,IAAA,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;AAExB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AAChC,IAAA,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW;AAC5B,IAAA,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ;AAC/B,IAAA,IAAI,EAAE,GAAG,KAAK,CAAC,WAAW;AAC1B,IAAA,IAAI,EAAE,IAAI,EAAE,EAAE;AACZ,QAAA,EAAE,GAAG,KAAK,CAAC,WAAW;IACxB;AAEA,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAEhC,OAAO,EAAE,GAAG,EAAE;AAChB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMG;AACG,SAAU,OAAO,CAAC,IAAS,EAAE,SAAc,EAAA;AAC/C,IAAA,IAAI,SAAS;IACb,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC7B,QAAA,SAAS,GAAG,CAAC,SAAS,CAAC;IACzB;AACA,IAAA,KAAK,MAAM,MAAM,IAAI,IAAI,EAAE;QACzB,IAAI,MAAM,EAAE;YACV,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAChC,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,SAAS,EAAE;oBACb,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACrC;YACF;AACA,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;gBACpC;YACF;QACF;IACF;AACA,IAAA,OAAO,SAAS;AAClB;;ACpsDA,MAAM,UAAU,GAAG;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,iBAAiB,EAAE,SAAS;AAC9B,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,gBAAgB,EAAE,SAAS;AAC7B,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,eAAe,EAAE,SAAS;AAC5B,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,GAAG,EAAE,SAAS;AAChB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,oBAAoB,EAAE,SAAS;AACjC,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,GAAG,EAAE,SAAS;AAChB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,SAAS,EAAE,SAAS;AACtB,EAAE,cAAc,EAAE,SAAS;AAC3B,EAAE,UAAU,EAAE,SAAS;AACvB,EAAE,aAAa,EAAE,SAAS;AAC1B,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,YAAY,EAAE,SAAS;AACzB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,IAAI,EAAE,SAAS;AACjB,EAAE,MAAM,EAAE,SAAS;AACnB,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,KAAK,EAAE,SAAS;AAClB,CAAC;;AAED;AACA;AACA;oBACO,MAAM,WAAW,CAAC;AACzB;AACA;AACA;AACA,EAAE,WAAW,CAAC,UAAU,GAAG,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK;AAC1B,IAAI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;AACvD,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI;AACvB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACnD,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AAC1D,IAAI,IAAI,CAAC,aAAa,GAAG,SAAS;AAClC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK;;AAExB;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC;;AAEjC;AACA,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,SAAS,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;AACnC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3B,MAAM,IAAI,CAAC,MAAM,GAAG,SAAS;AAC7B,IAAI;AACJ,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,WAAW,EAAE;;AAEtB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AAC9B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,IAAI,CAAC,cAAc,GAAG,QAAQ;AACpC,IAAI,CAAC,MAAM;AACX,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,6EAA6E;AACrF,OAAO;AACP,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,QAAQ,EAAE;AAC7B,IAAI,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,GAAG,QAAQ;AACnC,IAAI,CAAC,MAAM;AACX,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,8EAA8E;AACtF,OAAO;AACP,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,OAAO,UAAU,CAAC,KAAK,CAAC;AAC9B,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,MAAM;AACN,IAAI;;AAEJ,IAAI,IAAI,IAAI;;AAEZ;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAChD,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,KAAK,GAAG,SAAS;AACvB,IAAI;;AAEJ;AACA,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAClC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AACtC,QAAQ,MAAM,SAAS,GAAG;AAC1B,WAAW,MAAM,CAAC,CAAC;AACnB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;AACrC,WAAW,KAAK,CAAC,GAAG,CAAC;AACrB,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;AAC5E,MAAM,CAAC,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAC9C,QAAQ,MAAM,SAAS,GAAG;AAC1B,WAAW,MAAM,CAAC,CAAC;AACnB,WAAW,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;AACrC,WAAW,KAAK,CAAC,GAAG,CAAC;AACrB,QAAQ,IAAI,GAAG;AACf,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;AACzB,SAAS;AACT,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;AAC7C,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;AACtC,QAAQ,IAAI,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;AAChE,MAAM;AACN,IAAI,CAAC,MAAM;AACX,MAAM,IAAI,KAAK,YAAY,MAAM,EAAE;AACnC,QAAQ;AACR,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;AAC/B,UAAU,KAAK,CAAC,CAAC,KAAK,SAAS;AAC/B,UAAU,KAAK,CAAC,CAAC,KAAK;AACtB,UAAU;AACV,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,KAAK,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,KAAK;AAC/D,UAAU,IAAI,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE;AACjE,QAAQ;AACR,MAAM;AACN,IAAI;;AAEJ;AACA,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE;AAC5B,MAAM,MAAM,IAAI,KAAK;AACrB,QAAQ,+HAA+H;AACvI,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AAC/B,OAAO;AACP,IAAI,CAAC,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC;AACtC,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,MAAM,IAAI,CAAC,aAAa,GAAG,SAAS;AACpC,IAAI;;AAEJ,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK;AACxB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AACtC,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,EAAE;;AAEF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,EAAE;AAC9B;AACA,IAAI,IAAI,aAAa,KAAK,IAAI,EAAE;AAChC,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC;AACxD,IAAI;;AAEJ,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;AAC/B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;AAC5C,IAAI;;AAEJ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM;;AAErC;AACA;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC5C,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,QAAQ,IAAI,CAAC,aAAa,GAAG,SAAS;AACtC,MAAM;AACN,IAAI,CAAC,EAAE,CAAC,CAAC;AACT,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK;AACxB,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI;AACvB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAClC,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC;AAC9C,IAAI,CAAC,MAAM;AACX,MAAM,KAAK,CAAC,mCAAmC,CAAC;AAChD,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE;AACrC;AACA,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;AAC7B,MAAM,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC;AACjD,IAAI;;AAEJ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;AACrB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;AAEhD,IAAI,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE;AACpC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC;AACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;AACxE,IAAI,MAAM,CAAC;AACX,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;;AAExE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;AACvC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI;AAC3D,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;AACtC,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI;;AAE5D,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AAC5B,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG;AAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAClC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,KAAK,EAAE;AACxB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG;AACvB,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;AACrB,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE;AACnC,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAChD,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;AACvD,IAAI,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AACxC,MAAM,IAAI,CAAC,UAAU;AACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACrC,SAAS,GAAG,CAAC,4BAA4B;AACzC,UAAU,GAAG,CAAC,yBAAyB;AACvC,UAAU,GAAG,CAAC,wBAAwB;AACtC,UAAU,GAAG,CAAC,uBAAuB;AACrC,UAAU,GAAG,CAAC,sBAAsB;AACpC,UAAU,CAAC,CAAC;AACZ,IAAI;AACJ,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;;AAElE;AACA,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;AAChD,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY;AACjD,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;AAE7B,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C,IAAI,GAAG,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG;AACrD,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1E,IAAI,GAAG,CAAC,IAAI,EAAE;;AAEd,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;;AAE1C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;AAC9C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;AAC1C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;AAC/C,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;;AAEhD,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU;AACxD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,UAAU;AACzD,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,kBAAkB;;AAE7C,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACvD,IAAI,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5D,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,cAAc;AACvD,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC;;AAE7D,IAAI,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;AAE3D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;AAC5C,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACpD,MAAM,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AAClC,MAAM,QAAQ,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM;AACxC,MAAM,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM;AACrC,MAAM,QAAQ,CAAC,SAAS,GAAG,kDAAkD;AAC7E,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC;AAClD,IAAI,CAAC,MAAM;AACX,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;AACzD,MAAM,IAAI,CAAC,UAAU;AACrB,QAAQ,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACrC,SAAS,GAAG,CAAC,4BAA4B;AACzC,UAAU,GAAG,CAAC,yBAAyB;AACvC,UAAU,GAAG,CAAC,wBAAwB;AACtC,UAAU,GAAG,CAAC,uBAAuB;AACrC,UAAU,GAAG,CAAC,sBAAsB;AACpC,UAAU,CAAC,CAAC;AACZ,MAAM,IAAI,CAAC;AACX,SAAS,UAAU,CAAC,IAAI;AACxB,SAAS,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AACnE,IAAI;;AAEJ,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,WAAW;;AAE/C,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,aAAa;;AAE7C,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACtD,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,gBAAgB;;AAEnD,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,WAAW;;AAEzC,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACvD,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;AACvC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,GAAG;AACjC,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,KAAK;AACnC,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,IAAI;AACJ,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK;AACnC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,WAAW;;AAE7C,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AAC1D,IAAI,IAAI;AACR,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC;AAC1C,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,GAAG;AACpC,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,KAAK;AACtC,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,IAAI;AACJ,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,KAAK;AACtC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,WAAW;;AAEhD,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AAClD,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;;AAExD,IAAI,MAAM,EAAE,GAAG,IAAI;AACnB,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,YAAY;AAC7C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,IAAI,CAAC;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,YAAY;AAC5C,MAAM,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,IAAI,CAAC;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,YAAY;AAChD,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,IAAI,CAAC;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,YAAY;AAC/C,MAAM,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;AACnC,IAAI,CAAC;;AAEL,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACxD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,0BAA0B;AAC/D,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,aAAa;;AAElD,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACrD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB;AACzD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,UAAU;;AAE5C,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACpD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,eAAe;AAChD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK;;AAEtC,IAAI,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACxD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,mBAAmB;AACxD,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS;;AAE9C,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACrD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,uBAAuB;AACzD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,QAAQ;AAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;;AAE5D,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACpD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,sBAAsB;AACvD,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO;AACxC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;;AAErD,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB;AACrD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM;AACtC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEnD,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,qBAAqB;AACrD,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,WAAW;AAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEvD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;AACzC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;AAChD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;;AAEhD,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAC3C,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE;AAClB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAID,QAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACpD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;;AAElD,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,KAAK,KAAK;AAC9C,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AACjC,MAAM;AACN,IAAI,CAAC,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,KAAK;AACrC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/B,IAAI,CAAC,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK,KAAK;AAC1C,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/B,IAAI,CAAC,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;AACzC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/B,IAAI,CAAC,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,KAAK;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/B,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;AAClC,MAAM,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;AACzD,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AAC1C,QAAQ,IAAI,CAAC,UAAU;AACvB,UAAU,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC;AACvC,WAAW,GAAG,CAAC,4BAA4B;AAC3C,YAAY,GAAG,CAAC,yBAAyB;AACzC,YAAY,GAAG,CAAC,wBAAwB;AACxC,YAAY,GAAG,CAAC,uBAAuB;AACvC,YAAY,GAAG,CAAC,sBAAsB;AACtC,YAAY,CAAC,CAAC;AACd,MAAM;AACN,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;;AAEpE;AACA,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW;AAClD,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY;AACnD,MAAM,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;AAE/B;AACA,MAAM,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG;AACxB,MAAM,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE;AACzD,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;AACvB,MAAM,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG;AAC9C,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG;AAC1B,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AAC7B,MAAM,IAAI,GAAG;AACb,MAAM,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;AACtC,QAAQ,KAAK,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;AAC3C,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC;AAC3E,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC;AAC3E,UAAU,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;AACnD,UAAU,GAAG,CAAC,SAAS,GAAG,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;AAC1E,UAAU,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9C,QAAQ;AACR,MAAM;AACN,MAAM,GAAG,CAAC,WAAW,GAAG,eAAe;AACvC,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5E,MAAM,GAAG,CAAC,MAAM,EAAE;;AAElB,MAAM,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnD,IAAI;AACJ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI;AACzB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,KAAK,EAAE;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB,EAAE;AAC5D,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;AAC3C,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;;AAEzC,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY;AAC1D,IAAI,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW;;AAEzD,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,OAAO;AAC5B,IAAI,MAAM,CAAC,GAAG,GAAG,GAAG,OAAO;;AAE3B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAClC,IAAI,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;;AAErE,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;AACrD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;;AAEtD,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;AACtC,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI;AACjE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI;AACvC,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG,IAAI;;AAEjE;AACA,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACjC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AAC7B,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AACb,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;;AAErB;AACA,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe;AAC9C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC;AACzB,MAAM,GAAG;AACT,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe;AAC1C,MAAM,OAAO;AACb,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC;AAClB,MAAM,GAAG;AACT,EAAE;AACF;;AC7wBA;AACA;AACA;AACA;AACA;AACA,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE;AAC5B,EAAE,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC;AAC7C,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,IAAI,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3C,EAAE,CAAC,MAAM;AACT,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,IAAI,OAAO,OAAO;AAClB,EAAE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;qBACO,MAAM,YAAY,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW;AACb,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,UAAU,GAAG,CAAC;AAClB,IAAI,UAAU,GAAG,MAAM,KAAK;AAC5B,IAAI;AACJ,IAAI,IAAI,CAAC,MAAM,GAAG,YAAY;AAC9B,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,GAAG,gBAAgB;AACrC,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU;;AAEhC,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE;AACrB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK;AAC5B,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC;AACzB,IAAI,IAAI,CAAC,cAAc,GAAG;AAC1B,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,SAAS,EAAE,SAAS;AAC1B,MAAM,UAAU,EAAE,IAAI;AACtB,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC;;AAEpD,IAAI,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AAC5C,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE;AAC3B,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE;AACtB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC;AACvB,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAIE,aAAW,CAAC,UAAU,CAAC;AAClD,IAAI,IAAI,CAAC,OAAO,GAAG,SAAS;AAC5B,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;AAC/B;AACA,MAAM,IAAI,CAAC,YAAY,GAAG,EAAE;AAC5B,MAAM,IAAI,CAAC,YAAY,EAAE;;AAEzB,MAAM,IAAI,OAAO,GAAG,IAAI;AACxB,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO;AACrC,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACzC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAE;AAC5C,MAAM,CAAC,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC9C,QAAQ,IAAI,OAAO,IAAI,IAAI,EAAE;AAC7B,UAAU,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC;AACvD,QAAQ;AACR,QAAQ,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AAC7C,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS;AACpD,QAAQ;AACR,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;AAC1C,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;AAC9C,QAAQ;AACR,QAAQ,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE;AAC9C,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU;AACtD,QAAQ;AACR,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;AAC3C,UAAU,OAAO,GAAG,OAAO,CAAC,OAAO;AACnC,QAAQ;AACR,MAAM,CAAC,MAAM,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;AAC/C,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI;AAClC,QAAQ,OAAO,GAAG,OAAO;AACzB,MAAM,CAAC,MAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AAChD,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO;AACrC,QAAQ,OAAO,GAAG,IAAI;AACtB,MAAM;AACN,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;AACzC,QAAQ,OAAO,GAAG,KAAK;AACvB,MAAM;;AAEN,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO;AACpC,IAAI;AACJ,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,aAAa,EAAE;AAClC,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa;AACtC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;AACvC,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AAChD,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;AAC/C,MAAM;AACN,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE;;AAE5B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;AACtC,IAAI,IAAI,OAAO,GAAG,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,KAAK;AACpB,IAAI,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAChD,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,EAAE;AAC/E,QAAQ,IAAI,CAAC,aAAa,GAAG,KAAK;AAClC,QAAQ,IAAI,GAAG,KAAK;AACpB,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC;AACnC,UAAU,IAAI;AACd,YAAY,IAAI;AAChB,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;AAC7E,QAAQ,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;AACrE,UAAU,IAAI,GAAG,IAAI;AACrB,QAAQ;;AAER,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;AAC5B,UAAU,IAAI,CAAC,aAAa,GAAG,IAAI;;AAEnC;AACA,UAAU,IAAI,OAAO,GAAG,CAAC,EAAE;AAC3B,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B,UAAU;AACV;AACA,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;;AAElC;AACA,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACrE,QAAQ;AACR,QAAQ,OAAO,EAAE;AACjB,MAAM;AACN,IAAI;AACJ,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAChD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,2BAA2B;AACxD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACnD,IAAI;;AAEJ,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtD,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACnD,IAAI;;AAEJ,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AACpC,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;AAC9C,MAAM,IAAI,CAAC,OAAO,GAAG,SAAS;AAC9B,IAAI;AACJ,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE;;AAEzB,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa;AACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AACvC,QAAQ,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,CAAC,MAAM;AACb,QAAQ,IAAI,GAAG,SAAS;AACxB,QAAQ;AACR,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,IAAI;AACf,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE,GAAG,WAAW,EAAE;AAClC,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;AACrC,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAChD,MAAM,IAAI,CAAC,SAAS;AACpB,QAAQ,gDAAgD,GAAG,IAAI,CAAC,MAAM;AACtE,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACvC,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACjC,MAAM,CAAC,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AACjC,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;AACpC,IAAI;AACJ,IAAI,OAAO,CAAC;AACZ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC7C,IAAI,GAAG,CAAC,SAAS,GAAG,qCAAqC;AACzD,IAAI,GAAG,CAAC,SAAS,GAAG,IAAI;AACxB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;AAC3B,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE;AAC9C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC7C,IAAI,GAAG,CAAC,SAAS;AACjB,MAAM,iDAAiD,GAAG,IAAI,CAAC,MAAM;AACrE,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;AAC9B,MAAM,OAAO,GAAG,CAAC,UAAU,EAAE;AAC7B,QAAQ,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC;AACvC,MAAM;AACN,MAAM,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAChD,IAAI,CAAC,MAAM;AACX,MAAM,GAAG,CAAC,SAAS,GAAG,IAAI,GAAG,GAAG;AAChC,IAAI;AACJ,IAAI,OAAO,GAAG;AACd,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AAClC,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACnD,IAAI,MAAM,CAAC,SAAS,GAAG,qCAAqC;AAC5D,IAAI,IAAI,aAAa,GAAG,CAAC;AACzB,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;AACrC,QAAQ,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C,MAAM;AACN,IAAI;;AAEJ,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACrD,MAAM,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC;AAC3B,MAAM,IAAI,CAAC,KAAK,aAAa,EAAE;AAC/B,QAAQ,MAAM,CAAC,QAAQ,GAAG,UAAU;AACpC,MAAM;AACN,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;AAC/B,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAChC,IAAI;;AAEJ,IAAI,MAAM,EAAE,GAAG,IAAI;AACnB,IAAI,MAAM,CAAC,QAAQ,GAAG,YAAY;AAClC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;AAClC,IAAI,CAAC;;AAEL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;AACvC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AAC/B,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;AAC/B,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACvB,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACjD,IAAI,KAAK,CAAC,SAAS,GAAG,oCAAoC;AAC1D,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;AAC3B,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG;AACrB,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG;AACrB,IAAI,CAAC,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,IAAI;AACJ,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI;;AAErB;AACA,IAAI,IAAI,WAAW,GAAG,EAAE;AACxB,IAAI,IAAI,UAAU,GAAG,CAAC;;AAEtB,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,MAAM,MAAM,GAAG,GAAG;AACxB,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;AAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG;AAC9B,QAAQ,WAAW,GAAG,iBAAiB;AACvC,MAAM,CAAC,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,EAAE;AACvC,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG;AAC9B,QAAQ,WAAW,GAAG,iBAAiB;AACvC,MAAM;AACN,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;AAC7C,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7C,QAAQ,UAAU,GAAG,KAAK,CAAC,GAAG;AAC9B,QAAQ,WAAW,GAAG,iBAAiB;AACvC,MAAM;AACN,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK;AACzB,IAAI,CAAC,MAAM;AACX,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY;AAChC,IAAI;;AAEJ,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACjD,IAAI,KAAK,CAAC,SAAS,GAAG,yCAAyC;AAC/D,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;;AAE7B,IAAI,MAAM,EAAE,GAAG,IAAI;AACnB,IAAI,KAAK,CAAC,QAAQ,GAAG,YAAY;AACjC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AAC9B,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;AAC1C,IAAI,CAAC;AACL,IAAI,KAAK,CAAC,OAAO,GAAG,YAAY;AAChC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AAC9B,IAAI,CAAC;;AAEL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC9D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;AAE/D;AACA,IAAI,IAAI,WAAW,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,UAAU,EAAE;AAC3E,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,UAAU;AAC/C,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC;AAC9C,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;AAC1C,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1D,MAAM,cAAc,CAAC,SAAS,GAAG,qCAAqC;AACtE,MAAM,cAAc,CAAC,SAAS,GAAG,kBAAkB;AACnD,MAAM,cAAc,CAAC,OAAO,GAAG,MAAM;AACrC,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,CAAC;AACP,MAAM,cAAc,CAAC,WAAW,GAAG,MAAM;AACzC,QAAQ,cAAc,CAAC,SAAS,GAAG,2CAA2C;AAC9E,MAAM,CAAC;AACP,MAAM,cAAc,CAAC,UAAU,GAAG,MAAM;AACxC,QAAQ,cAAc,CAAC,SAAS,GAAG,qCAAqC;AACxE,MAAM,CAAC;;AAEP,MAAM,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC3D,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS;AACrC,QAAQ,+CAA+C;;AAEvD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAClD,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;AAC3C,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE;AAC7B,IAAI;AACJ,MAAM,IAAI,CAAC,WAAW,KAAK,IAAI;AAC/B,MAAM,IAAI,CAAC,aAAa,KAAK,IAAI;AACjC,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC/B,MAAM;AACN,MAAM,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/C,MAAM,GAAG,CAAC,EAAE,GAAG,yBAAyB;AACxC,MAAM,GAAG,CAAC,SAAS,GAAG,yBAAyB;AAC/C,MAAM,GAAG,CAAC,SAAS,GAAG,MAAM;AAC5B,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;AAC1B,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,CAAC;AACP,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC;AAC5B,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE;AACjD,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnE,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC7C,MAAM,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC/C,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE;AACxB,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC1C,MAAM,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;AACxE,MAAM,MAAM,IAAI,GAAG,oBAAoB,CAAC,qBAAqB,EAAE;AAC/D,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI;AACtD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AAC1D,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnD,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAC,MAAM;AACnD,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;AAC5C,MAAM,CAAC,EAAE,IAAI,CAAC;AACd,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM;AACrD,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,CAAC,EAAE,IAAI,CAAC;AACd,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;AAC3C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACpD,IAAI,QAAQ,CAAC,IAAI,GAAG,UAAU;AAC9B,IAAI,QAAQ,CAAC,SAAS,GAAG,uCAAuC;AAChE,IAAI,QAAQ,CAAC,OAAO,GAAG,YAAY;AACnC,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,MAAM,QAAQ,CAAC,OAAO,GAAG,KAAK;AAC9B,MAAM,IAAI,KAAK,KAAK,YAAY,EAAE;AAClC,QAAQ,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAC9C,UAAU,IAAI,KAAK,KAAK,YAAY,CAAC,OAAO,EAAE;AAC9C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAClE,UAAU;AACV,QAAQ,CAAC,MAAM;AACf,UAAU,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAChE,QAAQ;AACR,MAAM;AACN,IAAI;;AAEJ,IAAI,MAAM,EAAE,GAAG,IAAI;AACnB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;AACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;AACpC,IAAI,CAAC;;AAEL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACpD,IAAI,QAAQ,CAAC,IAAI,GAAG,MAAM;AAC1B,IAAI,QAAQ,CAAC,SAAS,GAAG,mCAAmC;AAC5D,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK;AAC1B,IAAI,IAAI,KAAK,KAAK,YAAY,EAAE;AAChC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC5D,IAAI;;AAEJ,IAAI,MAAM,EAAE,GAAG,IAAI;AACnB,IAAI,QAAQ,CAAC,QAAQ,GAAG,YAAY;AACpC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC;AAClC,IAAI,CAAC;;AAEL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AACpC,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC;AAC/B,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC7C,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK;;AAEtD,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,MAAM,GAAG,CAAC,SAAS,GAAG,yCAAyC;AAC/D,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK;AACvC,IAAI,CAAC,MAAM;AACX,MAAM,GAAG,CAAC,SAAS,GAAG,8CAA8C;AACpE,IAAI;;AAEJ,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,YAAY,GAAG,KAAK;AACtD,IAAI,GAAG,CAAC,OAAO,GAAG,MAAM;AACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC;AAC7C,IAAI,CAAC;;AAEL,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AACpC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;AACrC;AACA,IAAI,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,CAAC;;AAEhC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;AAClC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;;AAE3B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;AACpC,IAAI,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,KAAK,KAAK;AAClD,MAAM,MAAM,WAAW;AACvB,QAAQ,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG;AAC/E,MAAM,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,WAAW;AAC7C,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;AACrC,IAAI,CAAC,CAAC;;AAEN;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM;AAC5C,MAAM,GAAG,CAAC,OAAO,GAAG,MAAM;AAC1B,QAAQ,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC;AAC/C,MAAM,CAAC;AACP,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,GAAG,KAAK,EAAE;AACnD,IAAI,IAAI,IAAI,GAAG,KAAK;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;AACtC,IAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,EAAE;AAC9B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE;AAC7D,QAAQ,IAAI,GAAG,IAAI;AACnB,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC;AAChC,QAAQ,MAAM,OAAO,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC;AACxD,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC1C,UAAU,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC;;AAErC;AACA,UAAU,IAAI,IAAI,KAAK,KAAK,EAAE;AAC9B,YAAY;AACZ,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAClC,cAAc,OAAO,IAAI,KAAK,QAAQ;AACtC,cAAc,OAAO,IAAI,KAAK,SAAS;AACvC,cAAc,IAAI,YAAY;AAC9B,cAAc;AACd,cAAc,IAAI,CAAC,aAAa,GAAG,KAAK;AACxC,cAAc,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;AAC5D,cAAc,IAAI,CAAC,aAAa,GAAG,SAAS,KAAK,KAAK;AACtD,YAAY;AACZ,UAAU;AACV,QAAQ;;AAER,QAAQ,IAAI,IAAI,KAAK,KAAK,EAAE;AAC5B,UAAU,YAAY,GAAG,IAAI;AAC7B,UAAU,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;;AAE/C,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACnC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;AACnD,UAAU,CAAC,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC/C,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;AACrD,UAAU,CAAC,MAAM,IAAI,OAAO,IAAI,KAAK,SAAS,EAAE;AAChD,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC;AACpD,UAAU,CAAC,MAAM,IAAI,IAAI,YAAY,MAAM,EAAE;AAC7C;AACA,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE;AACpE;AACA,cAAc,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,EAAE;AAC9C,gBAAgB,MAAM,WAAW,GAAG,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC;AAC1E,gBAAgB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AAChE,gBAAgB,IAAI,YAAY,KAAK,IAAI,EAAE;AAC3C,kBAAkB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AACtE,kBAAkB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC;AAChD,kBAAkB,YAAY;AAC9B,oBAAoB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY;AACrE,gBAAgB,CAAC,MAAM;AACvB,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,CAAC;AACjE,gBAAgB;AAChB,cAAc,CAAC,MAAM;AACrB,gBAAgB,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC;AACpE,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC;AAC9C,gBAAgB,YAAY;AAC5B,kBAAkB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,YAAY;AACnE,cAAc;AACd,YAAY;AACZ,UAAU,CAAC,MAAM;AACjB,YAAY,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC;AAC3E,UAAU;AACV,QAAQ;AACR,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,YAAY;AACvB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;AAC1D,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;AAC5C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC9D,MAAM;AACN,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;AAC1C,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC9D,MAAM;AACN,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC3C,MAAM,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;AACvC,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;AACtE,MAAM;AACN,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE;AACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC;;AAEvD,IAAI;AACJ,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;AACtB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;AAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/B,MAAM;AACN,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC;AAC5D,IAAI;AACJ,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI;AAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACnC,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE;AAClD,IAAI,IAAI,OAAO,GAAG,UAAU;;AAE5B;AACA,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK;AAC3C,IAAI,KAAK,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK;;AAE7C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAChC,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;AAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;AAC/B,QAAQ;AACR,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,UAAU,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpC,QAAQ,CAAC,MAAM;AACf,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;AAClC,QAAQ;AACR,MAAM;AACN,IAAI;AACJ,IAAI,OAAO,UAAU;AACrB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;;AAErC,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE;AAC7C,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACzE,IAAI;AACJ,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW;AACrC,MAAM,SAAS,CAAC,KAAK,EAAE,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7E,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,MAAM,IAAI,CAAC,iBAAiB;AAC5B,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK;AACpC,QAAQ,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI;AACnC,QAAQ,OAAO;AACf,OAAO;AACP,IAAI;AACJ,IAAI,OAAO,OAAO;AAClB,EAAE;AACF;;AC1wBA;AACA;AACA;cACO,MAAM,KAAK,CAAC;AACnB;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,SAAS,EAAE,cAAc,EAAE;AACzC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,KAAK;;AAEjD,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC;AACd,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEvB;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa;AACxC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1C,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;AACxB,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,IAAI,OAAO,YAAY,OAAO,EAAE;AACpC,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AACpC,QAAQ,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;AACrD,MAAM;AACN,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;AACrC,IAAI,CAAC,MAAM;AACX;AACA;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,OAAO;AACpC,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;AAC9B,MAAM,MAAM,GAAG,IAAI;AACnB,IAAI;;AAEJ,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;AACzB,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY;AAC5C,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW;AAC1C,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY;AAC1D,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW;;AAExD,MAAM,IAAI,IAAI,GAAG,CAAC;AAClB,QAAQ,GAAG,GAAG,CAAC;;AAEf,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,MAAM,EAAE;AACzC,QAAQ,IAAI,MAAM,GAAG,KAAK;AAC1B,UAAU,KAAK,GAAG,IAAI,CAAC;;AAEvB,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE;AAC5C,UAAU,KAAK,GAAG,KAAK;AACvB,QAAQ;;AAER,QAAQ,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE;AACtD,UAAU,MAAM,GAAG,IAAI;AACvB,QAAQ;;AAER,QAAQ,IAAI,MAAM,EAAE;AACpB,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,KAAK;AAC/B,QAAQ,CAAC,MAAM;AACf,UAAU,IAAI,GAAG,IAAI,CAAC,CAAC;AACvB,QAAQ;;AAER,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM;AAC/B,QAAQ,CAAC,MAAM;AACf,UAAU,GAAG,GAAG,IAAI,CAAC,CAAC;AACtB,QAAQ;AACR,MAAM,CAAC,MAAM;AACb,QAAQ,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM;AAC7B,QAAQ,IAAI,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE;AACrD,UAAU,GAAG,GAAG,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO;AACjD,QAAQ;AACR,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE;AAChC,UAAU,GAAG,GAAG,IAAI,CAAC,OAAO;AAC5B,QAAQ;;AAER,QAAQ,IAAI,GAAG,IAAI,CAAC,CAAC;AACrB,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE;AACpD,UAAU,IAAI,GAAG,QAAQ,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO;AAChD,QAAQ;AACR,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE;AACjC,UAAU,IAAI,GAAG,IAAI,CAAC,OAAO;AAC7B,QAAQ;AACR,MAAM;;AAEN,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI;AACzC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI;AACvC,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;AAC7C,MAAM,IAAI,CAAC,MAAM,GAAG,KAAK;AACzB,IAAI,CAAC,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,EAAE;AACjB,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AACtB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG;AAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;AAC1C,EAAE;;AAEF;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE;AACF;;ACnIA,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,UAAU;;AAEP,MAAMC,uBAAqB,GAAG,qCAAqC;;AAE1E;AACA;AACA;kBACO,MAAM,SAAS,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,QAAQ,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE;AACxD,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,UAAU,GAAG,gBAAgB;AACjC,IAAI,IAAI,WAAW,GAAG,gBAAgB;AACtC,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC;AAC/C,IAAI;AACJ,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC;AAC7C,IAAI,OAAO,UAAU;AACrB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;AAChD,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AACjE,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC;AAChE,MAAM;AACN,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,IAAI,EAAE;AACxD,IAAI;AACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;AAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK;AACnC,MAAM;AACN,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,gBAAgB,EAAE,IAAI,CAAC;AAC7D,MAAM;AACN,IAAI;;AAEJ,IAAI,IAAI,eAAe,GAAG,MAAM;AAChC,IAAI,IAAI,SAAS,GAAG,IAAI;;AAExB,IAAI;AACJ,MAAM,gBAAgB,CAAC,MAAM,CAAC,KAAK,SAAS;AAC5C,MAAM,gBAAgB,CAAC,OAAO,KAAK;AACnC,MAAM;AACN;AACA;AACA;;AAEA;AACA,MAAM,eAAe,GAAG,SAAS;;AAEjC;AACA;AACA,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,QAAQ;AACjE,IAAI;;AAMJ,IAAI,IAAI,YAAY,GAAG,gBAAgB,CAAC,eAAe,CAAC;AACxD,IAAI,IAAI,SAAS,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC1D,MAAM,YAAY,GAAG,YAAY,CAAC,QAAQ;AAC1C,IAAI;;AAEJ,IAAI,SAAS,CAAC,WAAW;AACzB,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,YAAY;AAClB,MAAM,IAAI;AACV,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW;AACpB,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,IAAI;AACR,IAAI;AACJ,IAAI,MAAM,GAAG,GAAG,UAAU,OAAO,EAAE;AACnC,MAAM,OAAO,CAAC,KAAK;AACnB,QAAQ,IAAI,GAAG,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;AAC9D,QAAQA,uBAAqB;AAC7B,OAAO;AACP,IAAI,CAAC;;AAEL,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACzD,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC;;AAElD,IAAI,IAAI,aAAa,KAAK,SAAS,EAAE;AACrC;AACA,MAAM;AACN,QAAQ,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,OAAO;AACpD,QAAQ,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;AACnD,QAAQ;AACR,QAAQ,GAAG;AACX,UAAU,8BAA8B;AACxC,YAAY,MAAM;AAClB,YAAY,IAAI;AAChB,YAAY,sBAAsB;AAClC,YAAY,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC;AAC1C,YAAY,QAAQ;AACpB,YAAY,OAAO,CAAC,MAAM,CAAC;AAC3B,YAAY,KAAK;AACjB,SAAS;AACT,QAAQ,UAAU,GAAG,IAAI;AACzB,MAAM,CAAC,MAAM,IAAI,UAAU,KAAK,QAAQ,IAAI,eAAe,KAAK,SAAS,EAAE;AAC3E,QAAQ,IAAI,GAAG,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC;AAC/C,QAAQ,SAAS,CAAC,KAAK;AACvB,UAAU,OAAO,CAAC,MAAM,CAAC;AACzB,UAAU,gBAAgB,CAAC,eAAe,CAAC;AAC3C,UAAU,IAAI;AACd,SAAS;AACT,MAAM;AACN,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;AAClD;AACA,MAAM,GAAG;AACT,QAAQ,6BAA6B;AACrC,UAAU,MAAM;AAChB,UAAU,eAAe;AACzB,UAAU,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACpD,UAAU,cAAc;AACxB,UAAU,UAAU;AACpB,UAAU,KAAK;AACf,UAAU,OAAO,CAAC,MAAM,CAAC;AACzB,UAAU,GAAG;AACb,OAAO;AACP,MAAM,UAAU,GAAG,IAAI;AACvB,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,IAAI,GAAG,OAAO,MAAM;;AAE9B,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC3B,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;AAC3B,QAAQ,OAAO,MAAM;AACrB,MAAM;AACN,MAAM,IAAI,MAAM,YAAY,OAAO,EAAE;AACrC,QAAQ,OAAO,SAAS;AACxB,MAAM;AACN,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;AACpC,QAAQ,OAAO,QAAQ;AACvB,MAAM;AACN,MAAM,IAAI,MAAM,YAAY,MAAM,EAAE;AACpC,QAAQ,OAAO,QAAQ;AACvB,MAAM;AACN,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,OAAO,OAAO;AACtB,MAAM;AACN,MAAM,IAAI,MAAM,YAAY,IAAI,EAAE;AAClC,QAAQ,OAAO,MAAM;AACrB,MAAM;AACN,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,SAAS,EAAE;AACzC,QAAQ,OAAO,KAAK;AACpB,MAAM;AACN,MAAM,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE;AAC5C,QAAQ,OAAO,QAAQ;AACvB,MAAM;AACN,MAAM,OAAO,QAAQ;AACrB,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,QAAQ;AACrB,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AACnC,MAAM,OAAO,SAAS;AACtB,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAClC,MAAM,OAAO,QAAQ;AACrB,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,SAAS,EAAE;AACnC,MAAM,OAAO,WAAW;AACxB,IAAI;AACJ,IAAI,OAAO,IAAI;AACf,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE;AAC9C,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC;AAC7E,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC;;AAE9E,IAAI,MAAM,oBAAoB,GAAG,CAAC;AAClC,IAAI,MAAM,qBAAqB,GAAG,CAAC;;AAEnC,IAAI,IAAI,GAAG;AACX,IAAI,IAAI,WAAW,CAAC,UAAU,KAAK,SAAS,EAAE;AAC9C,MAAM,GAAG;AACT,QAAQ,MAAM;AACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;AAC7D,QAAQ,4CAA4C;AACpD,QAAQ,WAAW,CAAC,UAAU;AAC9B,QAAQ,QAAQ;AAChB,IAAI,CAAC,MAAM;AACX,MAAM,YAAY,CAAC,QAAQ,IAAI,qBAAqB;AACpD,MAAM,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC;AAC1C,MAAM;AACN,MAAM,GAAG;AACT,QAAQ,MAAM;AACd,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC;AAC7D,QAAQ,sDAAsD;AAC9D,QAAQ,SAAS,CAAC,aAAa;AAC/B,UAAU,YAAY,CAAC,IAAI;AAC3B,UAAU,YAAY,CAAC,YAAY;AACnC,UAAU,EAAE;AACZ,SAAS;AACT,IAAI,CAAC,MAAM,IAAI,WAAW,CAAC,QAAQ,IAAI,oBAAoB,EAAE;AAC7D,MAAM,GAAG;AACT,QAAQ,kBAAkB;AAC1B,QAAQ,WAAW,CAAC,YAAY;AAChC,QAAQ,IAAI;AACZ,QAAQ,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;AACzD,IAAI,CAAC,MAAM;AACX,MAAM,GAAG;AACT,QAAQ,+BAA+B;AACvC,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAQ,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;AAC7C,IAAI;;AAEJ,IAAI,OAAO,CAAC,KAAK;AACjB,MAAM,8BAA8B,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG;AACzD,MAAMA,uBAAqB;AAC3B,KAAK;AACL,IAAI,UAAU,GAAG,IAAI;AACrB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE;AACjE,IAAI,IAAI,GAAG,GAAG,GAAG;AACjB,IAAI,IAAI,YAAY,GAAG,EAAE;AACzB,IAAI,IAAI,gBAAgB,GAAG,EAAE;AAC7B,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,WAAW,EAAE;AAChD,IAAI,IAAI,UAAU,GAAG,SAAS;AAC9B,IAAI,KAAK,MAAM,EAAE,IAAI,OAAO,EAAE;AAC9B,MAAM,IAAI,QAAQ;AAClB,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAI,SAAS,KAAK,IAAI,EAAE;AACpE,QAAQ,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa;AAC9C,UAAU,MAAM;AAChB,UAAU,OAAO,CAAC,EAAE,CAAC;AACrB,UAAU,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC;AACtC,SAAS;AACT,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE;AACnC,UAAU,YAAY,GAAG,MAAM,CAAC,YAAY;AAC5C,UAAU,gBAAgB,GAAG,MAAM,CAAC,IAAI;AACxC,UAAU,GAAG,GAAG,MAAM,CAAC,QAAQ;AAC/B,UAAU,UAAU,GAAG,MAAM,CAAC,UAAU;AACxC,QAAQ;AACR,MAAM,CAAC,MAAM;AACb,QAAQ,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,EAAE;AAC9D,UAAU,UAAU,GAAG,EAAE;AACzB,QAAQ;AACR,QAAQ,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC;AAC5D,QAAQ,IAAI,GAAG,GAAG,QAAQ,EAAE;AAC5B,UAAU,YAAY,GAAG,EAAE;AAC3B,UAAU,gBAAgB,GAAG,SAAS,CAAC,IAAI,CAAC;AAC5C,UAAU,GAAG,GAAG,QAAQ;AACxB,QAAQ;AACR,MAAM;AACN,IAAI;AACJ,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,YAAY;AAChC,MAAM,IAAI,EAAE,gBAAgB;AAC5B,MAAM,QAAQ,EAAE,GAAG;AACnB,MAAM,UAAU,EAAE,UAAU;AAC5B,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,4BAA4B,EAAE;AAC5E,IAAI,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,eAAe;AAC/C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,GAAG,IAAI,IAAI;AACnB,MAAM;AACN,MAAM,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO;AAC9B,IAAI;AACJ,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,GAAG,IAAI,IAAI;AACjB,IAAI;AACJ,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI;AACxB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,GAAG,IAAI,IAAI;AACnB,MAAM;AACN,MAAM,GAAG,IAAI,KAAK;AAClB,IAAI;AACJ,IAAI,OAAO,GAAG,GAAG,MAAM;AACvB,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,KAAK,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO;AACjC,OAAO,OAAO,CAAC,8BAA8B,EAAE,EAAE;AACjD,OAAO,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;AAC5B,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE;AACnC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM;AACvC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM;;AAEvC,IAAI,MAAM,MAAM,GAAG,EAAE;;AAErB;AACA,IAAI,IAAI,CAAC;AACT,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrB,IAAI;;AAEJ;AACA,IAAI,IAAI,CAAC;AACT,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACtB,IAAI;;AAEJ;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAChD,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7C,QAAQ,CAAC,MAAM;AACf,UAAU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG;AACjC,YAAY,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACpC,YAAY,IAAI,CAAC,GAAG;AACpB,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAClC,cAAc,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAClC,aAAa;AACb,WAAW,CAAC;AACZ,QAAQ;AACR,MAAM;AACN,IAAI;;AAEJ,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACrC,EAAE;AACF;;ACpZO,MAAM,SAAS,GAAQC;AACvB,MAAM,WAAW,GAAQC;AACzB,MAAM,YAAY,GAAQC;AAC1B,MAAM,MAAM,GAAiBC;AAC7B,MAAM,KAAK,GAAQC;AACnB,MAAM,qBAAqB,GAAWC;AACtC,MAAM,SAAS,GAAQC;;;;"}