/**
 * Use this symbol to delete properies in deepObjectAssign.
 */
export declare const DELETE: unique symbol;
/**
 * Turns `undefined` into `undefined | typeof DELETE` and makes everything
 * partial. Intended to be used with `deepObjectAssign`.
 */
export type Assignable<T> = T extends undefined ? (T extends Function ? T : T extends object ? {
    [Key in keyof T]?: Assignable<T[Key]> | undefined;
} : T) | typeof DELETE : T extends Function ? T | undefined : T extends object ? {
    [Key in keyof T]?: Assignable<T[Key]> | undefined;
} : T | undefined;
/**
 * Pure version of deepObjectAssign, it doesn't modify any of it's arguments.
 * @param base - The base object that fullfils the whole interface T.
 * @param updates - Updates that may change or delete props.
 * @returns A brand new instance with all the supplied objects deeply merged.
 */
export declare function pureDeepObjectAssign<T>(base: T, ...updates: Assignable<T>[]): T;
/**
 * Deep version of object assign with additional deleting by the DELETE symbol.
 * @param target - The object that will be augmented using the sources.
 * @param sources - Objects to be deeply merged into the target.
 * @returns The target (same instance).
 */
export declare function deepObjectAssign<T>(target: T, ...sources: Assignable<T>[]): T;
//# sourceMappingURL=deep-object-assign.d.ts.map