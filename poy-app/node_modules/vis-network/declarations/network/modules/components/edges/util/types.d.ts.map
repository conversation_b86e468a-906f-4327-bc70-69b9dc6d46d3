{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../../../../lib/network/modules/components/edges/util/types.ts"], "names": [], "mappings": "AAEA,MAAM,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;AACjC,MAAM,MAAM,WAAW,GAAG,GAAG,CAAC;AAC9B,MAAM,MAAM,KAAK,GAAG,GAAG,CAAC;AAExB,MAAM,WAAW,KAAK;IACpB,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACzB,SAAS,EAAE;QACT,UAAU,CAAC,WAAW,EAAE,OAAO,GAAG,KAAK,CAAC;KACzC,CAAC;IACF,OAAO,EAAE;QACP,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAC3C,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;KAC7C,CAAC;IACF,IAAI,EAAE;QACJ,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH;AAED,MAAM,MAAM,KAAK,GAAG,EAAE,CAAC;AAIvB,MAAM,WAAW,KAAK;IACpB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;CACX;AACD,MAAM,WAAW,MAAO,SAAQ,KAAK;IACnC,CAAC,EAAE,MAAM,CAAC;CACX;AAED,MAAM,MAAM,iBAAiB,CAC3B,CAAC,EACD,WAAW,SAAS,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,IAC1C,CAAC,GAAG;KAAG,GAAG,IAAI,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;CAAE,CAAC;AAEzE,MAAM,MAAM,SAAS,GACjB,OAAO,GACP,KAAK,GACL,KAAK,GACL,QAAQ,GACR,MAAM,GACN,OAAO,GACP,SAAS,GACT,OAAO,GACP,WAAW,GACX,cAAc,GACd,UAAU,GACV,KAAK,CAAC;AAEV,MAAM,MAAM,gBAAgB,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAEhE,MAAM,WAAW,WAAW;IAC1B,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,mBAAmB,CACjB,GAAG,EAAE,wBAAwB,EAC7B,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,KAAK,CAAC,EAAE,MAAM,EACd,MAAM,CAAC,EAAE,MAAM,GACd,IAAI,CAAC;CACT;AACD,MAAM,WAAW,SAAS;IACxB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,WAAW,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,KAAK,CAAC;IACb,IAAI,EAAE,SAAS,CAAC;CACjB;AACD,MAAM,WAAW,iBAAkB,SAAQ,SAAS;IAClD,IAAI,EAAE,KAAK,CAAC;CACb;AAED,MAAM,WAAW,oBAAoB;IACnC,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,gBAAgB,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC;IACtC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,MAAM,CAAC,EAAE,OAAO,GAAG,MAAM,EAAE,CAAC;IAC5B,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,CAAC,EAAE,SAAS,CAAC;IAC1B,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,aAAa,CAAC,EAAE,gBAAgB,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,eAAe,CAAC,EAAE,SAAS,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,WAAW,CAAC,EAAE,SAAS,CAAC;IACxB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,QAAQ;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IAEvB,IAAI,EAAE,KAAK,CAAC;IACZ,SAAS,EAAE,KAAK,CAAC;IACjB,EAAE,EAAE,KAAK,CAAC;IACV,OAAO,EAAE,KAAK,CAAC;IACf,GAAG,CAAC,EAAE,KAAK,CAAC;IACZ,QAAQ,CAAC,EAAE,KAAK,CAAC;IAEjB;;;;OAIG;IACH,mBAAmB,CAAC,GAAG,EAAE,wBAAwB,GAAG;QAClD,IAAI,EAAE,KAAK,CAAC;QACZ,EAAE,EAAE,KAAK,CAAC;KACX,CAAC;IAEF;;;OAGG;IACH,OAAO,IAAI,OAAO,CAAC;IAEnB;;OAEG;IACH,OAAO,IAAI,IAAI,CAAC;IAEhB;;;;;OAKG;IACH,QAAQ,CAAC,UAAU,EAAE,MAAM,GAAG,KAAK,CAAC;IAEpC;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI,CAAC;IAEvC;;;;;;;;;OASG;IACH,iBAAiB,CACf,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT,MAAM,CAAC;IAEV;;;;;;;;;OASG;IACH,QAAQ,CACN,GAAG,EAAE,wBAAwB,EAC7B,MAAM,EAAE,iBAAiB,CACvB,oBAAoB,EAClB,OAAO,GACP,SAAS,GACT,aAAa,GACb,YAAY,GACZ,SAAS,GACT,SAAS,GACT,OAAO,CACV,GACA,IAAI,CAAC;IAER;;;;;;;OAOG;IACH,aAAa,CACX,GAAG,EAAE,wBAAwB,EAC7B,MAAM,EAAE,iBAAiB,CACvB,oBAAoB,EACpB,SAAS,GAAG,aAAa,GAAG,YAAY,GAAG,SAAS,GAAG,SAAS,GAAG,OAAO,CAC3E,EACD,QAAQ,EAAE,OAAO,EACjB,KAAK,EAAE,OAAO,EACd,SAAS,EAAE,SAAS,GACnB,IAAI,CAAC;IAER;;;;;;;;;OASG;IACH,YAAY,CACV,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,QAAQ,EAClB,OAAO,EAAE,KAAK,EACd,QAAQ,EAAE,OAAO,EACjB,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,iBAAiB,CACvB,oBAAoB,EACpB,iBAAiB,GAAG,kBAAkB,GAAG,OAAO,CACjD,GACA,iBAAiB,CAAC;IACrB;;;;;;;;;OASG;IACH,YAAY,CACV,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,IAAI,EACd,OAAO,EAAE,KAAK,EACd,QAAQ,EAAE,OAAO,EACjB,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,iBAAiB,CACvB,oBAAoB,EACpB,aAAa,GAAG,cAAc,GAAG,OAAO,CACzC,GACA,iBAAiB,CAAC;IACrB;;;;;;;;;OASG;IACH,YAAY,CACV,GAAG,EAAE,wBAAwB,EAC7B,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,KAAK,EACd,QAAQ,EAAE,OAAO,EACjB,KAAK,EAAE,OAAO,EACd,MAAM,EAAE,iBAAiB,CACvB,oBAAoB,EACpB,eAAe,GAAG,gBAAgB,GAAG,OAAO,CAC7C,GACA,iBAAiB,CAAC;CACtB"}