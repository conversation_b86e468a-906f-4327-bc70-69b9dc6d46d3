import "./network/modules/ManipulationSystem.css";
import "./network/modules/components/NavigationHandler.css";
import "vis-util/esnext/styles/activator.css";
import "vis-util/esnext/styles/bootstrap.css";
import "vis-util/esnext/styles/color-picker.css";
import "vis-util/esnext/styles/configurator.css";
import "vis-util/esnext/styles/popup.css";
export * from "./network/Network.js";
import * as gephiParser from "./network/gephiParser.js";
import * as allOptions from "./network/options.js";
export declare const network: {
    Images: any;
    dotparser: any;
    gephiParser: typeof gephiParser;
    allOptions: typeof allOptions;
    convertDot: any;
    convertGephi: typeof gephiParser.parseGephi;
};
import * as DOMutil from "./DOMutil.js";
export { DOMutil };
import * as util from "vis-util/esnext";
export { util };
import * as data from "vis-data/esnext";
export { data };
export { DataSet, DataView, Queue } from "vis-data/esnext";
import { Hammer } from "vis-util/esnext";
export { Hammer };
import * as keycharm from "keycharm";
export { keycharm };
//# sourceMappingURL=index-legacy.d.ts.map