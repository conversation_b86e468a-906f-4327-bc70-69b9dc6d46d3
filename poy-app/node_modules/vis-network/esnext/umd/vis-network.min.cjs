/**
 * vis-network
 * https://visjs.github.io/vis-network/
 *
 * A dynamic, browser-based visualization library.
 *
 * @version 10.0.1
 * @date    2025-07-13T08:15:32.270Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("component-emitter"),require("vis-util/esnext/umd/vis-util.js"),require("vis-data/esnext/umd/vis-data.js"),require("uuid"),require("keycharm")):"function"==typeof define&&define.amd?define(["exports","component-emitter","vis-util/esnext/umd/vis-util.js","vis-data/esnext/umd/vis-data.js","uuid","keycharm"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{},t.Emitter,t.vis,t.vis,t.uuid,t.keycharm)}(this,function(t,e,i,o,s,n){function r(t,e,i,o){t.beginPath(),t.arc(e,i,o,0,2*Math.PI,!1),t.closePath()}function d(t,e,i,o,s,n){const r=Math.PI/180;o-2*n<0&&(n=o/2),s-2*n<0&&(n=s/2),t.beginPath(),t.moveTo(e+n,i),t.lineTo(e+o-n,i),t.arc(e+o-n,i+n,n,270*r,360*r,!1),t.lineTo(e+o,i+s-n),t.arc(e+o-n,i+s-n,n,0,90*r,!1),t.lineTo(e+n,i+s),t.arc(e+n,i+s-n,n,90*r,180*r,!1),t.lineTo(e,i+n),t.arc(e+n,i+n,n,180*r,270*r,!1),t.closePath()}function a(t,e,i,o,s){const n=.5522848,r=o/2*n,d=s/2*n,a=e+o,h=i+s,l=e+o/2,c=i+s/2;t.beginPath(),t.moveTo(e,c),t.bezierCurveTo(e,c-d,l-r,i,l,i),t.bezierCurveTo(l+r,i,a,c-d,a,c),t.bezierCurveTo(a,c+d,l+r,h,l,h),t.bezierCurveTo(l-r,h,e,c+d,e,c),t.closePath()}function h(t,e,i,o,s){const n=s*(1/3),r=.5522848,d=o/2*r,a=n/2*r,h=e+o,l=i+n,c=e+o/2,p=i+n/2,u=i+(s-n/2),g=i+s;t.beginPath(),t.moveTo(h,p),t.bezierCurveTo(h,p+a,c+d,l,c,l),t.bezierCurveTo(c-d,l,e,p+a,e,p),t.bezierCurveTo(e,p-a,c-d,i,c,i),t.bezierCurveTo(c+d,i,h,p-a,h,p),t.lineTo(h,u),t.bezierCurveTo(h,u+a,c+d,g,c,g),t.bezierCurveTo(c-d,g,e,u+a,e,u),t.lineTo(e,p)}function l(t,e,i,o,s,n){t.beginPath(),t.moveTo(e,i);const r=n.length,d=o-e,a=s-i,h=a/d;let l=Math.sqrt(d*d+a*a),c=0,p=!0,u=0,g=+n[0];for(;l>=.1;)g=+n[c++%r],g>l&&(g=l),u=Math.sqrt(g*g/(1+h*h)),u=d<0?-u:u,e+=u,i+=h*u,!0===p?t.lineTo(e,i):t.moveTo(e,i),l-=g,p=!p}const c={circle:r,dashedLine:l,database:h,diamond:function(t,e,i,o){t.beginPath(),t.lineTo(e,i+o),t.lineTo(e+o,i),t.lineTo(e,i-o),t.lineTo(e-o,i),t.closePath()},ellipse:a,ellipse_vis:a,hexagon:function(t,e,i,o){t.beginPath();const s=2*Math.PI/6;t.moveTo(e+o,i);for(let n=1;n<6;n++)t.lineTo(e+o*Math.cos(s*n),i+o*Math.sin(s*n));t.closePath()},roundRect:d,square:function(t,e,i,o){t.beginPath(),t.rect(e-o,i-o,2*o,2*o),t.closePath()},star:function(t,e,i,o){t.beginPath(),i+=.1*(o*=.82);for(let s=0;s<10;s++){const n=s%2==0?1.3*o:.5*o;t.lineTo(e+n*Math.sin(2*s*Math.PI/10),i-n*Math.cos(2*s*Math.PI/10))}t.closePath()},triangle:function(t,e,i,o){t.beginPath(),i+=.275*(o*=1.15);const s=2*o,n=s/2,r=Math.sqrt(3)/6*s,d=Math.sqrt(s*s-n*n);t.moveTo(e,i-(d-r)),t.lineTo(e+n,i+r),t.lineTo(e-n,i+r),t.lineTo(e,i-(d-r)),t.closePath()},triangleDown:function(t,e,i,o){t.beginPath(),i-=.275*(o*=1.15);const s=2*o,n=s/2,r=Math.sqrt(3)/6*s,d=Math.sqrt(s*s-n*n);t.moveTo(e,i+(d-r)),t.lineTo(e+n,i-r),t.lineTo(e-n,i-r),t.lineTo(e,i+(d-r)),t.closePath()}};function p(t){return f=t,function(){var t={};y=0,void(v=f.charAt(0)),z(),"strict"===w&&(t.strict=!0,z());"graph"!==w&&"digraph"!==w||(t.type=w,z());x===b.IDENTIFIER&&(t.id=w,z());if("{"!=w)throw F("Angle bracket { expected");if(z(),T(t),"}"!=w)throw F("Angle bracket } expected");if(z(),""!==w)throw F("End of file expected");return z(),delete t.node,delete t.edge,delete t.graph,t}()}var u={fontsize:"font.size",fontcolor:"font.color",labelfontcolor:"font.color",fontname:"font.face",color:["color.border","color.background"],fillcolor:"color.background",tooltip:"title",labeltooltip:"title"},g=Object.create(u);g.color="color.color",g.style="dashes";var b={NULL:0,DELIMITER:1,IDENTIFIER:2,UNKNOWN:3},m={"{":!0,"}":!0,"[":!0,"]":!0,";":!0,"=":!0,",":!0,"->":!0,"--":!0},f="",y=0,v="",w="",x=b.NULL;function _(){y++,v=f.charAt(y)}function E(){return f.charAt(y+1)}function O(t){var e=t.charCodeAt(0);return e<47?35===e||46===e:e<59?e>47:e<91?e>64:e<96?95===e:e<123&&e>96}function M(t,e){if(t||(t={}),e)for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function C(t,e,i){for(var o=e.split("."),s=t;o.length;){var n=o.shift();o.length?(s[n]||(s[n]={}),s=s[n]):s[n]=i}}function S(t,e){for(var i,o,s=null,n=[t],r=t;r.parent;)n.push(r.parent),r=r.parent;if(r.nodes)for(i=0,o=r.nodes.length;i<o;i++)if(e.id===r.nodes[i].id){s=r.nodes[i];break}for(s||(s={id:e.id},t.node&&(s.attr=M(s.attr,t.node))),i=n.length-1;i>=0;i--){var d=n[i];d.nodes||(d.nodes=[]),-1===d.nodes.indexOf(s)&&d.nodes.push(s)}e.attr&&(s.attr=M(s.attr,e.attr))}function k(t,e){if(t.edges||(t.edges=[]),t.edges.push(e),t.edge){var i=M({},t.edge);e.attr=M(i,e.attr)}}function I(t,e,i,o,s){var n={from:e,to:i,type:o};return t.edge&&(n.attr=M({},t.edge)),n.attr=M(n.attr||{},s),null!=s&&s.hasOwnProperty("arrows")&&null!=s.arrows&&(n.arrows={to:{enabled:!0,type:s.arrows.type}},s.arrows=null),n}function z(){for(x=b.NULL,w="";" "===v||"\t"===v||"\n"===v||"\r"===v;)_();do{var t=!1;if("#"===v){for(var e=y-1;" "===f.charAt(e)||"\t"===f.charAt(e);)e--;if("\n"===f.charAt(e)||""===f.charAt(e)){for(;""!=v&&"\n"!=v;)_();t=!0}}if("/"===v&&"/"===E()){for(;""!=v&&"\n"!=v;)_();t=!0}if("/"===v&&"*"===E()){for(;""!=v;){if("*"===v&&"/"===E()){_(),_();break}_()}t=!0}for(;" "===v||"\t"===v||"\n"===v||"\r"===v;)_()}while(t);if(""!==v){var i=v+E();if(m[i])return x=b.DELIMITER,w=i,_(),void _();if(m[v])return x=b.DELIMITER,w=v,void _();if(O(v)||"-"===v){for(w+=v,_();O(v);)w+=v,_();return"false"===w?w=!1:"true"===w?w=!0:isNaN(Number(w))||(w=Number(w)),void(x=b.IDENTIFIER)}if('"'===v){for(_();""!=v&&('"'!=v||'"'===v&&'"'===E());)'"'===v?(w+=v,_()):"\\"===v&&"n"===E()?(w+="\n",_()):w+=v,_();if('"'!=v)throw F('End of string " expected');return _(),void(x=b.IDENTIFIER)}for(x=b.UNKNOWN;""!=v;)w+=v,_();throw new SyntaxError('Syntax error in part "'+R(w,30)+'"')}x=b.DELIMITER}function T(t){for(;""!==w&&"}"!=w;)D(t),";"===w&&z()}function D(t){var e=B(t);if(e)N(t,e);else{var i=function(t){if("node"===w)return z(),t.node=P(),"node";if("edge"===w)return z(),t.edge=P(),"edge";if("graph"===w)return z(),t.graph=P(),"graph";return null}(t);if(!i){if(x!=b.IDENTIFIER)throw F("Identifier expected");var o=w;if(z(),"="===w){if(z(),x!=b.IDENTIFIER)throw F("Identifier expected");t[o]=w,z()}else!function(t,e){var i={id:e},o=P();o&&(i.attr=o);S(t,i),N(t,e)}(t,o)}}}function B(t){var e=null;if("subgraph"===w&&((e={}).type="subgraph",z(),x===b.IDENTIFIER&&(e.id=w,z())),"{"===w){if(z(),e||(e={}),e.parent=t,e.node=t.node,e.edge=t.edge,e.graph=t.graph,T(e),"}"!=w)throw F("Angle bracket } expected");z(),delete e.node,delete e.edge,delete e.graph,delete e.parent,t.subgraphs||(t.subgraphs=[]),t.subgraphs.push(e)}return e}function N(t,e){for(;"->"===w||"--"===w;){var i,o=w;z();var s=B(t);if(s)i=s;else{if(x!=b.IDENTIFIER)throw F("Identifier or subgraph expected");S(t,{id:i=w}),z()}k(t,I(t,e,i,o,P())),e=i}}function P(){for(var t,e,i=null,o={dashed:!0,solid:!1,dotted:[1,5]},s={dot:"circle",box:"box",crow:"crow",curve:"curve",icurve:"inv_curve",normal:"triangle",inv:"inv_triangle",diamond:"diamond",tee:"bar",vee:"vee"},n=new Array,r=new Array;"["===w;){for(z(),i={};""!==w&&"]"!=w;){if(x!=b.IDENTIFIER)throw F("Attribute name expected");var d=w;if(z(),"="!=w)throw F("Equal sign = expected");if(z(),x!=b.IDENTIFIER)throw F("Attribute value expected");var a=w;"style"===d&&(a=o[a]),"arrowhead"===d&&(d="arrows",a={to:{enabled:!0,type:s[a]}}),"arrowtail"===d&&(d="arrows",a={from:{enabled:!0,type:s[a]}}),n.push({attr:i,name:d,value:a}),r.push(d),z(),","==w&&z()}if("]"!=w)throw F("Bracket ] expected");z()}if(r.includes("dir")){var h={arrows:{}};for(t=0;t<n.length;t++)if("arrows"===n[t].name)if(null!=n[t].value.to)h.arrows.to=t;else{if(null==n[t].value.from)throw F("Invalid value of arrows");h.arrows.from=t}else"dir"===n[t].name&&(h.dir=t);var l,c,p=n[h.dir].value;if(!r.includes("arrows"))if("both"===p)n.push({attr:n[h.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),h.arrows.to=n.length-1,n.push({attr:n[h.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),h.arrows.from=n.length-1;else if("forward"===p)n.push({attr:n[h.dir].attr,name:"arrows",value:{to:{enabled:!0}}}),h.arrows.to=n.length-1;else if("back"===p)n.push({attr:n[h.dir].attr,name:"arrows",value:{from:{enabled:!0}}}),h.arrows.from=n.length-1;else{if("none"!==p)throw F('Invalid dir type "'+p+'"');n.push({attr:n[h.dir].attr,name:"arrows",value:""}),h.arrows.to=n.length-1}if("both"===p)h.arrows.to&&h.arrows.from?(c=n[h.arrows.to].value.to.type,l=n[h.arrows.from].value.from.type,n[h.arrows.to]={attr:n[h.arrows.to].attr,name:n[h.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}},n.splice(h.arrows.from,1)):h.arrows.to?(c=n[h.arrows.to].value.to.type,l="arrow",n[h.arrows.to]={attr:n[h.arrows.to].attr,name:n[h.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):h.arrows.from&&(c="arrow",l=n[h.arrows.from].value.from.type,n[h.arrows.from]={attr:n[h.arrows.from].attr,name:n[h.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}});else if("back"===p)h.arrows.to&&h.arrows.from?(c="",l=n[h.arrows.from].value.from.type,n[h.arrows.from]={attr:n[h.arrows.from].attr,name:n[h.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):h.arrows.to?(c="",l="arrow",h.arrows.from=h.arrows.to,n[h.arrows.from]={attr:n[h.arrows.from].attr,name:n[h.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):h.arrows.from&&(c="",l=n[h.arrows.from].value.from.type,n[h.arrows.to]={attr:n[h.arrows.from].attr,name:n[h.arrows.from].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),n[h.arrows.from]={attr:n[h.arrows.from].attr,name:n[h.arrows.from].name,value:{from:{enabled:!0,type:n[h.arrows.from].value.from.type}}};else if("none"===p){var u;n[u=h.arrows.to?h.arrows.to:h.arrows.from]={attr:n[u].attr,name:n[u].name,value:""}}else{if("forward"!==p)throw F('Invalid dir type "'+p+'"');h.arrows.to&&h.arrows.from||h.arrows.to?(c=n[h.arrows.to].value.to.type,l="",n[h.arrows.to]={attr:n[h.arrows.to].attr,name:n[h.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}):h.arrows.from&&(c="arrow",l="",h.arrows.to=h.arrows.from,n[h.arrows.to]={attr:n[h.arrows.to].attr,name:n[h.arrows.to].name,value:{to:{enabled:!0,type:c},from:{enabled:!0,type:l}}}),n[h.arrows.to]={attr:n[h.arrows.to].attr,name:n[h.arrows.to].name,value:{to:{enabled:!0,type:n[h.arrows.to].value.to.type}}}}n.splice(h.dir,1)}if(r.includes("penwidth")){var g=[];for(e=n.length,t=0;t<e;t++)"width"!==n[t].name&&("penwidth"===n[t].name&&(n[t].name="width"),g.push(n[t]));n=g}for(e=n.length,t=0;t<e;t++)C(n[t].attr,n[t].name,n[t].value);return i}function F(t){return new SyntaxError(t+', got "'+R(w,30)+'" (char '+y+")")}function R(t,e){return t.length<=e?t:t.substr(0,27)+"..."}function A(t,e,i){for(var o=e.split("."),s=o.pop(),n=t,r=0;r<o.length;r++){var d=o[r];d in n||(n[d]={}),n=n[d]}return n[s]=i,t}function j(t,e){var i={};for(var o in t)if(t.hasOwnProperty(o)){var s=e[o];Array.isArray(s)?s.forEach(function(e){A(i,e,t[o])}):A(i,"string"==typeof s?s:o,t[o])}return i}function L(t){var e=p(t),i={nodes:[],edges:[],options:{}};if(e.nodes&&e.nodes.forEach(function(t){var e={id:t.id,label:String(t.label||t.id)};M(e,j(t.attr,u)),e.image&&(e.shape="image"),i.nodes.push(e)}),e.edges){var o=function(t){var e={from:t.from,to:t.to};return M(e,j(t.attr,g)),null==e.arrows&&"->"===t.type&&(e.arrows="to"),e};e.edges.forEach(function(t){var e,s,n,r,d;e=t.from instanceof Object?t.from.nodes:{id:t.from},s=t.to instanceof Object?t.to.nodes:{id:t.to},t.from instanceof Object&&t.from.edges&&t.from.edges.forEach(function(t){var e=o(t);i.edges.push(e)}),n=e,r=s,d=function(e,s){var n=I(i,e.id,s.id,t.type,t.attr),r=o(n);i.edges.push(r)},Array.isArray(n)?n.forEach(function(t){Array.isArray(r)?r.forEach(function(e){d(t,e)}):d(t,r)}):Array.isArray(r)?r.forEach(function(t){d(n,t)}):d(n,r),t.to instanceof Object&&t.to.edges&&t.to.edges.forEach(function(t){var e=o(t);i.edges.push(e)})})}return e.attr&&(i.options=e.attr),i}var H=Object.freeze({__proto__:null,DOTToGraph:L,parseDOT:p});function W(t,e){const i={edges:{inheritColor:!1},nodes:{fixed:!1,parseColor:!1}};null!=e&&(null!=e.fixed&&(i.nodes.fixed=e.fixed),null!=e.parseColor&&(i.nodes.parseColor=e.parseColor),null!=e.inheritColor&&(i.edges.inheritColor=e.inheritColor));const o=t.edges.map(t=>{const e={from:t.source,id:t.id,to:t.target};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),"Directed"===t.type&&(e.arrows="to"),t.color&&!1===i.edges.inheritColor&&(e.color=t.color),e});return{nodes:t.nodes.map(t=>{const e={id:t.id,fixed:i.nodes.fixed&&null!=t.x&&null!=t.y};return null!=t.attributes&&(e.attributes=t.attributes),null!=t.label&&(e.label=t.label),null!=t.size&&(e.size=t.size),null!=t.attributes&&null!=t.attributes.title&&(e.title=t.attributes.title),null!=t.title&&(e.title=t.title),null!=t.x&&(e.x=t.x),null!=t.y&&(e.y=t.y),null!=t.color&&(!0===i.nodes.parseColor?e.color=t.color:e.color={background:t.color,border:t.color,highlight:{background:t.color,border:t.color},hover:{background:t.color,border:t.color}}),e}),edges:o}}var V=Object.freeze({__proto__:null,parseGephi:W});var q=Object.freeze({__proto__:null,cn:{addDescription:"单击空白处放置新节点。",addEdge:"添加连接线",addNode:"添加节点",back:"返回",close:"關閉",createEdgeError:"无法将连接线连接到群集。",del:"删除选定",deleteClusterError:"无法删除群集。",edgeDescription:"单击某个节点并将该连接线拖动到另一个节点以连接它们。",edit:"编辑",editClusterError:"无法编辑群集。",editEdge:"编辑连接线",editEdgeDescription:"单击控制节点并将它们拖到节点上连接。",editNode:"编辑节点"},cs:{addDescription:"Kluknutím do prázdného prostoru můžete přidat nový vrchol.",addEdge:"Přidat hranu",addNode:"Přidat vrchol",back:"Zpět",close:"Zavřít",createEdgeError:"Nelze připojit hranu ke shluku.",del:"Smazat výběr",deleteClusterError:"Nelze mazat shluky.",edgeDescription:"Přetažením z jednoho vrcholu do druhého můžete spojit tyto vrcholy novou hranou.",edit:"Upravit",editClusterError:"Nelze upravovat shluky.",editEdge:"Upravit hranu",editEdgeDescription:"Přetažením kontrolního vrcholu hrany ji můžete připojit k jinému vrcholu.",editNode:"Upravit vrchol"},de:{addDescription:"Klicke auf eine freie Stelle, um einen neuen Knoten zu plazieren.",addEdge:"Kante hinzufügen",addNode:"Knoten hinzufügen",back:"Zurück",close:"Schließen",createEdgeError:"Es ist nicht möglich, Kanten mit Clustern zu verbinden.",del:"Lösche Auswahl",deleteClusterError:"Cluster können nicht gelöscht werden.",edgeDescription:"Klicke auf einen Knoten und ziehe die Kante zu einem anderen Knoten, um diese zu verbinden.",edit:"Editieren",editClusterError:"Cluster können nicht editiert werden.",editEdge:"Kante editieren",editEdgeDescription:"Klicke auf die Verbindungspunkte und ziehe diese auf einen Knoten, um sie zu verbinden.",editNode:"Knoten editieren"},en:{addDescription:"Click in an empty space to place a new node.",addEdge:"Add Edge",addNode:"Add Node",back:"Back",close:"Close",createEdgeError:"Cannot link edges to a cluster.",del:"Delete selected",deleteClusterError:"Clusters cannot be deleted.",edgeDescription:"Click on a node and drag the edge to another node to connect them.",edit:"Edit",editClusterError:"Clusters cannot be edited.",editEdge:"Edit Edge",editEdgeDescription:"Click on the control points and drag them to a node to connect to it.",editNode:"Edit Node"},es:{addDescription:"Haga clic en un lugar vacío para colocar un nuevo nodo.",addEdge:"Añadir arista",addNode:"Añadir nodo",back:"Atrás",close:"Cerrar",createEdgeError:"No se puede conectar una arista a un grupo.",del:"Eliminar selección",deleteClusterError:"No es posible eliminar grupos.",edgeDescription:"Haga clic en un nodo y arrastre la arista hacia otro nodo para conectarlos.",edit:"Editar",editClusterError:"No es posible editar grupos.",editEdge:"Editar arista",editEdgeDescription:"Haga clic en un punto de control y arrastrelo a un nodo para conectarlo.",editNode:"Editar nodo"},fr:{addDescription:"Cliquez dans un endroit vide pour placer un nœud.",addEdge:"Ajouter un lien",addNode:"Ajouter un nœud",back:"Retour",close:"Fermer",createEdgeError:"Impossible de créer un lien vers un cluster.",del:"Effacer la sélection",deleteClusterError:"Les clusters ne peuvent pas être effacés.",edgeDescription:"Cliquez sur un nœud et glissez le lien vers un autre nœud pour les connecter.",edit:"Éditer",editClusterError:"Les clusters ne peuvent pas être édités.",editEdge:"Éditer le lien",editEdgeDescription:"Cliquez sur les points de contrôle et glissez-les pour connecter un nœud.",editNode:"Éditer le nœud"},it:{addDescription:"Clicca per aggiungere un nuovo nodo",addEdge:"Aggiungi un vertice",addNode:"Aggiungi un nodo",back:"Indietro",close:"Chiudere",createEdgeError:"Non si possono collegare vertici ad un cluster",del:"Cancella la selezione",deleteClusterError:"I cluster non possono essere cancellati",edgeDescription:"Clicca su un nodo e trascinalo ad un altro nodo per connetterli.",edit:"Modifica",editClusterError:"I clusters non possono essere modificati.",editEdge:"Modifica il vertice",editEdgeDescription:"Clicca sui Punti di controllo e trascinali ad un nodo per connetterli.",editNode:"Modifica il nodo"},nl:{addDescription:"Klik op een leeg gebied om een nieuwe node te maken.",addEdge:"Link toevoegen",addNode:"Node toevoegen",back:"Terug",close:"Sluiten",createEdgeError:"Kan geen link maken naar een cluster.",del:"Selectie verwijderen",deleteClusterError:"Clusters kunnen niet worden verwijderd.",edgeDescription:"Klik op een node en sleep de link naar een andere node om ze te verbinden.",edit:"Wijzigen",editClusterError:"Clusters kunnen niet worden aangepast.",editEdge:"Link wijzigen",editEdgeDescription:"Klik op de verbindingspunten en sleep ze naar een node om daarmee te verbinden.",editNode:"Node wijzigen"},pt:{addDescription:"Clique em um espaço em branco para adicionar um novo nó",addEdge:"Adicionar aresta",addNode:"Adicionar nó",back:"Voltar",close:"Fechar",createEdgeError:"Não foi possível linkar arestas a um cluster.",del:"Remover selecionado",deleteClusterError:"Clusters não puderam ser removidos.",edgeDescription:"Clique em um nó e arraste a aresta até outro nó para conectá-los",edit:"Editar",editClusterError:"Clusters não puderam ser editados.",editEdge:"Editar aresta",editEdgeDescription:"Clique nos pontos de controle e os arraste para um nó para conectá-los",editNode:"Editar nó"},ru:{addDescription:"Кликните в свободное место, чтобы добавить новый узел.",addEdge:"Добавить ребро",addNode:"Добавить узел",back:"Назад",close:"Закрывать",createEdgeError:"Невозможно соединить ребра в кластер.",del:"Удалить выбранное",deleteClusterError:"Кластеры не могут быть удалены",edgeDescription:"Кликните на узел и протяните ребро к другому узлу, чтобы соединить их.",edit:"Редактировать",editClusterError:"Кластеры недоступны для редактирования.",editEdge:"Редактировать ребро",editEdgeDescription:"Кликните на контрольные точки и перетащите их в узел, чтобы подключиться к нему.",editNode:"Редактировать узел"},uk:{addDescription:"Kлікніть на вільне місце, щоб додати новий вузол.",addEdge:"Додати край",addNode:"Додати вузол",back:"Назад",close:"Закрити",createEdgeError:"Не можливо об'єднати краї в групу.",del:"Видалити обране",deleteClusterError:"Групи не можуть бути видалені.",edgeDescription:"Клікніть на вузол і перетягніть край до іншого вузла, щоб їх з'єднати.",edit:"Редагувати",editClusterError:"Групи недоступні для редагування.",editEdge:"Редагувати край",editEdgeDescription:"Клікніть на контрольні точки і перетягніть їх у вузол, щоб підключитися до нього.",editNode:"Редагувати вузол"}});class U{constructor(){this.NUM_ITERATIONS=4,this.image=new Image,this.canvas=document.createElement("canvas")}init(){if(this.initialized())return;this.src=this.image.src;const t=this.image.width,e=this.image.height;this.width=t,this.height=e;const i=Math.floor(e/2),o=Math.floor(e/4),s=Math.floor(e/8),n=Math.floor(e/16),r=Math.floor(t/2),d=Math.floor(t/4),a=Math.floor(t/8),h=Math.floor(t/16);this.canvas.width=3*d,this.canvas.height=i,this.coordinates=[[0,0,r,i],[r,0,d,o],[r,o,a,s],[5*a,o,h,n]],this._fillMipMap()}initialized(){return void 0!==this.coordinates}_fillMipMap(){const t=this.canvas.getContext("2d"),e=this.coordinates[0];t.drawImage(this.image,e[0],e[1],e[2],e[3]);for(let e=1;e<this.NUM_ITERATIONS;e++){const i=this.coordinates[e-1],o=this.coordinates[e];t.drawImage(this.canvas,i[0],i[1],i[2],i[3],o[0],o[1],o[2],o[3])}}drawImageAtPosition(t,e,i,o,s,n){if(this.initialized())if(e>2){e*=.5;let r=0;for(;e>2&&r<this.NUM_ITERATIONS;)e*=.5,r+=1;r>=this.NUM_ITERATIONS&&(r=this.NUM_ITERATIONS-1);const d=this.coordinates[r];t.drawImage(this.canvas,d[0],d[1],d[2],d[3],i,o,s,n)}else t.drawImage(this.image,i,o,s,n)}}class Y{constructor(t){this.images={},this.imageBroken={},this.callback=t}_tryloadBrokenUrl(t,e,i){void 0!==t&&void 0!==i&&(void 0!==e?(i.image.onerror=()=>{console.error("Could not load brokenImage:",e)},i.image.src=e):console.warn("No broken url image defined"))}_redrawWithImage(t){this.callback&&this.callback(t)}load(t,e){const i=this.images[t];if(i)return i;const o=new U;return this.images[t]=o,o.image.onload=()=>{this._fixImageCoordinates(o.image),o.init(),this._redrawWithImage(o)},o.image.onerror=()=>{console.error("Could not load image:",t),this._tryloadBrokenUrl(t,e,o)},o.image.src=t,o}_fixImageCoordinates(t){0===t.width&&(document.body.appendChild(t),t.width=t.offsetWidth,t.height=t.offsetHeight,document.body.removeChild(t))}}class X{constructor(){this.clear(),this._defaultIndex=0,this._groupIndex=0,this._defaultGroups=[{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},{border:"#FFA500",background:"#FFFF00",highlight:{border:"#FFA500",background:"#FFFFA3"},hover:{border:"#FFA500",background:"#FFFFA3"}},{border:"#FA0A10",background:"#FB7E81",highlight:{border:"#FA0A10",background:"#FFAFB1"},hover:{border:"#FA0A10",background:"#FFAFB1"}},{border:"#41A906",background:"#7BE141",highlight:{border:"#41A906",background:"#A1EC76"},hover:{border:"#41A906",background:"#A1EC76"}},{border:"#E129F0",background:"#EB7DF4",highlight:{border:"#E129F0",background:"#F0B3F5"},hover:{border:"#E129F0",background:"#F0B3F5"}},{border:"#7C29F0",background:"#AD85E4",highlight:{border:"#7C29F0",background:"#D3BDF0"},hover:{border:"#7C29F0",background:"#D3BDF0"}},{border:"#C37F00",background:"#FFA807",highlight:{border:"#C37F00",background:"#FFCA66"},hover:{border:"#C37F00",background:"#FFCA66"}},{border:"#4220FB",background:"#6E6EFD",highlight:{border:"#4220FB",background:"#9B9BFD"},hover:{border:"#4220FB",background:"#9B9BFD"}},{border:"#FD5A77",background:"#FFC0CB",highlight:{border:"#FD5A77",background:"#FFD1D9"},hover:{border:"#FD5A77",background:"#FFD1D9"}},{border:"#4AD63A",background:"#C2FABC",highlight:{border:"#4AD63A",background:"#E6FFE3"},hover:{border:"#4AD63A",background:"#E6FFE3"}},{border:"#990000",background:"#EE0000",highlight:{border:"#BB0000",background:"#FF3333"},hover:{border:"#BB0000",background:"#FF3333"}},{border:"#FF6000",background:"#FF6000",highlight:{border:"#FF6000",background:"#FF6000"},hover:{border:"#FF6000",background:"#FF6000"}},{border:"#97C2FC",background:"#2B7CE9",highlight:{border:"#D2E5FF",background:"#2B7CE9"},hover:{border:"#D2E5FF",background:"#2B7CE9"}},{border:"#399605",background:"#255C03",highlight:{border:"#399605",background:"#255C03"},hover:{border:"#399605",background:"#255C03"}},{border:"#B70054",background:"#FF007E",highlight:{border:"#B70054",background:"#FF007E"},hover:{border:"#B70054",background:"#FF007E"}},{border:"#AD85E4",background:"#7C29F0",highlight:{border:"#D3BDF0",background:"#7C29F0"},hover:{border:"#D3BDF0",background:"#7C29F0"}},{border:"#4557FA",background:"#000EA1",highlight:{border:"#6E6EFD",background:"#000EA1"},hover:{border:"#6E6EFD",background:"#000EA1"}},{border:"#FFC0CB",background:"#FD5A77",highlight:{border:"#FFD1D9",background:"#FD5A77"},hover:{border:"#FFD1D9",background:"#FD5A77"}},{border:"#C2FABC",background:"#74D66A",highlight:{border:"#E6FFE3",background:"#74D66A"},hover:{border:"#E6FFE3",background:"#74D66A"}},{border:"#EE0000",background:"#990000",highlight:{border:"#FF3333",background:"#BB0000"},hover:{border:"#FF3333",background:"#BB0000"}}],this.options={},this.defaultOptions={useDefaultGroups:!0},Object.assign(this.options,this.defaultOptions)}setOptions(t){const e=["useDefaultGroups"];if(void 0!==t)for(const i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&-1===e.indexOf(i)){const e=t[i];this.add(i,e)}}clear(){this._groups=new Map,this._groupNames=[]}get(t,e=!0){let i=this._groups.get(t);if(void 0===i&&e)if(!1===this.options.useDefaultGroups&&this._groupNames.length>0){const e=this._groupIndex%this._groupNames.length;++this._groupIndex,i={},i.color=this._groups.get(this._groupNames[e]),this._groups.set(t,i)}else{const e=this._defaultIndex%this._defaultGroups.length;this._defaultIndex++,i={},i.color=this._defaultGroups[e],this._groups.set(t,i)}return i}add(t,e){return this._groups.has(t)||this._groupNames.push(t),this._groups.set(t,e),e}}function K(t,e){const o=["node","edge","label"];let s=!0;const n=i.topMost(e,"chosen");if("boolean"==typeof n)s=n;else if("object"==typeof n){if(-1===o.indexOf(t))throw new Error("choosify: subOption '"+t+"' should be one of '"+o.join("', '")+"'");const n=i.topMost(e,["chosen",t]);"boolean"!=typeof n&&"function"!=typeof n||(s=n)}return s}function G(t,e,i){if(t.width<=0||t.height<=0)return!1;if(void 0!==i){const t={x:e.x-i.x,y:e.y-i.y};if(0!==i.angle){const o=-i.angle;e={x:Math.cos(o)*t.x-Math.sin(o)*t.y,y:Math.sin(o)*t.x+Math.cos(o)*t.y}}else e=t}const o=t.x+t.width,s=t.y+t.width;return t.left<e.x&&o>e.x&&t.top<e.y&&s>e.y}function Z(t){return"string"==typeof t&&""!==t}function $(t,e,i,o){let s=o.x,n=o.y;if("function"==typeof o.distanceToBorder){const i=o.distanceToBorder(t,e),r=Math.sin(e)*i,d=Math.cos(e)*i;d===i?(s+=i,n=o.y):r===i?(s=o.x,n-=i):(s+=d,n-=r)}else o.shape.width>o.shape.height?(s=o.x+.5*o.shape.width,n=o.y-i):(s=o.x+i,n=o.y-.5*o.shape.height);return{x:s,y:n}}class Q{constructor(t){this.measureText=t,this.current=0,this.width=0,this.height=0,this.lines=[]}_add(t,e,i="normal"){void 0===this.lines[t]&&(this.lines[t]={width:0,height:0,blocks:[]});let o=e;void 0!==e&&""!==e||(o=" ");const s=this.measureText(o,i),n=Object.assign({},s.values);n.text=e,n.width=s.width,n.mod=i,void 0!==e&&""!==e||(n.width=0),this.lines[t].blocks.push(n),this.lines[t].width+=n.width}curWidth(){const t=this.lines[this.current];return void 0===t?0:t.width}append(t,e="normal"){this._add(this.current,t,e)}newLine(t,e="normal"){this._add(this.current,t,e),this.current++}determineLineHeights(){for(let t=0;t<this.lines.length;t++){const e=this.lines[t];let i=0;if(void 0!==e.blocks)for(let t=0;t<e.blocks.length;t++){const o=e.blocks[t];i<o.height&&(i=o.height)}e.height=i}}determineLabelSize(){let t=0,e=0;for(let i=0;i<this.lines.length;i++){const o=this.lines[i];o.width>t&&(t=o.width),e+=o.height}this.width=t,this.height=e}removeEmptyBlocks(){const t=[];for(let e=0;e<this.lines.length;e++){const i=this.lines[e];if(0===i.blocks.length)continue;if(e===this.lines.length-1&&0===i.width)continue;const o={};let s;Object.assign(o,i),o.blocks=[];const n=[];for(let t=0;t<i.blocks.length;t++){const e=i.blocks[t];0!==e.width?n.push(e):void 0===s&&(s=e)}0===n.length&&void 0!==s&&n.push(s),o.blocks=n,t.push(o)}return t}finalize(){this.determineLineHeights(),this.determineLabelSize();const t=this.removeEmptyBlocks();return{width:this.width,height:this.height,lines:t}}}const J={"<b>":/<b>/,"<i>":/<i>/,"<code>":/<code>/,"</b>":/<\/b>/,"</i>":/<\/i>/,"</code>":/<\/code>/,"*":/\*/,_:/_/,"`":/`/,afterBold:/[^*]/,afterItal:/[^_]/,afterMono:/[^`]/};class tt{constructor(t){this.text=t,this.bold=!1,this.ital=!1,this.mono=!1,this.spacing=!1,this.position=0,this.buffer="",this.modStack=[],this.blocks=[]}mod(){return 0===this.modStack.length?"normal":this.modStack[0]}modName(){return 0===this.modStack.length?"normal":"mono"===this.modStack[0]?"mono":this.bold&&this.ital?"boldital":this.bold?"bold":this.ital?"ital":void 0}emitBlock(){this.spacing&&(this.add(" "),this.spacing=!1),this.buffer.length>0&&(this.blocks.push({text:this.buffer,mod:this.modName()}),this.buffer="")}add(t){" "===t&&(this.spacing=!0),this.spacing&&(this.buffer+=" ",this.spacing=!1)," "!=t&&(this.buffer+=t)}parseWS(t){return!!/[ \t]/.test(t)&&(this.mono?this.add(t):this.spacing=!0,!0)}setTag(t){this.emitBlock(),this[t]=!0,this.modStack.unshift(t)}unsetTag(t){this.emitBlock(),this[t]=!1,this.modStack.shift()}parseStartTag(t,e){return!(this.mono||this[t]||!this.match(e))&&(this.setTag(t),!0)}match(t,e=!0){const[i,o]=this.prepareRegExp(t),s=i.test(this.text.substr(this.position,o));return s&&e&&(this.position+=o-1),s}parseEndTag(t,e,i){let o=this.mod()===t;return o="mono"===t?o&&this.mono:o&&!this.mono,!(!o||!this.match(e))&&(void 0!==i?(this.position===this.text.length-1||this.match(i,!1))&&this.unsetTag(t):this.unsetTag(t),!0)}replace(t,e){return!!this.match(t)&&(this.add(e),this.position+=length-1,!0)}prepareRegExp(t){let e,i;if(t instanceof RegExp)i=t,e=1;else{const o=J[t];i=void 0!==o?o:new RegExp(t),e=t.length}return[i,e]}}class et{constructor(t,e,i,o){this.ctx=t,this.parent=e,this.selected=i,this.hover=o;this.lines=new Q((e,s)=>{if(void 0===e)return 0;const n=this.parent.getFormattingValues(t,i,o,s);let r=0;if(""!==e){r=this.ctx.measureText(e).width}return{width:r,values:n}})}process(t){if(!Z(t))return this.lines.finalize();const e=this.parent.fontOptions;t=(t=t.replace(/\r\n/g,"\n")).replace(/\r/g,"\n");const i=String(t).split("\n"),o=i.length;if(e.multi)for(let t=0;t<o;t++){const o=this.splitBlocks(i[t],e.multi);if(void 0!==o)if(0!==o.length){if(e.maxWdt>0)for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.splitStringIntoLines(i,e,!0)}else for(let t=0;t<o.length;t++){const e=o[t].mod,i=o[t].text;this.lines.append(i,e)}this.lines.newLine()}else this.lines.newLine("")}else if(e.maxWdt>0)for(let t=0;t<o;t++)this.splitStringIntoLines(i[t]);else for(let t=0;t<o;t++)this.lines.newLine(i[t]);return this.lines.finalize()}decodeMarkupSystem(t){let e="none";return"markdown"===t||"md"===t?e="markdown":!0!==t&&"html"!==t||(e="html"),e}splitHtmlBlocks(t){const e=new tt(t),i=t=>{if(/&/.test(t)){return e.replace(e.text,"&lt;","<")||e.replace(e.text,"&amp;","&")||e.add("&"),!0}return!1};for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||/</.test(t)&&(e.parseStartTag("bold","<b>")||e.parseStartTag("ital","<i>")||e.parseStartTag("mono","<code>")||e.parseEndTag("bold","</b>")||e.parseEndTag("ital","</i>")||e.parseEndTag("mono","</code>"))||i(t)||e.add(t),e.position++}return e.emitBlock(),e.blocks}splitMarkdownBlocks(t){const e=new tt(t);let i=!0;const o=t=>!!/\\/.test(t)&&(e.position<this.text.length+1&&(e.position++,t=this.text.charAt(e.position),/ \t/.test(t)?e.spacing=!0:(e.add(t),i=!1)),!0);for(;e.position<e.text.length;){const t=e.text.charAt(e.position);e.parseWS(t)||o(t)||(i||e.spacing)&&(e.parseStartTag("bold","*")||e.parseStartTag("ital","_")||e.parseStartTag("mono","`"))||e.parseEndTag("bold","*","afterBold")||e.parseEndTag("ital","_","afterItal")||e.parseEndTag("mono","`","afterMono")||(e.add(t),i=!1),e.position++}return e.emitBlock(),e.blocks}splitBlocks(t,e){const i=this.decodeMarkupSystem(e);return"none"===i?[{text:t,mod:"normal"}]:"markdown"===i?this.splitMarkdownBlocks(t):"html"===i?this.splitHtmlBlocks(t):void 0}overMaxWidth(t){const e=this.ctx.measureText(t).width;return this.lines.curWidth()+e>this.parent.fontOptions.maxWdt}getLongestFit(t){let e="",i=0;for(;i<t.length;){const o=e+(""===e?"":" ")+t[i];if(this.overMaxWidth(o))break;e=o,i++}return i}getLongestFitWord(t){let e=0;for(;e<t.length&&!this.overMaxWidth(t.slice(0,e));)e++;return e}splitStringIntoLines(t,e="normal",i=!1){this.parent.getFormattingValues(this.ctx,this.selected,this.hover,e);let o=(t=(t=t.replace(/^( +)/g,"$1\r")).replace(/([^\r][^ ]*)( +)/g,"$1\r$2\r")).split("\r");for(;o.length>0;){let t=this.getLongestFit(o);if(0===t){const t=o[0],i=this.getLongestFitWord(t);this.lines.newLine(t.slice(0,i),e),o[0]=t.slice(i)}else{let s=t;" "===o[t-1]?t--:" "===o[s]&&s++;const n=o.slice(0,t).join("");t==o.length&&i?this.lines.append(n,e):this.lines.newLine(n,e),o=o.slice(s)}}}}const it=["bold","ital","boldital","mono"];class ot{constructor(t,e,i=!1){this.body=t,this.pointToSelf=!1,this.baseSize=void 0,this.fontOptions={},this.setOptions(e),this.size={top:0,left:0,width:0,height:0,yLine:0},this.isEdgeLabel=i}setOptions(t){if(this.elementOptions=t,this.initFontOptions(t.font),Z(t.label)?this.labelDirty=!0:t.label=void 0,void 0!==t.font&&null!==t.font)if("string"==typeof t.font)this.baseSize=this.fontOptions.size;else if("object"==typeof t.font){const e=t.font.size;void 0!==e&&(this.baseSize=e)}}initFontOptions(t){i.forEach(it,t=>{this.fontOptions[t]={}}),ot.parseFontString(this.fontOptions,t)?this.fontOptions.vadjust=0:i.forEach(t,(t,e)=>{null!=t&&"object"!=typeof t&&(this.fontOptions[e]=t)})}static parseFontString(t,e){if(!e||"string"!=typeof e)return!1;const i=e.split(" ");return t.size=+i[0].replace("px",""),t.face=i[1],t.color=i[2],!0}constrain(t){const e={constrainWidth:!1,maxWdt:-1,minWdt:-1,constrainHeight:!1,minHgt:-1,valign:"middle"},o=i.topMost(t,"widthConstraint");if("number"==typeof o)e.maxWdt=Number(o),e.minWdt=Number(o);else if("object"==typeof o){const o=i.topMost(t,["widthConstraint","maximum"]);"number"==typeof o&&(e.maxWdt=Number(o));const s=i.topMost(t,["widthConstraint","minimum"]);"number"==typeof s&&(e.minWdt=Number(s))}const s=i.topMost(t,"heightConstraint");if("number"==typeof s)e.minHgt=Number(s);else if("object"==typeof s){const o=i.topMost(t,["heightConstraint","minimum"]);"number"==typeof o&&(e.minHgt=Number(o));const s=i.topMost(t,["heightConstraint","valign"]);"string"==typeof s&&("top"!==s&&"bottom"!==s||(e.valign=s))}return e}update(t,e){this.setOptions(t,!0),this.propagateFonts(e),i.deepExtend(this.fontOptions,this.constrain(e)),this.fontOptions.chooser=K("label",e)}adjustSizes(t){const e=t?t.right+t.left:0;this.fontOptions.constrainWidth&&(this.fontOptions.maxWdt-=e,this.fontOptions.minWdt-=e);const i=t?t.top+t.bottom:0;this.fontOptions.constrainHeight&&(this.fontOptions.minHgt-=i)}addFontOptionsToPile(t,e){for(let i=0;i<e.length;++i)this.addFontToPile(t,e[i])}addFontToPile(t,e){if(void 0===e)return;if(void 0===e.font||null===e.font)return;const i=e.font;t.push(i)}getBasicOptions(t){const e={};for(let o=0;o<t.length;++o){let s=t[o];const n={};ot.parseFontString(n,s)&&(s=n),i.forEach(s,(t,i)=>{void 0!==t&&(Object.prototype.hasOwnProperty.call(e,i)||(-1!==it.indexOf(i)?e[i]={}:e[i]=t))})}return e}getFontOption(t,e,i){let o;for(let s=0;s<t.length;++s){const n=t[s];if(Object.prototype.hasOwnProperty.call(n,e)){if(o=n[e],null==o)continue;const t={};if(ot.parseFontString(t,o)&&(o=t),Object.prototype.hasOwnProperty.call(o,i))return o[i]}}if(Object.prototype.hasOwnProperty.call(this.fontOptions,i))return this.fontOptions[i];throw new Error("Did not find value for multi-font for property: '"+i+"'")}getFontOptions(t,e){const i={},o=["color","size","face","mod","vadjust"];for(let s=0;s<o.length;++s){const n=o[s];i[n]=this.getFontOption(t,e,n)}return i}propagateFonts(t){const e=[];this.addFontOptionsToPile(e,t),this.fontOptions=this.getBasicOptions(e);for(let t=0;t<it.length;++t){const o=it[t],s=this.fontOptions[o],n=this.getFontOptions(e,o);i.forEach(n,(t,e)=>{s[e]=t}),s.size=Number(s.size),s.vadjust=Number(s.vadjust)}}draw(t,e,i,o,s,n="middle"){if(void 0===this.elementOptions.label)return;let r=this.fontOptions.size*this.body.view.scale;this.elementOptions.label&&r<this.elementOptions.scaling.label.drawThreshold-1||(r>=this.elementOptions.scaling.label.maxVisible&&(r=Number(this.elementOptions.scaling.label.maxVisible)/this.body.view.scale),this.calculateLabelSize(t,o,s,e,i,n),this._drawBackground(t),this._drawText(t,e,this.size.yLine,n,r))}_drawBackground(t){if(void 0!==this.fontOptions.background&&"none"!==this.fontOptions.background){t.fillStyle=this.fontOptions.background;const e=this.getSize();t.fillRect(e.left,e.top,e.width,e.height)}}_drawText(t,e,i,o="middle",s){[e,i]=this._setAlignment(t,e,i,o),t.textAlign="left",e-=this.size.width/2,this.fontOptions.valign&&this.size.height>this.size.labelHeight&&("top"===this.fontOptions.valign&&(i-=(this.size.height-this.size.labelHeight)/2),"bottom"===this.fontOptions.valign&&(i+=(this.size.height-this.size.labelHeight)/2));for(let o=0;o<this.lineCount;o++){const n=this.lines[o];if(n&&n.blocks){let o=0;this.isEdgeLabel||"center"===this.fontOptions.align?o+=(this.size.width-n.width)/2:"right"===this.fontOptions.align&&(o+=this.size.width-n.width);for(let r=0;r<n.blocks.length;r++){const d=n.blocks[r];t.font=d.font;const[a,h]=this._getColor(d.color,s,d.strokeColor);d.strokeWidth>0&&(t.lineWidth=d.strokeWidth,t.strokeStyle=h,t.lineJoin="round"),t.fillStyle=a,d.strokeWidth>0&&t.strokeText(d.text,e+o,i+d.vadjust),t.fillText(d.text,e+o,i+d.vadjust),o+=d.width}i+=n.height}}}_setAlignment(t,e,i,o){if(this.isEdgeLabel&&"horizontal"!==this.fontOptions.align&&!1===this.pointToSelf){e=0,i=0;const o=2;"top"===this.fontOptions.align?(t.textBaseline="alphabetic",i-=2*o):"bottom"===this.fontOptions.align?(t.textBaseline="hanging",i+=2*o):t.textBaseline="middle"}else t.textBaseline=o;return[e,i]}_getColor(t,e,o){let s=t||"#000000",n=o||"#ffffff";if(e<=this.elementOptions.scaling.label.drawThreshold){const t=Math.max(0,Math.min(1,1-(this.elementOptions.scaling.label.drawThreshold-e)));s=i.overrideOpacity(s,t),n=i.overrideOpacity(n,t)}return[s,n]}getTextSize(t,e=!1,i=!1){return this._processLabel(t,e,i),{width:this.size.width,height:this.size.height,lineCount:this.lineCount}}getSize(){let t=this.size.left,e=this.size.top-1;if(this.isEdgeLabel){const i=.5*-this.size.width;switch(this.fontOptions.align){case"middle":t=i,e=.5*-this.size.height;break;case"top":t=i,e=-(this.size.height+2);break;case"bottom":t=i,e=2}}return{left:t,top:e,width:this.size.width,height:this.size.height}}calculateLabelSize(t,e,i,o=0,s=0,n="middle"){this._processLabel(t,e,i),this.size.left=o-.5*this.size.width,this.size.top=s-.5*this.size.height,this.size.yLine=s+.5*(1-this.lineCount)*this.fontOptions.size,"hanging"===n&&(this.size.top+=.5*this.fontOptions.size,this.size.top+=4,this.size.yLine+=4)}getFormattingValues(t,e,i,o){const s=function(t,e,i){return"normal"===e?"mod"===i?"":t[i]:void 0!==t[e][i]?t[e][i]:t[i]},n={color:s(this.fontOptions,o,"color"),size:s(this.fontOptions,o,"size"),face:s(this.fontOptions,o,"face"),mod:s(this.fontOptions,o,"mod"),vadjust:s(this.fontOptions,o,"vadjust"),strokeWidth:this.fontOptions.strokeWidth,strokeColor:this.fontOptions.strokeColor};(e||i)&&("normal"===o&&!0===this.fontOptions.chooser&&this.elementOptions.labelHighlightBold?n.mod="bold":"function"==typeof this.fontOptions.chooser&&this.fontOptions.chooser(n,this.elementOptions.id,e,i));let r="";return void 0!==n.mod&&""!==n.mod&&(r+=n.mod+" "),r+=n.size+"px "+n.face,t.font=r.replace(/"/g,""),n.font=t.font,n.height=n.size,n}differentState(t,e){return t!==this.selectedState||e!==this.hoverState}_processLabelText(t,e,i,o){return new et(t,this,e,i).process(o)}_processLabel(t,e,i){if(!1===this.labelDirty&&!this.differentState(e,i))return;const o=this._processLabelText(t,e,i,this.elementOptions.label);this.fontOptions.minWdt>0&&o.width<this.fontOptions.minWdt&&(o.width=this.fontOptions.minWdt),this.size.labelHeight=o.height,this.fontOptions.minHgt>0&&o.height<this.fontOptions.minHgt&&(o.height=this.fontOptions.minHgt),this.lines=o.lines,this.lineCount=o.lines.length,this.size.width=o.width,this.size.height=o.height,this.selectedState=e,this.hoverState=i,this.labelDirty=!1}visible(){if(0===this.size.width||0===this.size.height||void 0===this.elementOptions.label)return!1;return!(this.fontOptions.size*this.body.view.scale<this.elementOptions.scaling.label.drawThreshold-1)}}class st{constructor(t,e,i){this.body=e,this.labelModule=i,this.setOptions(t),this.top=void 0,this.left=void 0,this.height=void 0,this.width=void 0,this.radius=void 0,this.margin=void 0,this.refreshNeeded=!0,this.boundingBox={top:0,left:0,right:0,bottom:0}}setOptions(t){this.options=t}_setMargins(t){this.margin={},this.options.margin&&("object"==typeof this.options.margin?(this.margin.top=this.options.margin.top,this.margin.right=this.options.margin.right,this.margin.bottom=this.options.margin.bottom,this.margin.left=this.options.margin.left):(this.margin.top=this.options.margin,this.margin.right=this.options.margin,this.margin.bottom=this.options.margin,this.margin.left=this.options.margin)),t.adjustSizes(this.margin)}_distanceToBorder(t,e){const i=this.options.borderWidth;return t&&this.resize(t),Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}enableShadow(t,e){e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}enableBorderDashes(t,e){if(!1!==e.borderDashes)if(void 0!==t.setLineDash){let i=e.borderDashes;!0===i&&(i=[5,15]),t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1}disableBorderDashes(t,e){!1!==e.borderDashes&&(void 0!==t.setLineDash?t.setLineDash([0]):(console.warn("setLineDash is not supported in this browser. The dashed borders cannot be used."),this.options.shapeProperties.borderDashes=!1,e.borderDashes=!1))}needsRefresh(t,e){return!0===this.refreshNeeded?(this.refreshNeeded=!1,!0):void 0===this.width||this.labelModule.differentState(t,e)}initContextForDraw(t,e){const i=e.borderWidth/this.body.view.scale;t.lineWidth=Math.min(this.width,i),t.strokeStyle=e.borderColor,t.fillStyle=e.color}performStroke(t,e){const i=e.borderWidth/this.body.view.scale;t.save(),i>0&&(this.enableBorderDashes(t,e),t.stroke(),this.disableBorderDashes(t,e)),t.restore()}performFill(t,e){t.save(),t.fillStyle=e.color,this.enableShadow(t,e),t.fill(),this.disableShadow(t,e),t.restore(),this.performStroke(t,e)}_addBoundingBoxMargin(t){this.boundingBox.left-=t,this.boundingBox.top-=t,this.boundingBox.bottom+=t,this.boundingBox.right+=t}_updateBoundingBox(t,e,i,o,s){void 0!==i&&this.resize(i,o,s),this.left=t-this.width/2,this.top=e-this.height/2,this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width}updateBoundingBox(t,e,i,o,s){this._updateBoundingBox(t,e,i,o,s)}getDimensionsFromLabel(t,e,i){this.textSize=this.labelModule.getTextSize(t,e,i);let o=this.textSize.width,s=this.textSize.height;return 0===o&&(o=14,s=14),{width:o,height:s}}}let nt=class extends st{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e=this.selected,i=this.hover){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.width=o.width+this.margin.right+this.margin.left,this.height=o.height+this.margin.top+this.margin.bottom,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,n),d(t,this.left,this.top,this.width,this.height,n.borderRadius),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s)}updateBoundingBox(t,e,i,o,s){this._updateBoundingBox(t,e,i,o,s);const n=this.options.shapeProperties.borderRadius;this._addBoundingBoxMargin(n)}distanceToBorder(t,e){t&&this.resize(t);const i=this.options.borderWidth;return Math.min(Math.abs(this.width/2/Math.cos(e)),Math.abs(this.height/2/Math.sin(e)))+i}};class rt extends st{constructor(t,e,i){super(t,e,i),this.labelOffset=0,this.selected=!1}setOptions(t,e,i){this.options=t,void 0===e&&void 0===i||this.setImages(e,i)}setImages(t,e){e&&this.selected?(this.imageObj=e,this.imageObjAlt=t):(this.imageObj=t,this.imageObjAlt=e)}switchImages(t){const e=t&&!this.selected||!t&&this.selected;if(this.selected=t,void 0!==this.imageObjAlt&&e){const t=this.imageObj;this.imageObj=this.imageObjAlt,this.imageObjAlt=t}}_getImagePadding(){const t={top:0,right:0,bottom:0,left:0};if(this.options.imagePadding){const e=this.options.imagePadding;"object"==typeof e?(t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left):(t.top=e,t.right=e,t.bottom=e,t.left=e)}return t}_resizeImage(){let t,e;if(!1===this.options.shapeProperties.useImageSize){let i=1,o=1;this.imageObj.width&&this.imageObj.height&&(this.imageObj.width>this.imageObj.height?i=this.imageObj.width/this.imageObj.height:o=this.imageObj.height/this.imageObj.width),t=2*this.options.size*i,e=2*this.options.size*o}else{const i=this._getImagePadding();t=this.imageObj.width+i.left+i.right,e=this.imageObj.height+i.top+i.bottom}this.width=t,this.height=e,this.radius=.5*this.width}_drawRawCircle(t,e,i,o){this.initContextForDraw(t,o),r(t,e,i,o.size),this.performFill(t,o)}_drawImageAtPosition(t,e){if(0!=this.imageObj.width){t.globalAlpha=void 0!==e.opacity?e.opacity:1,this.enableShadow(t,e);let i=1;!0===this.options.shapeProperties.interpolation&&(i=this.imageObj.width/this.width/this.body.view.scale);const o=this._getImagePadding(),s=this.left+o.left,n=this.top+o.top,r=this.width-o.left-o.right,d=this.height-o.top-o.bottom;this.imageObj.drawImageAtPosition(t,i,s,n,r,d),this.disableShadow(t,e)}}_drawImageLabel(t,e,i,o,s){let n=0;if(void 0!==this.height){n=.5*this.height;const e=this.labelModule.getTextSize(t,o,s);e.lineCount>=1&&(n+=e.height/2)}const r=i+n;this.options.label&&(this.labelOffset=n),this.labelModule.draw(t,e,r,o,s,"hanging")}}let dt=class extends rt{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e=this.selected,i=this.hover){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i),s=Math.max(o.width+this.margin.right+this.margin.left,o.height+this.margin.top+this.margin.bottom);this.options.size=s/2,this.width=s,this.height=s,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this._drawRawCircle(t,e,i,n),this.updateBoundingBox(e,i),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,i,o,s)}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size}distanceToBorder(t){return t&&this.resize(t),.5*this.width}};class at extends rt{constructor(t,e,i,o,s){super(t,e,i),this.setImages(o,s)}resize(t,e=this.selected,i=this.hover){if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,this.height=t,void(this.radius=.5*this.width)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,i,o,s,n){this.switchImages(o),this.resize();let r=e,d=i;"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=i,r+=this.width/2,d+=this.height/2):(this.left=e-this.width/2,this.top=i-this.height/2),this._drawRawCircle(t,r,d,n),t.save(),t.clip(),this._drawImageAtPosition(t,n),t.restore(),this._drawImageLabel(t,r,d,o,s),this.updateBoundingBox(e,i)}updateBoundingBox(t,e){"top-left"===this.options.shapeProperties.coordinateOrigin?(this.boundingBox.top=e,this.boundingBox.left=t,this.boundingBox.right=t+2*this.options.size,this.boundingBox.bottom=e+2*this.options.size):(this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size),this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset)}distanceToBorder(t){return t&&this.resize(t),.5*this.width}}class ht extends st{constructor(t,e,i){super(t,e,i)}resize(t,e=this.selected,i=this.hover,o={size:this.options.size}){if(this.needsRefresh(e,i)){this.labelModule.getTextSize(t,e,i);const s=2*o.size;this.width=this.customSizeWidth??s,this.height=this.customSizeHeight??s,this.radius=.5*this.width}}_drawShape(t,e,i,o,s,n,r,d){var a;return this.resize(t,n,r,d),this.left=o-this.width/2,this.top=s-this.height/2,this.initContextForDraw(t,d),(a=e,Object.prototype.hasOwnProperty.call(c,a)?c[a]:function(t,...e){CanvasRenderingContext2D.prototype[a].call(t,e)})(t,o,s,d.size),this.performFill(t,d),void 0!==this.options.icon&&void 0!==this.options.icon.code&&(t.font=(n?"bold ":"")+this.height/2+"px "+(this.options.icon.face||"FontAwesome"),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",t.fillText(this.options.icon.code,o,s)),{drawExternalLabel:()=>{if(void 0!==this.options.label){this.labelModule.calculateLabelSize(t,n,r,o,s,"hanging");const e=s+.5*this.height+.5*this.labelModule.size.height;this.labelModule.draw(t,o,e,n,r,"hanging")}this.updateBoundingBox(o,s)}}}updateBoundingBox(t,e){this.boundingBox.top=e-this.options.size,this.boundingBox.left=t-this.options.size,this.boundingBox.right=t+this.options.size,this.boundingBox.bottom=e+this.options.size,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height))}}class lt extends ht{constructor(t,e,i,o){super(t,e,i,o),this.ctxRenderer=o}draw(t,e,i,o,s,n){this.resize(t,o,s,n),this.left=e-this.width/2,this.top=i-this.height/2,t.save();const r=this.ctxRenderer({ctx:t,id:this.options.id,x:e,y:i,state:{selected:o,hover:s},style:{...n},label:this.options.label});if(null!=r.drawNode&&r.drawNode(),t.restore(),r.drawExternalLabel){const e=r.drawExternalLabel;r.drawExternalLabel=()=>{t.save(),e(),t.restore()}}return r.nodeDimensions&&(this.customSizeWidth=r.nodeDimensions.width,this.customSizeHeight=r.nodeDimensions.height),r}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class ct extends st{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i).width+this.margin.right+this.margin.left;this.width=o,this.height=o,this.radius=this.width/2}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.initContextForDraw(t,n),h(t,e-this.width/2,i-this.height/2,this.width,this.height),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let pt=class extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"diamond",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class ut extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"circle",2,e,i,o,s,n)}distanceToBorder(t){return t&&this.resize(t),this.options.size}}class gt extends st{constructor(t,e,i){super(t,e,i)}resize(t,e=this.selected,i=this.hover){if(this.needsRefresh(e,i)){const o=this.getDimensionsFromLabel(t,e,i);this.height=2*o.height,this.width=o.width+o.height,this.radius=.5*this.width}}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-.5*this.width,this.top=i-.5*this.height,this.initContextForDraw(t,n),a(t,this.left,this.top,this.width,this.height),this.performFill(t,n),this.updateBoundingBox(e,i,t,o,s),this.labelModule.draw(t,e,i,o,s)}distanceToBorder(t,e){t&&this.resize(t);const i=.5*this.width,o=.5*this.height,s=Math.sin(e)*i,n=Math.cos(e)*o;return i*o/Math.sqrt(s*s+n*n)}}class bt extends st{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.iconSize={width:Number(this.options.icon.size),height:Number(this.options.icon.size)},this.width=this.iconSize.width+this.margin.right+this.margin.left,this.height=this.iconSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,s,n){return this.resize(t,o,s),this.options.icon.size=this.options.icon.size||50,this.left=e-this.width/2,this.top=i-this.height/2,this._icon(t,e,i,o,s,n),{drawExternalLabel:()=>{if(void 0!==this.options.label){const e=5;this.labelModule.draw(t,this.left+this.iconSize.width/2+this.margin.left,i+this.height/2+e,o)}this.updateBoundingBox(e,i)}}}updateBoundingBox(t,e){if(this.boundingBox.top=e-.5*this.options.icon.size,this.boundingBox.left=t-.5*this.options.icon.size,this.boundingBox.right=t+.5*this.options.icon.size,this.boundingBox.bottom=e+.5*this.options.icon.size,void 0!==this.options.label&&this.labelModule.size.width>0){const t=5;this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelModule.size.height+t)}}_icon(t,e,i,o,s,n){const r=Number(this.options.icon.size);void 0!==this.options.icon.code?(t.font=[null!=this.options.icon.weight?this.options.icon.weight:o?"bold":"",(null!=this.options.icon.weight&&o?5:0)+r+"px",this.options.icon.face].join(" "),t.fillStyle=this.options.icon.color||"black",t.textAlign="center",t.textBaseline="middle",this.enableShadow(t,n),t.fillText(this.options.icon.code,e,i),this.disableShadow(t,n)):console.error("When using the icon shape, you need to define the code in the icon options object. This can be done per node or globally.")}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let mt=class extends rt{constructor(t,e,i,o,s){super(t,e,i),this.setImages(o,s)}resize(t,e=this.selected,i=this.hover){if(void 0===this.imageObj.src||void 0===this.imageObj.width||void 0===this.imageObj.height){const t=2*this.options.size;return this.width=t,void(this.height=t)}this.needsRefresh(e,i)&&this._resizeImage()}draw(t,e,o,s,n,r){t.save(),this.switchImages(s),this.resize();let d=e,a=o;if("top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=e,this.top=o,d+=this.width/2,a+=this.height/2):(this.left=e-this.width/2,this.top=o-this.height/2),!0===this.options.shapeProperties.useBorderWithImage){const e=this.options.borderWidth,o=this.options.borderWidthSelected||2*this.options.borderWidth,d=(s?o:e)/this.body.view.scale;t.lineWidth=Math.min(this.width,d),t.beginPath();let a=s?this.options.color.highlight.border:n?this.options.color.hover.border:this.options.color.border,h=s?this.options.color.highlight.background:n?this.options.color.hover.background:this.options.color.background;void 0!==r.opacity&&(a=i.overrideOpacity(a,r.opacity),h=i.overrideOpacity(h,r.opacity)),t.strokeStyle=a,t.fillStyle=h,t.rect(this.left-.5*t.lineWidth,this.top-.5*t.lineWidth,this.width+t.lineWidth,this.height+t.lineWidth),t.fill(),this.performStroke(t,r),t.closePath()}this._drawImageAtPosition(t,r),this._drawImageLabel(t,d,a,s,n),this.updateBoundingBox(e,o),t.restore()}updateBoundingBox(t,e){this.resize(),"top-left"===this.options.shapeProperties.coordinateOrigin?(this.left=t,this.top=e):(this.left=t-this.width/2,this.top=e-this.height/2),this.boundingBox.left=this.left,this.boundingBox.top=this.top,this.boundingBox.bottom=this.top+this.height,this.boundingBox.right=this.left+this.width,void 0!==this.options.label&&this.labelModule.size.width>0&&(this.boundingBox.left=Math.min(this.boundingBox.left,this.labelModule.size.left),this.boundingBox.right=Math.max(this.boundingBox.right,this.labelModule.size.left+this.labelModule.size.width),this.boundingBox.bottom=Math.max(this.boundingBox.bottom,this.boundingBox.bottom+this.labelOffset))}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class ft extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"square",2,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class yt extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"hexagon",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class vt extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"star",4,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class wt extends st{constructor(t,e,i){super(t,e,i),this._setMargins(i)}resize(t,e,i){this.needsRefresh(e,i)&&(this.textSize=this.labelModule.getTextSize(t,e,i),this.width=this.textSize.width+this.margin.right+this.margin.left,this.height=this.textSize.height+this.margin.top+this.margin.bottom,this.radius=.5*this.width)}draw(t,e,i,o,s,n){this.resize(t,o,s),this.left=e-this.width/2,this.top=i-this.height/2,this.enableShadow(t,n),this.labelModule.draw(t,this.left+this.textSize.width/2+this.margin.left,this.top+this.textSize.height/2+this.margin.top,o,s),this.disableShadow(t,n),this.updateBoundingBox(e,i,t,o,s)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}let xt=class extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"triangle",3,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}};class _t extends ht{constructor(t,e,i){super(t,e,i)}draw(t,e,i,o,s,n){return this._drawShape(t,"triangleDown",3,e,i,o,s,n)}distanceToBorder(t,e){return this._distanceToBorder(t,e)}}class Et{constructor(t,e,o,s,n,r){this.options=i.bridgeObject(n),this.globalOptions=n,this.defaultOptions=r,this.body=e,this.edges=[],this.id=void 0,this.imagelist=o,this.grouplist=s,this.x=void 0,this.y=void 0,this.baseSize=this.options.size,this.baseFontSize=this.options.font.size,this.predefinedPosition=!1,this.selected=!1,this.hover=!1,this.labelModule=new ot(this.body,this.options,!1),this.setOptions(t)}attachEdge(t){-1===this.edges.indexOf(t)&&this.edges.push(t)}detachEdge(t){const e=this.edges.indexOf(t);-1!=e&&this.edges.splice(e,1)}setOptions(t){const e=this.options.shape;if(!t)return;if(void 0!==t.color&&(this._localColor=t.color),void 0!==t.id&&(this.id=t.id),void 0===this.id)throw new Error("Node must have an id");Et.checkMass(t,this.id),void 0!==t.x&&(null===t.x?(this.x=void 0,this.predefinedPosition=!1):(this.x=parseInt(t.x),this.predefinedPosition=!0)),void 0!==t.y&&(null===t.y?(this.y=void 0,this.predefinedPosition=!1):(this.y=parseInt(t.y),this.predefinedPosition=!0)),void 0!==t.size&&(this.baseSize=t.size),void 0!==t.value&&(t.value=parseFloat(t.value)),Et.parseOptions(this.options,t,!0,this.globalOptions,this.grouplist);const i=[t,this.options,this.defaultOptions];return this.chooser=K("node",i),this._load_images(),this.updateLabelModule(t),void 0!==t.opacity&&Et.checkOpacity(t.opacity)&&(this.options.opacity=t.opacity),this.updateShape(e),void 0!==t.hidden||void 0!==t.physics}_load_images(){if(("circularImage"===this.options.shape||"image"===this.options.shape)&&void 0===this.options.image)throw new Error("Option image must be defined for node type '"+this.options.shape+"'");if(void 0!==this.options.image){if(void 0===this.imagelist)throw new Error("Internal Error: No images provided");if("string"==typeof this.options.image)this.imageObj=this.imagelist.load(this.options.image,this.options.brokenImage,this.id);else{if(void 0===this.options.image.unselected)throw new Error("No unselected image provided");this.imageObj=this.imagelist.load(this.options.image.unselected,this.options.brokenImage,this.id),void 0!==this.options.image.selected?this.imageObjAlt=this.imagelist.load(this.options.image.selected,this.options.brokenImage,this.id):this.imageObjAlt=void 0}}}static checkOpacity(t){return 0<=t&&t<=1}static checkCoordinateOrigin(t){return void 0===t||"center"===t||"top-left"===t}static updateGroupOptions(t,e,o){if(void 0===o)return;const s=t.group;if(void 0!==e&&void 0!==e.group&&s!==e.group)throw new Error("updateGroupOptions: group values in options don't match.");if(!("number"==typeof s||"string"==typeof s&&""!=s))return;const n=o.get(s);void 0!==n.opacity&&void 0===e.opacity&&(Et.checkOpacity(n.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+n.opacity),n.opacity=void 0));const r=Object.getOwnPropertyNames(e).filter(t=>null!=e[t]);r.push("font"),i.selectiveNotDeepExtend(r,t,n),t.color=i.parseColor(t.color)}static parseOptions(t,e,o=!1,s={},n){if(i.selectiveNotDeepExtend(["color","fixed","shadow"],t,e,o),Et.checkMass(e),void 0!==t.opacity&&(Et.checkOpacity(t.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity),t.opacity=void 0)),void 0!==e.opacity&&(Et.checkOpacity(e.opacity)||(console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+e.opacity),e.opacity=void 0)),e.shapeProperties&&!Et.checkCoordinateOrigin(e.shapeProperties.coordinateOrigin)&&console.error("Invalid option for node coordinateOrigin, found: "+e.shapeProperties.coordinateOrigin),i.mergeOptions(t,e,"shadow",s),void 0!==e.color&&null!==e.color){const o=i.parseColor(e.color);i.fillIfDefined(t.color,o)}else!0===o&&null===e.color&&(t.color=i.bridgeObject(s.color));void 0!==e.fixed&&null!==e.fixed&&("boolean"==typeof e.fixed?(t.fixed.x=e.fixed,t.fixed.y=e.fixed):(void 0!==e.fixed.x&&"boolean"==typeof e.fixed.x&&(t.fixed.x=e.fixed.x),void 0!==e.fixed.y&&"boolean"==typeof e.fixed.y&&(t.fixed.y=e.fixed.y))),!0===o&&null===e.font&&(t.font=i.bridgeObject(s.font)),Et.updateGroupOptions(t,e,n),void 0!==e.scaling&&i.mergeOptions(t.scaling,e.scaling,"label",s.scaling)}getFormattingValues(){const t={color:this.options.color.background,opacity:this.options.opacity,borderWidth:this.options.borderWidth,borderColor:this.options.color.border,size:this.options.size,borderDashes:this.options.shapeProperties.borderDashes,borderRadius:this.options.shapeProperties.borderRadius,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y};if(this.selected||this.hover?!0===this.chooser?this.selected?(null!=this.options.borderWidthSelected?t.borderWidth=this.options.borderWidthSelected:t.borderWidth*=2,t.color=this.options.color.highlight.background,t.borderColor=this.options.color.highlight.border,t.shadow=this.options.shadow.enabled):this.hover&&(t.color=this.options.color.hover.background,t.borderColor=this.options.color.hover.border,t.shadow=this.options.shadow.enabled):"function"==typeof this.chooser&&(this.chooser(t,this.options.id,this.selected,this.hover),!1===t.shadow&&(t.shadowColor===this.options.shadow.color&&t.shadowSize===this.options.shadow.size&&t.shadowX===this.options.shadow.x&&t.shadowY===this.options.shadow.y||(t.shadow=!0))):t.shadow=this.options.shadow.enabled,void 0!==this.options.opacity){const e=this.options.opacity;t.borderColor=i.overrideOpacity(t.borderColor,e),t.color=i.overrideOpacity(t.color,e),t.shadowColor=i.overrideOpacity(t.shadowColor,e)}return t}updateLabelModule(t){void 0!==this.options.label&&null!==this.options.label||(this.options.label=""),Et.updateGroupOptions(this.options,{...t,color:t&&t.color||this._localColor||void 0},this.grouplist);const e=this.grouplist.get(this.options.group,!1),i=[t,this.options,e,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,i),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateShape(t){if(t===this.options.shape&&this.shape)this.shape.setOptions(this.options,this.imageObj,this.imageObjAlt);else switch(this.options.shape){case"box":this.shape=new nt(this.options,this.body,this.labelModule);break;case"circle":this.shape=new dt(this.options,this.body,this.labelModule);break;case"circularImage":this.shape=new at(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"custom":this.shape=new lt(this.options,this.body,this.labelModule,this.options.ctxRenderer);break;case"database":this.shape=new ct(this.options,this.body,this.labelModule);break;case"diamond":this.shape=new pt(this.options,this.body,this.labelModule);break;case"dot":this.shape=new ut(this.options,this.body,this.labelModule);break;case"ellipse":default:this.shape=new gt(this.options,this.body,this.labelModule);break;case"icon":this.shape=new bt(this.options,this.body,this.labelModule);break;case"image":this.shape=new mt(this.options,this.body,this.labelModule,this.imageObj,this.imageObjAlt);break;case"square":this.shape=new ft(this.options,this.body,this.labelModule);break;case"hexagon":this.shape=new yt(this.options,this.body,this.labelModule);break;case"star":this.shape=new vt(this.options,this.body,this.labelModule);break;case"text":this.shape=new wt(this.options,this.body,this.labelModule);break;case"triangle":this.shape=new xt(this.options,this.body,this.labelModule);break;case"triangleDown":this.shape=new _t(this.options,this.body,this.labelModule)}this.needsRefresh()}select(){this.selected=!0,this.needsRefresh()}unselect(){this.selected=!1,this.needsRefresh()}needsRefresh(){this.shape.refreshNeeded=!0}getTitle(){return this.options.title}distanceToBorder(t,e){return this.shape.distanceToBorder(t,e)}isFixed(){return this.options.fixed.x&&this.options.fixed.y}isSelected(){return this.selected}getValue(){return this.options.value}getLabelSize(){return this.labelModule.size()}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),s=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.size=this.options.scaling.min+o*s}else this.options.size=this.baseSize,this.options.font.size=this.baseFontSize;this.updateLabelModule()}draw(t){const e=this.getFormattingValues();return this.shape.draw(t,this.x,this.y,this.selected,this.hover,e)||{}}updateBoundingBox(t){this.shape.updateBoundingBox(this.x,this.y,t)}resize(t){const e=this.getFormattingValues();this.shape.resize(t,this.selected,this.hover,e)}getItemsOnPoint(t){const e=[];return this.labelModule.visible()&&G(this.labelModule.getSize(),t)&&e.push({nodeId:this.id,labelId:0}),G(this.shape.boundingBox,t)&&e.push({nodeId:this.id}),e}isOverlappingWith(t){return this.shape.left<t.right&&this.shape.left+this.shape.width>t.left&&this.shape.top<t.bottom&&this.shape.top+this.shape.height>t.top}isBoundingBoxOverlappingWith(t){return this.shape.boundingBox.left<t.right&&this.shape.boundingBox.right>t.left&&this.shape.boundingBox.top<t.bottom&&this.shape.boundingBox.bottom>t.top}static checkMass(t,e){if(void 0!==t.mass&&t.mass<=0){let o="";void 0!==e&&(o=" in node id: "+e),console.error("%cNegative or zero mass disallowed"+o+", setting mass to 1.",i.VALIDATOR_PRINT_STYLE),t.mass=1}}}class Ot{constructor(t,e,o,s){if(this.body=t,this.images=e,this.groups=o,this.layoutEngine=s,this.body.functions.createNode=this.create.bind(this),this.nodesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items,e.data,e.oldData)},remove:(t,e)=>{this.remove(e.items)}},this.defaultOptions={borderWidth:1,borderWidthSelected:void 0,brokenImage:void 0,color:{border:"#2B7CE9",background:"#97C2FC",highlight:{border:"#2B7CE9",background:"#D2E5FF"},hover:{border:"#2B7CE9",background:"#D2E5FF"}},opacity:void 0,fixed:{x:!1,y:!1},font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:0,strokeColor:"#ffffff",align:"center",vadjust:0,multi:!1,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"monospace",vadjust:2}},group:void 0,hidden:!1,icon:{face:"FontAwesome",code:void 0,size:50,color:"#2B7CE9"},image:void 0,imagePadding:{top:0,right:0,bottom:0,left:0},label:void 0,labelHighlightBold:!0,level:void 0,margin:{top:5,right:5,bottom:5,left:5},mass:1,physics:!0,scaling:{min:10,max:30,label:{enabled:!1,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},shape:"ellipse",shapeProperties:{borderDashes:!1,borderRadius:6,interpolation:!0,useImageSize:!1,useBorderWithImage:!1,coordinateOrigin:"center"},size:25,title:void 0,value:void 0,x:void 0,y:void 0},this.defaultOptions.mass<=0)throw"Internal error: mass in defaultOptions of NodesHandler may not be zero or negative";this.options=i.bridgeObject(this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("refreshNodes",this.refresh.bind(this)),this.body.emitter.on("refresh",this.refresh.bind(this)),this.body.emitter.on("destroy",()=>{i.forEach(this.nodesListeners,(t,e)=>{this.body.data.nodes&&this.body.data.nodes.off(e,t)}),delete this.body.functions.createNode,delete this.nodesListeners.add,delete this.nodesListeners.update,delete this.nodesListeners.remove,delete this.nodesListeners})}setOptions(t){if(void 0!==t){if(Et.parseOptions(this.options,t),void 0!==t.opacity&&(Number.isNaN(t.opacity)||!Number.isFinite(t.opacity)||t.opacity<0||t.opacity>1?console.error("Invalid option for node opacity. Value must be between 0 and 1, found: "+t.opacity):this.options.opacity=t.opacity),void 0!==t.shape)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].updateShape();if(void 0!==t.font||void 0!==t.widthConstraint||void 0!==t.heightConstraint)for(const t of Object.keys(this.body.nodes))this.body.nodes[t].updateLabelModule(),this.body.nodes[t].needsRefresh();if(void 0!==t.size)for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.body.nodes[t].needsRefresh();void 0===t.hidden&&void 0===t.physics||this.body.emitter.emit("_dataChanged")}}setData(t,e=!1){const s=this.body.data.nodes;if(o.isDataViewLike("id",t))this.body.data.nodes=t;else if(Array.isArray(t))this.body.data.nodes=new o.DataSet,this.body.data.nodes.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.nodes=new o.DataSet}if(s&&i.forEach(this.nodesListeners,function(t,e){s.off(e,t)}),this.body.nodes={},this.body.data.nodes){const t=this;i.forEach(this.nodesListeners,function(e,i){t.body.data.nodes.on(i,e)});const e=this.body.data.nodes.getIds();this.add(e,!0)}!1===e&&this.body.emitter.emit("_dataChanged")}add(t,e=!1){let i;const o=[];for(let e=0;e<t.length;e++){i=t[e];const s=this.body.data.nodes.get(i),n=this.create(s);o.push(n),this.body.nodes[i]=n}this.layoutEngine.positionInitially(o),!1===e&&this.body.emitter.emit("_dataChanged")}update(t,e,i){const o=this.body.nodes;let s=!1;for(let i=0;i<t.length;i++){const n=t[i];let r=o[n];const d=e[i];void 0!==r?r.setOptions(d)&&(s=!0):(s=!0,r=this.create(d),o[n]=r)}s||void 0===i||(s=e.some(function(t,e){const o=i[e];return o&&o.level!==t.level})),!0===s?this.body.emitter.emit("_dataChanged"):this.body.emitter.emit("_dataUpdated")}remove(t){const e=this.body.nodes;for(let i=0;i<t.length;i++){delete e[t[i]]}this.body.emitter.emit("_dataChanged")}create(t,e=Et){return new e(t,this.body,this.images,this.groups,this.options,this.defaultOptions)}refresh(t=!1){i.forEach(this.body.nodes,(e,i)=>{const o=this.body.data.nodes.get(i);void 0!==o&&(!0===t&&e.setOptions({x:null,y:null}),e.setOptions({fixed:!1}),e.setOptions(o))})}getPositions(t){const e={};if(void 0!==t){if(!0===Array.isArray(t)){for(let i=0;i<t.length;i++)if(void 0!==this.body.nodes[t[i]]){const o=this.body.nodes[t[i]];e[t[i]]={x:Math.round(o.x),y:Math.round(o.y)}}}else if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];e[t]={x:Math.round(i.x),y:Math.round(i.y)}}}else for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];e[this.body.nodeIndices[t]]={x:Math.round(i.x),y:Math.round(i.y)}}return e}getPosition(t){if(null==t)throw new TypeError("No id was specified for getPosition method.");if(null==this.body.nodes[t])throw new ReferenceError(`NodeId provided for getPosition does not exist. Provided: ${t}`);return{x:Math.round(this.body.nodes[t].x),y:Math.round(this.body.nodes[t].y)}}storePositions(){const t=[],e=this.body.data.nodes.getDataSet();for(const i of e.get()){const e=i.id,o=this.body.nodes[e],s=Math.round(o.x),n=Math.round(o.y);i.x===s&&i.y===n||t.push({id:e,x:s,y:n})}e.update(t)}getBoundingBox(t){if(void 0!==this.body.nodes[t])return this.body.nodes[t].shape.boundingBox}getConnectedNodes(t,e){const i=[];if(void 0!==this.body.nodes[t]){const o=this.body.nodes[t],s={};for(let t=0;t<o.edges.length;t++){const n=o.edges[t];"to"!==e&&n.toId==o.id?void 0===s[n.fromId]&&(i.push(n.fromId),s[n.fromId]=!0):"from"!==e&&n.fromId==o.id&&void 0===s[n.toId]&&(i.push(n.toId),s[n.toId]=!0)}}return i}getConnectedEdges(t){const e=[];if(void 0!==this.body.nodes[t]){const i=this.body.nodes[t];for(let t=0;t<i.edges.length;t++)e.push(i.edges[t].id)}else console.error("NodeId provided for getConnectedEdges does not exist. Provided: ",t);return e}moveNode(t,e,i){void 0!==this.body.nodes[t]?(this.body.nodes[t].x=Number(e),this.body.nodes[t].y=Number(i),setTimeout(()=>{this.body.emitter.emit("startSimulation")},0)):console.error("Node id supplied to moveNode does not exist. Provided: ",t)}}class Mt{static transform(t,e){Array.isArray(t)||(t=[t]);const i=e.point.x,o=e.point.y,s=e.angle,n=e.length;for(let e=0;e<t.length;++e){const r=t[e],d=r.x*Math.cos(s)-r.y*Math.sin(s),a=r.x*Math.sin(s)+r.y*Math.cos(s);r.x=i+n*d,r.y=o+n*a}}static drawPath(t,e){t.beginPath(),t.moveTo(e[0].x,e[0].y);for(let i=1;i<e.length;++i)t.lineTo(e[i].x,e[i].y);t.closePath()}}let Ct=class extends Mt{static draw(t,e){if(e.image){t.save(),t.translate(e.point.x,e.point.y),t.rotate(Math.PI/2+e.angle);const i=null!=e.imageWidth?e.imageWidth:e.image.width,o=null!=e.imageHeight?e.imageHeight:e.image.height;e.image.drawImageAtPosition(t,1,-i/2,0,i,o),t.restore()}return!1}};class St extends Mt{static draw(t,e){const i=[{x:0,y:0},{x:-1,y:.3},{x:-.9,y:0},{x:-1,y:-.3}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class kt{static draw(t,e){const i=[{x:-1,y:0},{x:0,y:.3},{x:-.4,y:0},{x:0,y:-.3}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class It{static draw(t,e){const i={x:-.4,y:0};Mt.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,s=e.angle-o/2,n=e.angle+o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,s,n,!1),t.stroke(),!0}}class zt{static draw(t,e){const i={x:-.3,y:0};Mt.transform(i,e),t.strokeStyle=t.fillStyle,t.fillStyle="rgba(0, 0, 0, 0)";const o=Math.PI,s=e.angle+o/2,n=e.angle+3*o/2;return t.beginPath(),t.arc(i.x,i.y,.4*e.length,s,n,!1),t.stroke(),!0}}class Tt{static draw(t,e){const i=[{x:.02,y:0},{x:-1,y:.3},{x:-1,y:-.3}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class Dt{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-1,y:0}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class Bt{static draw(t,e){const i={x:-.4,y:0};return Mt.transform(i,e),r(t,i.x,i.y,.4*e.length),!0}}class Nt{static draw(t,e){const i=[{x:0,y:.5},{x:0,y:-.5},{x:-.15,y:-.5},{x:-.15,y:.5}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class Pt{static draw(t,e){const i=[{x:0,y:.3},{x:0,y:-.3},{x:-.6,y:-.3},{x:-.6,y:.3}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class Ft{static draw(t,e){const i=[{x:0,y:0},{x:-.5,y:-.3},{x:-1,y:0},{x:-.5,y:.3}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class Rt{static draw(t,e){const i=[{x:-1,y:.3},{x:-.5,y:0},{x:-1,y:-.3},{x:0,y:0}];return Mt.transform(i,e),Mt.drawPath(t,i),!0}}class At{static draw(t,e){let i;switch(e.type&&(i=e.type.toLowerCase()),i){case"image":return Ct.draw(t,e);case"circle":return Bt.draw(t,e);case"box":return Pt.draw(t,e);case"crow":return kt.draw(t,e);case"curve":return It.draw(t,e);case"diamond":return Ft.draw(t,e);case"inv_curve":return zt.draw(t,e);case"triangle":return Tt.draw(t,e);case"inv_triangle":return Dt.draw(t,e);case"bar":return Nt.draw(t,e);case"vee":return Rt.draw(t,e);default:return St.draw(t,e)}}}class jt{from;fromPoint;to;toPoint;via;color={};colorDirty=!0;id;options;hoverWidth=1.5;selectionWidth=2;_body;_labelModule;constructor(t,e,i){this._body=e,this._labelModule=i,this.setOptions(t),this.fromPoint=this.from,this.toPoint=this.to}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to]}cleanup(){return!1}setOptions(t){this.options=t,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.id=this.options.id}drawLine(t,e,i,o,s=this.getViaNode()){t.strokeStyle=this.getColor(t,e),t.lineWidth=e.width,!1!==e.dashes?this._drawDashedLine(t,e,s):this._drawLine(t,e,s)}_drawLine(t,e,i,o,s){if(this.from!=this.to)this._line(t,e,i,o,s);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}}_drawDashedLine(t,e,i,o,s){t.lineCap="round";const n=Array.isArray(e.dashes)?e.dashes:[5,5];if(void 0!==t.setLineDash){if(t.save(),t.setLineDash(n),t.lineDashOffset=0,this.from!=this.to)this._line(t,e,i);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}t.setLineDash([0]),t.lineDashOffset=0,t.restore()}else{if(this.from!=this.to)l(t,this.from.x,this.from.y,this.to.x,this.to.y,n);else{const[i,o,s]=this._getCircleData(t);this._circle(t,e,i,o,s)}this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}}findBorderPosition(t,e,i){return this.from!=this.to?this._findBorderPosition(t,e,i):this._findBorderPositionCircle(t,e,i)}findBorderPositions(t){if(this.from!=this.to)return{from:this._findBorderPosition(this.from,t),to:this._findBorderPosition(this.to,t)};{const[e,i]=this._getCircleData(t).slice(0,2);return{from:this._findBorderPositionCircle(this.from,t,{x:e,y:i,low:.25,high:.6,direction:-1}),to:this._findBorderPositionCircle(this.from,t,{x:e,y:i,low:.6,high:.8,direction:1})}}}_getCircleData(t){const e=this.options.selfReference.size;void 0!==t&&void 0===this.from.shape.width&&this.from.shape.resize(t);const i=$(t,this.options.selfReference.angle,e,this.from);return[i.x,i.y,e]}_pointOnCircle(t,e,i,o){const s=2*o*Math.PI;return{x:t+i*Math.cos(s),y:e-i*Math.sin(s)}}_findBorderPositionCircle(t,e,i){const o=i.x,s=i.y;let n=i.low,r=i.high;const d=i.direction,a=this.options.selfReference.size;let h,l=.5*(n+r),c=0;!0===this.options.arrowStrikethrough&&(-1===d?c=this.options.endPointOffset.from:1===d&&(c=this.options.endPointOffset.to));let p=0;do{l=.5*(n+r),h=this._pointOnCircle(o,s,a,l);const i=Math.atan2(t.y-h.y,t.x-h.x),u=t.distanceToBorder(e,i)+c-Math.sqrt(Math.pow(h.x-t.x,2)+Math.pow(h.y-t.y,2));if(Math.abs(u)<.05)break;u>0?d>0?n=l:r=l:d>0?r=l:n=l,++p}while(n<=r&&p<10);return{...h,t:l}}getLineWidth(t,e){return!0===t?Math.max(this.selectionWidth,.3/this._body.view.scale):!0===e?Math.max(this.hoverWidth,.3/this._body.view.scale):Math.max(this.options.width,.3/this._body.view.scale)}getColor(t,e){if(!1!==e.inheritsColor){if("both"===e.inheritsColor&&this.from.id!==this.to.id){const o=t.createLinearGradient(this.from.x,this.from.y,this.to.x,this.to.y);let s=this.from.options.color.highlight.border,n=this.to.options.color.highlight.border;return!1===this.from.selected&&!1===this.to.selected?(s=i.overrideOpacity(this.from.options.color.border,e.opacity),n=i.overrideOpacity(this.to.options.color.border,e.opacity)):!0===this.from.selected&&!1===this.to.selected?n=this.to.options.color.border:!1===this.from.selected&&!0===this.to.selected&&(s=this.from.options.color.border),o.addColorStop(0,s),o.addColorStop(1,n),o}return"to"===e.inheritsColor?i.overrideOpacity(this.to.options.color.border,e.opacity):i.overrideOpacity(this.from.options.color.border,e.opacity)}return i.overrideOpacity(e.color,e.opacity)}_circle(t,e,i,o,s){this.enableShadow(t,e);let n=0,r=2*Math.PI;if(!this.options.selfReference.renderBehindTheNode){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,d=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:-1}),a=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:1});n=Math.atan2(d.y-o,d.x-i),r=Math.atan2(a.y-o,a.x-i)}t.beginPath(),t.arc(i,o,s,n,r,!1),t.stroke(),this.disableShadow(t,e)}getDistanceToEdge(t,e,i,o,s,n){if(this.from!=this.to)return this._getDistanceToEdge(t,e,i,o,s,n);{const[t,e,i]=this._getCircleData(void 0),o=t-s,r=e-n;return Math.abs(Math.sqrt(o*o+r*r)-i)}}_getDistanceToLine(t,e,i,o,s,n){const r=i-t,d=o-e;let a=((s-t)*r+(n-e)*d)/(r*r+d*d);a>1?a=1:a<0&&(a=0);const h=t+a*r-s,l=e+a*d-n;return Math.sqrt(h*h+l*l)}getArrowData(t,e,i,o,s,n){let r,d,a,h,l,c,p;const u=n.width;"from"===e?(a=this.from,h=this.to,l=n.fromArrowScale<0,c=Math.abs(n.fromArrowScale),p=n.fromArrowType):"to"===e?(a=this.to,h=this.from,l=n.toArrowScale<0,c=Math.abs(n.toArrowScale),p=n.toArrowType):(a=this.to,h=this.from,l=n.middleArrowScale<0,c=Math.abs(n.middleArrowScale),p=n.middleArrowType);const g=15*c+3*u;if(a!=h){const o=g/Math.hypot(a.x-h.x,a.y-h.y);if("middle"!==e)if(!0===this.options.smooth.enabled){const s=this._findBorderPosition(a,t,{via:i}),n=this.getPoint(s.t+o*("from"===e?1:-1),i);r=Math.atan2(s.y-n.y,s.x-n.x),d=s}else r=Math.atan2(a.y-h.y,a.x-h.x),d=this._findBorderPosition(a,t);else{const t=(l?-o:o)/2,e=this.getPoint(.5+t,i),s=this.getPoint(.5-t,i);r=Math.atan2(e.y-s.y,e.x-s.x),d=this.getPoint(.5,i)}}else{const[i,o,s]=this._getCircleData(t);if("from"===e){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,n=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:-1});r=-2*n.t*Math.PI+1.5*Math.PI+.1*Math.PI,d=n}else if("to"===e){const e=this.options.selfReference.angle,s=this.options.selfReference.angle+Math.PI,n=this._findBorderPositionCircle(this.from,t,{x:i,y:o,low:e,high:s,direction:1});r=-2*n.t*Math.PI+1.5*Math.PI-1.1*Math.PI,d=n}else{const t=this.options.selfReference.angle/(2*Math.PI);d=this._pointOnCircle(i,o,s,t),r=-2*t*Math.PI+1.5*Math.PI+.1*Math.PI}}return{point:d,core:{x:d.x-.9*g*Math.cos(r),y:d.y-.9*g*Math.sin(r)},angle:r,length:g,type:p}}drawArrowHead(t,e,i,o,s){t.strokeStyle=this.getColor(t,e),t.fillStyle=t.strokeStyle,t.lineWidth=e.width;At.draw(t,s)&&(this.enableShadow(t,e),t.fill(),this.disableShadow(t,e))}enableShadow(t,e){!0===e.shadow&&(t.shadowColor=e.shadowColor,t.shadowBlur=e.shadowSize,t.shadowOffsetX=e.shadowX,t.shadowOffsetY=e.shadowY)}disableShadow(t,e){!0===e.shadow&&(t.shadowColor="rgba(0,0,0,0)",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0)}drawBackground(t,e){if(!1!==e.background){const i={strokeStyle:t.strokeStyle,lineWidth:t.lineWidth,dashes:t.dashes};t.strokeStyle=e.backgroundColor,t.lineWidth=e.backgroundSize,this.setStrokeDashed(t,e.backgroundDashes),t.stroke(),t.strokeStyle=i.strokeStyle,t.lineWidth=i.lineWidth,t.dashes=i.dashes,this.setStrokeDashed(t,e.dashes)}}setStrokeDashed(t,e){if(!1!==e)if(void 0!==t.setLineDash){const i=Array.isArray(e)?e:[5,5];t.setLineDash(i)}else console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.");else void 0!==t.setLineDash?t.setLineDash([]):console.warn("setLineDash is not supported in this browser. The dashed stroke cannot be used.")}}class Lt extends jt{constructor(t,e,i){super(t,e,i)}_findBorderPositionBezier(t,e,i=this._getViaCoordinates()){let o,s,n=!1,r=1,d=0,a=this.to,h=this.options.endPointOffset?this.options.endPointOffset.to:0;t.id===this.from.id&&(a=this.from,n=!0,h=this.options.endPointOffset?this.options.endPointOffset.from:0),!1===this.options.arrowStrikethrough&&(h=0);let l=0;do{s=.5*(d+r),o=this.getPoint(s,i);const t=Math.atan2(a.y-o.y,a.x-o.x),c=a.distanceToBorder(e,t)+h-Math.sqrt(Math.pow(o.x-a.x,2)+Math.pow(o.y-a.y,2));if(Math.abs(c)<.2)break;c<0?!1===n?d=s:r=s:!1===n?r=s:d=s,++l}while(d<=r&&l<10);return{...o,t:s}}_getDistanceToBezierEdge(t,e,i,o,s,n,r){let d,a,h,l,c,p=1e9,u=t,g=e;for(a=1;a<10;a++)h=.1*a,l=Math.pow(1-h,2)*t+2*h*(1-h)*r.x+Math.pow(h,2)*i,c=Math.pow(1-h,2)*e+2*h*(1-h)*r.y+Math.pow(h,2)*o,a>0&&(d=this._getDistanceToLine(u,g,l,c,s,n),p=d<p?d:p),u=l,g=c;return p}_bezierCurve(t,e,i,o){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),null!=i&&null!=i.x?null!=o&&null!=o.x?t.bezierCurveTo(i.x,i.y,o.x,o.y,this.toPoint.x,this.toPoint.y):t.quadraticCurveTo(i.x,i.y,this.toPoint.x,this.toPoint.y):t.lineTo(this.toPoint.x,this.toPoint.y),this.drawBackground(t,e),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){return this._getViaCoordinates()}}class Ht extends Lt{via=this.via;_boundFunction;constructor(t,e,i){super(t,e,i),this._boundFunction=()=>{this.positionBezierNode()},this._body.emitter.on("_repositionBezierNodes",this._boundFunction)}setOptions(t){super.setOptions(t);let e=!1;this.options.physics!==t.physics&&(e=!0),this.options=t,this.id=this.options.id,this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],this.setupSupportNode(),this.connect(),!0===e&&(this.via.setOptions({physics:this.options.physics}),this.positionBezierNode())}connect(){this.from=this._body.nodes[this.options.from],this.to=this._body.nodes[this.options.to],void 0===this.from||void 0===this.to||!1===this.options.physics||this.from.id===this.to.id?this.via.setOptions({physics:!1}):this.via.setOptions({physics:!0})}cleanup(){return this._body.emitter.off("_repositionBezierNodes",this._boundFunction),void 0!==this.via&&(delete this._body.nodes[this.via.id],this.via=void 0,!0)}setupSupportNode(){if(void 0===this.via){const t="edgeId:"+this.id,e=this._body.functions.createNode({id:t,shape:"circle",physics:!0,hidden:!0});this._body.nodes[t]=e,this.via=e,this.via.parentEdgeId=this.id,this.positionBezierNode()}}positionBezierNode(){void 0!==this.via&&void 0!==this.from&&void 0!==this.to?(this.via.x=.5*(this.from.x+this.to.x),this.via.y=.5*(this.from.y+this.to.y)):void 0!==this.via&&(this.via.x=0,this.via.y=0)}_line(t,e,i){this._bezierCurve(t,e,i)}_getViaCoordinates(){return this.via}getViaNode(){return this.via}getPoint(t,e=this.via){if(this.from===this.to){const[e,i,o]=this._getCircleData(),s=2*Math.PI*(1-t);return{x:e+o*Math.sin(s),y:i+o-o*(1-Math.cos(s))}}return{x:Math.pow(1-t,2)*this.fromPoint.x+2*t*(1-t)*e.x+Math.pow(t,2)*this.toPoint.x,y:Math.pow(1-t,2)*this.fromPoint.y+2*t*(1-t)*e.y+Math.pow(t,2)*this.toPoint.y}}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e,this.via)}_getDistanceToEdge(t,e,i,o,s,n){return this._getDistanceToBezierEdge(t,e,i,o,s,n,this.via)}}class Wt extends Lt{constructor(t,e,i){super(t,e,i)}_line(t,e,i){this._bezierCurve(t,e,i)}getViaNode(){return this._getViaCoordinates()}_getViaCoordinates(){const t=this.options.smooth.roundness,e=this.options.smooth.type;let i=Math.abs(this.from.x-this.to.x),o=Math.abs(this.from.y-this.to.y);if("discrete"===e||"diagonalCross"===e){let s,n;s=n=i<=o?t*o:t*i,this.from.x>this.to.x&&(s=-s),this.from.y>=this.to.y&&(n=-n);let r=this.from.x+s,d=this.from.y+n;return"discrete"===e&&(i<=o?r=i<t*o?this.from.x:r:d=o<t*i?this.from.y:d),{x:r,y:d}}if("straightCross"===e){let e=(1-t)*i,s=(1-t)*o;return i<=o?(e=0,this.from.y<this.to.y&&(s=-s)):(this.from.x<this.to.x&&(e=-e),s=0),{x:this.to.x+e,y:this.to.y+s}}if("horizontal"===e){let e=(1-t)*i;return this.from.x<this.to.x&&(e=-e),{x:this.to.x+e,y:this.from.y}}if("vertical"===e){let e=(1-t)*o;return this.from.y<this.to.y&&(e=-e),{x:this.from.x,y:this.to.y+e}}if("curvedCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),s=Math.PI,n=(Math.atan2(o,i)+(.5*t+.5)*s)%(2*s);return{x:this.from.x+(.5*t+.5)*e*Math.sin(n),y:this.from.y+(.5*t+.5)*e*Math.cos(n)}}if("curvedCCW"===e){i=this.to.x-this.from.x,o=this.from.y-this.to.y;const e=Math.sqrt(i*i+o*o),s=Math.PI,n=(Math.atan2(o,i)+(.5*-t+.5)*s)%(2*s);return{x:this.from.x+(.5*t+.5)*e*Math.sin(n),y:this.from.y+(.5*t+.5)*e*Math.cos(n)}}{let e,s;e=s=i<=o?t*o:t*i,this.from.x>this.to.x&&(e=-e),this.from.y>=this.to.y&&(s=-s);let n=this.from.x+e,r=this.from.y+s;return i<=o?n=this.from.x<=this.to.x?this.to.x<n?this.to.x:n:this.to.x>n?this.to.x:n:r=this.from.y>=this.to.y?this.to.y>r?this.to.y:r:this.to.y<r?this.to.y:r,{x:n,y:r}}}_findBorderPosition(t,e,i={}){return this._findBorderPositionBezier(t,e,i.via)}_getDistanceToEdge(t,e,i,o,s,n,r=this._getViaCoordinates()){return this._getDistanceToBezierEdge(t,e,i,o,s,n,r)}getPoint(t,e=this._getViaCoordinates()){const i=t;return{x:Math.pow(1-i,2)*this.fromPoint.x+2*i*(1-i)*e.x+Math.pow(i,2)*this.toPoint.x,y:Math.pow(1-i,2)*this.fromPoint.y+2*i*(1-i)*e.y+Math.pow(i,2)*this.toPoint.y}}}class Vt extends Lt{constructor(t,e,i){super(t,e,i)}_getDistanceToBezierEdge2(t,e,i,o,s,n,r,d){let a=1e9,h=t,l=e;const c=[0,0,0,0];for(let p=1;p<10;p++){const u=.1*p;c[0]=Math.pow(1-u,3),c[1]=3*u*Math.pow(1-u,2),c[2]=3*Math.pow(u,2)*(1-u),c[3]=Math.pow(u,3);const g=c[0]*t+c[1]*r.x+c[2]*d.x+c[3]*i,b=c[0]*e+c[1]*r.y+c[2]*d.y+c[3]*o;if(p>0){const t=this._getDistanceToLine(h,l,g,b,s,n);a=t<a?t:a}h=g,l=b}return a}}class qt extends Vt{constructor(t,e,i){super(t,e,i)}_line(t,e,i){const o=i[0],s=i[1];this._bezierCurve(t,e,o,s)}_getViaCoordinates(){const t=this.from.x-this.to.x,e=this.from.y-this.to.y;let i,o,s,n;const r=this.options.smooth.roundness;return(Math.abs(t)>Math.abs(e)||!0===this.options.smooth.forceDirection||"horizontal"===this.options.smooth.forceDirection)&&"vertical"!==this.options.smooth.forceDirection?(o=this.from.y,n=this.to.y,i=this.from.x-r*t,s=this.to.x+r*t):(o=this.from.y-r*e,n=this.to.y+r*e,i=this.from.x,s=this.to.x),[{x:i,y:o},{x:s,y:n}]}getViaNode(){return this._getViaCoordinates()}_findBorderPosition(t,e){return this._findBorderPositionBezier(t,e)}_getDistanceToEdge(t,e,i,o,s,n,[r,d]=this._getViaCoordinates()){return this._getDistanceToBezierEdge2(t,e,i,o,s,n,r,d)}getPoint(t,[e,i]=this._getViaCoordinates()){const o=t,s=[Math.pow(1-o,3),3*o*Math.pow(1-o,2),3*Math.pow(o,2)*(1-o),Math.pow(o,3)];return{x:s[0]*this.fromPoint.x+s[1]*e.x+s[2]*i.x+s[3]*this.toPoint.x,y:s[0]*this.fromPoint.y+s[1]*e.y+s[2]*i.y+s[3]*this.toPoint.y}}}class Ut extends jt{constructor(t,e,i){super(t,e,i)}_line(t,e){t.beginPath(),t.moveTo(this.fromPoint.x,this.fromPoint.y),t.lineTo(this.toPoint.x,this.toPoint.y),this.enableShadow(t,e),t.stroke(),this.disableShadow(t,e)}getViaNode(){}getPoint(t){return{x:(1-t)*this.fromPoint.x+t*this.toPoint.x,y:(1-t)*this.fromPoint.y+t*this.toPoint.y}}_findBorderPosition(t,e){let i=this.to,o=this.from;t.id===this.from.id&&(i=this.from,o=this.to);const s=Math.atan2(i.y-o.y,i.x-o.x),n=i.x-o.x,r=i.y-o.y,d=Math.sqrt(n*n+r*r),a=(d-t.distanceToBorder(e,s))/d;return{x:(1-a)*o.x+a*i.x,y:(1-a)*o.y+a*i.y,t:0}}_getDistanceToEdge(t,e,i,o,s,n){return this._getDistanceToLine(t,e,i,o,s,n)}}class Yt{constructor(t,e,o,s,n){if(void 0===e)throw new Error("No body provided");this.options=i.bridgeObject(s),this.globalOptions=s,this.defaultOptions=n,this.body=e,this.imagelist=o,this.id=void 0,this.fromId=void 0,this.toId=void 0,this.selected=!1,this.hover=!1,this.labelDirty=!0,this.baseWidth=this.options.width,this.baseFontSize=this.options.font.size,this.from=void 0,this.to=void 0,this.edgeType=void 0,this.connected=!1,this.labelModule=new ot(this.body,this.options,!0),this.setOptions(t)}setOptions(t){if(!t)return;let e=void 0!==t.physics&&this.options.physics!==t.physics||void 0!==t.hidden&&(this.options.hidden||!1)!==(t.hidden||!1)||void 0!==t.from&&this.options.from!==t.from||void 0!==t.to&&this.options.to!==t.to;Yt.parseOptions(this.options,t,!0,this.globalOptions),void 0!==t.id&&(this.id=t.id),void 0!==t.from&&(this.fromId=t.from),void 0!==t.to&&(this.toId=t.to),void 0!==t.title&&(this.title=t.title),void 0!==t.value&&(t.value=parseFloat(t.value));const i=[t,this.options,this.defaultOptions];return this.chooser=K("edge",i),this.updateLabelModule(t),e=this.updateEdgeType()||e,this._setInteractionWidths(),this.connect(),e}static parseOptions(t,e,o=!1,s={},n=!1){if(i.selectiveDeepExtend(["endPointOffset","arrowStrikethrough","id","from","hidden","hoverWidth","labelHighlightBold","length","line","opacity","physics","scaling","selectionWidth","selfReferenceSize","selfReference","to","title","value","width","font","chosen","widthConstraint"],t,e,o),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.from&&(Number.isFinite(e.endPointOffset.from)?t.endPointOffset.from=e.endPointOffset.from:(t.endPointOffset.from=void 0!==s.endPointOffset.from?s.endPointOffset.from:0,console.error("endPointOffset.from is not a valid number"))),void 0!==e.endPointOffset&&void 0!==e.endPointOffset.to&&(Number.isFinite(e.endPointOffset.to)?t.endPointOffset.to=e.endPointOffset.to:(t.endPointOffset.to=void 0!==s.endPointOffset.to?s.endPointOffset.to:0,console.error("endPointOffset.to is not a valid number"))),Z(e.label)?t.label=e.label:Z(t.label)||(t.label=void 0),i.mergeOptions(t,e,"smooth",s),i.mergeOptions(t,e,"shadow",s),i.mergeOptions(t,e,"background",s),void 0!==e.dashes&&null!==e.dashes?t.dashes=e.dashes:!0===o&&null===e.dashes&&(t.dashes=Object.create(s.dashes)),void 0!==e.scaling&&null!==e.scaling?(void 0!==e.scaling.min&&(t.scaling.min=e.scaling.min),void 0!==e.scaling.max&&(t.scaling.max=e.scaling.max),i.mergeOptions(t.scaling,e.scaling,"label",s.scaling)):!0===o&&null===e.scaling&&(t.scaling=Object.create(s.scaling)),void 0!==e.arrows&&null!==e.arrows)if("string"==typeof e.arrows){const i=e.arrows.toLowerCase();t.arrows.to.enabled=-1!=i.indexOf("to"),t.arrows.middle.enabled=-1!=i.indexOf("middle"),t.arrows.from.enabled=-1!=i.indexOf("from")}else{if("object"!=typeof e.arrows)throw new Error("The arrow newOptions can only be an object or a string. Refer to the documentation. You used:"+JSON.stringify(e.arrows));i.mergeOptions(t.arrows,e.arrows,"to",s.arrows),i.mergeOptions(t.arrows,e.arrows,"middle",s.arrows),i.mergeOptions(t.arrows,e.arrows,"from",s.arrows)}else!0===o&&null===e.arrows&&(t.arrows=Object.create(s.arrows));if(void 0!==e.color&&null!==e.color){const r=i.isString(e.color)?{color:e.color,highlight:e.color,hover:e.color,inherit:!1,opacity:1}:e.color,d=t.color;if(n)i.deepExtend(d,s.color,!1,o);else for(const t in d)Object.prototype.hasOwnProperty.call(d,t)&&delete d[t];if(i.isString(d))d.color=d,d.highlight=d,d.hover=d,d.inherit=!1,void 0===r.opacity&&(d.opacity=1);else{let t=!1;void 0!==r.color&&(d.color=r.color,t=!0),void 0!==r.highlight&&(d.highlight=r.highlight,t=!0),void 0!==r.hover&&(d.hover=r.hover,t=!0),void 0!==r.inherit&&(d.inherit=r.inherit),void 0!==r.opacity&&(d.opacity=Math.min(1,Math.max(0,r.opacity))),!0===t?d.inherit=!1:void 0===d.inherit&&(d.inherit="from")}}else!0===o&&null===e.color&&(t.color=i.bridgeObject(s.color));!0===o&&null===e.font&&(t.font=i.bridgeObject(s.font)),Object.prototype.hasOwnProperty.call(e,"selfReferenceSize")&&(console.warn("The selfReferenceSize property has been deprecated. Please use selfReference property instead. The selfReference can be set like thise selfReference:{size:30, angle:Math.PI / 4}"),t.selfReference.size=e.selfReferenceSize)}getFormattingValues(){const t=!0===this.options.arrows.to||!0===this.options.arrows.to.enabled,e=!0===this.options.arrows.from||!0===this.options.arrows.from.enabled,i=!0===this.options.arrows.middle||!0===this.options.arrows.middle.enabled,o=this.options.color.inherit,s={toArrow:t,toArrowScale:this.options.arrows.to.scaleFactor,toArrowType:this.options.arrows.to.type,toArrowSrc:this.options.arrows.to.src,toArrowImageWidth:this.options.arrows.to.imageWidth,toArrowImageHeight:this.options.arrows.to.imageHeight,middleArrow:i,middleArrowScale:this.options.arrows.middle.scaleFactor,middleArrowType:this.options.arrows.middle.type,middleArrowSrc:this.options.arrows.middle.src,middleArrowImageWidth:this.options.arrows.middle.imageWidth,middleArrowImageHeight:this.options.arrows.middle.imageHeight,fromArrow:e,fromArrowScale:this.options.arrows.from.scaleFactor,fromArrowType:this.options.arrows.from.type,fromArrowSrc:this.options.arrows.from.src,fromArrowImageWidth:this.options.arrows.from.imageWidth,fromArrowImageHeight:this.options.arrows.from.imageHeight,arrowStrikethrough:this.options.arrowStrikethrough,color:o?void 0:this.options.color.color,inheritsColor:o,opacity:this.options.color.opacity,hidden:this.options.hidden,length:this.options.length,shadow:this.options.shadow.enabled,shadowColor:this.options.shadow.color,shadowSize:this.options.shadow.size,shadowX:this.options.shadow.x,shadowY:this.options.shadow.y,dashes:this.options.dashes,width:this.options.width,background:this.options.background.enabled,backgroundColor:this.options.background.color,backgroundSize:this.options.background.size,backgroundDashes:this.options.background.dashes};if(this.selected||this.hover)if(!0===this.chooser){if(this.selected){const t=this.options.selectionWidth;"function"==typeof t?s.width=t(s.width):"number"==typeof t&&(s.width+=t),s.width=Math.max(s.width,.3/this.body.view.scale),s.color=this.options.color.highlight,s.shadow=this.options.shadow.enabled}else if(this.hover){const t=this.options.hoverWidth;"function"==typeof t?s.width=t(s.width):"number"==typeof t&&(s.width+=t),s.width=Math.max(s.width,.3/this.body.view.scale),s.color=this.options.color.hover,s.shadow=this.options.shadow.enabled}}else"function"==typeof this.chooser&&(this.chooser(s,this.options.id,this.selected,this.hover),void 0!==s.color&&(s.inheritsColor=!1),!1===s.shadow&&(s.shadowColor===this.options.shadow.color&&s.shadowSize===this.options.shadow.size&&s.shadowX===this.options.shadow.x&&s.shadowY===this.options.shadow.y||(s.shadow=!0)));else s.shadow=this.options.shadow.enabled,s.width=Math.max(s.width,.3/this.body.view.scale);return s}updateLabelModule(t){const e=[t,this.options,this.globalOptions,this.defaultOptions];this.labelModule.update(this.options,e),void 0!==this.labelModule.baseSize&&(this.baseFontSize=this.labelModule.baseSize)}updateEdgeType(){const t=this.options.smooth;let e=!1,i=!0;return void 0!==this.edgeType&&((this.edgeType instanceof Ht&&!0===t.enabled&&"dynamic"===t.type||this.edgeType instanceof qt&&!0===t.enabled&&"cubicBezier"===t.type||this.edgeType instanceof Wt&&!0===t.enabled&&"dynamic"!==t.type&&"cubicBezier"!==t.type||this.edgeType instanceof Ut&&!1===t.type.enabled)&&(i=!1),!0===i&&(e=this.cleanup())),!0===i?!0===t.enabled?"dynamic"===t.type?(e=!0,this.edgeType=new Ht(this.options,this.body,this.labelModule)):"cubicBezier"===t.type?this.edgeType=new qt(this.options,this.body,this.labelModule):this.edgeType=new Wt(this.options,this.body,this.labelModule):this.edgeType=new Ut(this.options,this.body,this.labelModule):this.edgeType.setOptions(this.options),e}connect(){this.disconnect(),this.from=this.body.nodes[this.fromId]||void 0,this.to=this.body.nodes[this.toId]||void 0,this.connected=void 0!==this.from&&void 0!==this.to,!0===this.connected?(this.from.attachEdge(this),this.to.attachEdge(this)):(this.from&&this.from.detachEdge(this),this.to&&this.to.detachEdge(this)),this.edgeType.connect()}disconnect(){this.from&&(this.from.detachEdge(this),this.from=void 0),this.to&&(this.to.detachEdge(this),this.to=void 0),this.connected=!1}getTitle(){return this.title}isSelected(){return this.selected}getValue(){return this.options.value}setValueRange(t,e,i){if(void 0!==this.options.value){const o=this.options.scaling.customScalingFunction(t,e,i,this.options.value),s=this.options.scaling.max-this.options.scaling.min;if(!0===this.options.scaling.label.enabled){const t=this.options.scaling.label.max-this.options.scaling.label.min;this.options.font.size=this.options.scaling.label.min+o*t}this.options.width=this.options.scaling.min+o*s}else this.options.width=this.baseWidth,this.options.font.size=this.baseFontSize;this._setInteractionWidths(),this.updateLabelModule()}_setInteractionWidths(){"function"==typeof this.options.hoverWidth?this.edgeType.hoverWidth=this.options.hoverWidth(this.options.width):this.edgeType.hoverWidth=this.options.hoverWidth+this.options.width,"function"==typeof this.options.selectionWidth?this.edgeType.selectionWidth=this.options.selectionWidth(this.options.width):this.edgeType.selectionWidth=this.options.selectionWidth+this.options.width}draw(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode();this.edgeType.drawLine(t,e,this.selected,this.hover,i),this.drawLabel(t,i)}drawArrows(t){const e=this.getFormattingValues();if(e.hidden)return;const i=this.edgeType.getViaNode(),o={};this.edgeType.fromPoint=this.edgeType.from,this.edgeType.toPoint=this.edgeType.to,e.fromArrow&&(o.from=this.edgeType.getArrowData(t,"from",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.fromPoint=o.from.core),e.fromArrowSrc&&(o.from.image=this.imagelist.load(e.fromArrowSrc)),e.fromArrowImageWidth&&(o.from.imageWidth=e.fromArrowImageWidth),e.fromArrowImageHeight&&(o.from.imageHeight=e.fromArrowImageHeight)),e.toArrow&&(o.to=this.edgeType.getArrowData(t,"to",i,this.selected,this.hover,e),!1===e.arrowStrikethrough&&(this.edgeType.toPoint=o.to.core),e.toArrowSrc&&(o.to.image=this.imagelist.load(e.toArrowSrc)),e.toArrowImageWidth&&(o.to.imageWidth=e.toArrowImageWidth),e.toArrowImageHeight&&(o.to.imageHeight=e.toArrowImageHeight)),e.middleArrow&&(o.middle=this.edgeType.getArrowData(t,"middle",i,this.selected,this.hover,e),e.middleArrowSrc&&(o.middle.image=this.imagelist.load(e.middleArrowSrc)),e.middleArrowImageWidth&&(o.middle.imageWidth=e.middleArrowImageWidth),e.middleArrowImageHeight&&(o.middle.imageHeight=e.middleArrowImageHeight)),e.fromArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.from),e.middleArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.middle),e.toArrow&&this.edgeType.drawArrowHead(t,e,this.selected,this.hover,o.to)}drawLabel(t,e){if(void 0!==this.options.label){const i=this.from,o=this.to;let s;if(this.labelModule.differentState(this.selected,this.hover)&&this.labelModule.getTextSize(t,this.selected,this.hover),i.id!=o.id){this.labelModule.pointToSelf=!1,s=this.edgeType.getPoint(.5,e),t.save();const i=this._getRotation(t);0!=i.angle&&(t.translate(i.x,i.y),t.rotate(i.angle)),this.labelModule.draw(t,s.x,s.y,this.selected,this.hover),t.restore()}else{this.labelModule.pointToSelf=!0;const e=$(t,this.options.selfReference.angle,this.options.selfReference.size,i);s=this._pointOnCircle(e.x,e.y,this.options.selfReference.size,this.options.selfReference.angle),this.labelModule.draw(t,s.x,s.y,this.selected,this.hover)}}}getItemsOnPoint(t){const e=[];if(this.labelModule.visible()){const i=this._getRotation();G(this.labelModule.getSize(),t,i)&&e.push({edgeId:this.id,labelId:0})}const i={left:t.x,top:t.y};return this.isOverlappingWith(i)&&e.push({edgeId:this.id}),e}isOverlappingWith(t){if(this.connected){const e=10,i=this.from.x,o=this.from.y,s=this.to.x,n=this.to.y,r=t.left,d=t.top;return this.edgeType.getDistanceToEdge(i,o,s,n,r,d)<e}return!1}_getRotation(t){const e=this.edgeType.getViaNode(),i=this.edgeType.getPoint(.5,e);void 0!==t&&this.labelModule.calculateLabelSize(t,this.selected,this.hover,i.x,i.y);const o={x:i.x,y:this.labelModule.size.yLine,angle:0};if(!this.labelModule.visible())return o;if("horizontal"===this.options.font.align)return o;const s=this.from.y-this.to.y,n=this.from.x-this.to.x;let r=Math.atan2(s,n);return(r<-1&&n<0||r>0&&n<0)&&(r+=Math.PI),o.angle=r,o}_pointOnCircle(t,e,i,o){return{x:t+i*Math.cos(o),y:e-i*Math.sin(o)}}select(){this.selected=!0}unselect(){this.selected=!1}cleanup(){return this.edgeType.cleanup()}remove(){this.cleanup(),this.disconnect(),delete this.body.edges[this.id]}endPointsValid(){return void 0!==this.body.nodes[this.fromId]&&void 0!==this.body.nodes[this.toId]}}class Xt{constructor(t,e,o){this.body=t,this.images=e,this.groups=o,this.body.functions.createEdge=this.create.bind(this),this.edgesListeners={add:(t,e)=>{this.add(e.items)},update:(t,e)=>{this.update(e.items)},remove:(t,e)=>{this.remove(e.items)}},this.options={},this.defaultOptions={arrows:{to:{enabled:!1,scaleFactor:1,type:"arrow"},middle:{enabled:!1,scaleFactor:1,type:"arrow"},from:{enabled:!1,scaleFactor:1,type:"arrow"}},endPointOffset:{from:0,to:0},arrowStrikethrough:!0,color:{color:"#848484",highlight:"#848484",hover:"#848484",inherit:"from",opacity:1},dashes:!1,font:{color:"#343434",size:14,face:"arial",background:"none",strokeWidth:2,strokeColor:"#ffffff",align:"horizontal",multi:!1,vadjust:0,bold:{mod:"bold"},boldital:{mod:"bold italic"},ital:{mod:"italic"},mono:{mod:"",size:15,face:"courier new",vadjust:2}},hidden:!1,hoverWidth:1.5,label:void 0,labelHighlightBold:!0,length:void 0,physics:!0,scaling:{min:1,max:15,label:{enabled:!0,min:14,max:30,maxVisible:30,drawThreshold:5},customScalingFunction:function(t,e,i,o){if(e===t)return.5;{const i=1/(e-t);return Math.max(0,(o-t)*i)}}},selectionWidth:1.5,selfReference:{size:20,angle:Math.PI/4,renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:10,x:5,y:5},background:{enabled:!1,color:"rgba(111,111,111,1)",size:10,dashes:!1},smooth:{enabled:!0,type:"dynamic",forceDirection:"none",roundness:.5},title:void 0,width:1,value:void 0},i.deepExtend(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("_forceDisableDynamicCurves",(t,e=!0)=>{"dynamic"===t&&(t="continuous");let i=!1;for(const e in this.body.edges)if(Object.prototype.hasOwnProperty.call(this.body.edges,e)){const o=this.body.edges[e],s=this.body.data.edges.get(e);if(null!=s){const e=s.smooth;void 0!==e&&!0===e.enabled&&"dynamic"===e.type&&(void 0===t?o.setOptions({smooth:!1}):o.setOptions({smooth:{type:t}}),i=!0)}}!0===e&&!0===i&&this.body.emitter.emit("_dataChanged")}),this.body.emitter.on("_dataUpdated",()=>{this.reconnectEdges()}),this.body.emitter.on("refreshEdges",this.refresh.bind(this)),this.body.emitter.on("refresh",this.refresh.bind(this)),this.body.emitter.on("destroy",()=>{i.forEach(this.edgesListeners,(t,e)=>{this.body.data.edges&&this.body.data.edges.off(e,t)}),delete this.body.functions.createEdge,delete this.edgesListeners.add,delete this.edgesListeners.update,delete this.edgesListeners.remove,delete this.edgesListeners})}setOptions(t){if(void 0!==t){Yt.parseOptions(this.options,t,!0,this.defaultOptions,!0);let e=!1;if(void 0!==t.smooth)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&(e=this.body.edges[t].updateEdgeType()||e);if(void 0!==t.font)for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&this.body.edges[t].updateLabelModule();void 0===t.hidden&&void 0===t.physics&&!0!==e||this.body.emitter.emit("_dataChanged")}}setData(t,e=!1){const s=this.body.data.edges;if(o.isDataViewLike("id",t))this.body.data.edges=t;else if(Array.isArray(t))this.body.data.edges=new o.DataSet,this.body.data.edges.add(t);else{if(t)throw new TypeError("Array or DataSet expected");this.body.data.edges=new o.DataSet}if(s&&i.forEach(this.edgesListeners,(t,e)=>{s.off(e,t)}),this.body.edges={},this.body.data.edges){i.forEach(this.edgesListeners,(t,e)=>{this.body.data.edges.on(e,t)});const t=this.body.data.edges.getIds();this.add(t,!0)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}add(t,e=!1){const i=this.body.edges,o=this.body.data.edges;for(let e=0;e<t.length;e++){const s=t[e],n=i[s];n&&n.disconnect();const r=o.get(s,{showInternalIds:!0});i[s]=this.create(r)}this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),!1===e&&this.body.emitter.emit("_dataChanged")}update(t){const e=this.body.edges,i=this.body.data.edges;let o=!1;for(let s=0;s<t.length;s++){const n=t[s],r=i.get(n),d=e[n];void 0!==d?(d.disconnect(),o=d.setOptions(r)||o,d.connect()):(this.body.edges[n]=this.create(r),o=!0)}!0===o?(this.body.emitter.emit("_adjustEdgesForHierarchicalLayout"),this.body.emitter.emit("_dataChanged")):this.body.emitter.emit("_dataUpdated")}remove(t,e=!0){if(0===t.length)return;const o=this.body.edges;i.forEach(t,t=>{const e=o[t];void 0!==e&&e.remove()}),e&&this.body.emitter.emit("_dataChanged")}refresh(){i.forEach(this.body.edges,(t,e)=>{const i=this.body.data.edges.get(e);void 0!==i&&t.setOptions(i)})}create(t){return new Yt(t,this.body,this.images,this.options,this.defaultOptions)}reconnectEdges(){let t;const e=this.body.nodes,i=this.body.edges;for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(e[t].edges=[]);for(t in i)if(Object.prototype.hasOwnProperty.call(i,t)){const e=i[t];e.from=null,e.to=null,e.connect()}}getConnectedNodes(t){const e=[];if(void 0!==this.body.edges[t]){const i=this.body.edges[t];void 0!==i.fromId&&e.push(i.fromId),void 0!==i.toId&&e.push(i.toId)}return e}_updateState(){this._addMissingEdges(),this._removeInvalidEdges()}_removeInvalidEdges(){const t=[];i.forEach(this.body.edges,(e,i)=>{const o=this.body.nodes[e.toId],s=this.body.nodes[e.fromId];void 0!==o&&!0===o.isCluster||void 0!==s&&!0===s.isCluster||void 0!==o&&void 0!==s||t.push(i)}),this.remove(t,!1)}_addMissingEdges(){const t=this.body.data.edges;if(null==t)return;const e=this.body.edges,i=[];t.forEach((t,o)=>{void 0===e[o]&&i.push(o)}),this.add(i,!0)}}class Kt{constructor(t,e,o){this.body=t,this.physicsBody=e,this.barnesHutTree,this.setOptions(o),this._rng=i.Alea("BARNES HUT SOLVER")}setOptions(t){this.options=t,this.thetaInversed=1/this.options.theta,this.overlapAvoidanceFactor=1-Math.max(0,Math.min(1,this.options.avoidOverlap))}solve(){if(0!==this.options.gravitationalConstant&&this.physicsBody.physicsNodeIndices.length>0){let t;const e=this.body.nodes,i=this.physicsBody.physicsNodeIndices,o=i.length,s=this._formBarnesHutTree(e,i);this.barnesHutTree=s;for(let n=0;n<o;n++)t=e[i[n]],t.options.mass>0&&this._getForceContributions(s.root,t)}}_getForceContributions(t,e){this._getForceContribution(t.children.NW,e),this._getForceContribution(t.children.NE,e),this._getForceContribution(t.children.SW,e),this._getForceContribution(t.children.SE,e)}_getForceContribution(t,e){if(t.childrenCount>0){const i=t.centerOfMass.x-e.x,o=t.centerOfMass.y-e.y,s=Math.sqrt(i*i+o*o);s*t.calcSize>this.thetaInversed?this._calculateForces(s,i,o,e,t):4===t.childrenCount?this._getForceContributions(t,e):t.children.data.id!=e.id&&this._calculateForces(s,i,o,e,t)}}_calculateForces(t,e,i,o,s){0===t&&(e=t=.1),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const n=this.options.gravitationalConstant*s.mass*o.options.mass/Math.pow(t,3),r=e*n,d=i*n;this.physicsBody.forces[o.id].x+=r,this.physicsBody.forces[o.id].y+=d}_formBarnesHutTree(t,e){let i;const o=e.length;let s=t[e[0]].x,n=t[e[0]].y,r=t[e[0]].x,d=t[e[0]].y;for(let i=1;i<o;i++){const o=t[e[i]],a=o.x,h=o.y;o.options.mass>0&&(a<s&&(s=a),a>r&&(r=a),h<n&&(n=h),h>d&&(d=h))}const a=Math.abs(r-s)-Math.abs(d-n);a>0?(n-=.5*a,d+=.5*a):(s+=.5*a,r-=.5*a);const h=Math.max(1e-5,Math.abs(r-s)),l=.5*h,c=.5*(s+r),p=.5*(n+d),u={root:{centerOfMass:{x:0,y:0},mass:0,range:{minX:c-l,maxX:c+l,minY:p-l,maxY:p+l},size:h,calcSize:1/h,children:{data:null},maxWidth:0,level:0,childrenCount:4}};this._splitBranch(u.root);for(let s=0;s<o;s++)i=t[e[s]],i.options.mass>0&&this._placeInTree(u.root,i);return u}_updateBranchMass(t,e){const i=t.centerOfMass,o=t.mass+e.options.mass,s=1/o;i.x=i.x*t.mass+e.x*e.options.mass,i.x*=s,i.y=i.y*t.mass+e.y*e.options.mass,i.y*=s,t.mass=o;const n=Math.max(Math.max(e.height,e.radius),e.width);t.maxWidth=t.maxWidth<n?n:t.maxWidth}_placeInTree(t,e,i){1==i&&void 0!==i||this._updateBranchMass(t,e);const o=t.children.NW.range;let s;s=o.maxX>e.x?o.maxY>e.y?"NW":"SW":o.maxY>e.y?"NE":"SE",this._placeInRegion(t,e,s)}_placeInRegion(t,e,i){const o=t.children[i];switch(o.childrenCount){case 0:o.children.data=e,o.childrenCount=1,this._updateBranchMass(o,e);break;case 1:o.children.data.x===e.x&&o.children.data.y===e.y?(e.x+=this._rng(),e.y+=this._rng()):(this._splitBranch(o),this._placeInTree(o,e));break;case 4:this._placeInTree(o,e)}}_splitBranch(t){let e=null;1===t.childrenCount&&(e=t.children.data,t.mass=0,t.centerOfMass.x=0,t.centerOfMass.y=0),t.childrenCount=4,t.children.data=null,this._insertRegion(t,"NW"),this._insertRegion(t,"NE"),this._insertRegion(t,"SW"),this._insertRegion(t,"SE"),null!=e&&this._placeInTree(t,e)}_insertRegion(t,e){let i,o,s,n;const r=.5*t.size;switch(e){case"NW":i=t.range.minX,o=t.range.minX+r,s=t.range.minY,n=t.range.minY+r;break;case"NE":i=t.range.minX+r,o=t.range.maxX,s=t.range.minY,n=t.range.minY+r;break;case"SW":i=t.range.minX,o=t.range.minX+r,s=t.range.minY+r,n=t.range.maxY;break;case"SE":i=t.range.minX+r,o=t.range.maxX,s=t.range.minY+r,n=t.range.maxY}t.children[e]={centerOfMass:{x:0,y:0},mass:0,range:{minX:i,maxX:o,minY:s,maxY:n},size:.5*t.size,calcSize:2*t.calcSize,children:{data:null},maxWidth:0,level:t.level+1,childrenCount:0}}_debug(t,e){void 0!==this.barnesHutTree&&(t.lineWidth=1,this._drawBranch(this.barnesHutTree.root,t,e))}_drawBranch(t,e,i){void 0===i&&(i="#FF0000"),4===t.childrenCount&&(this._drawBranch(t.children.NW,e),this._drawBranch(t.children.NE,e),this._drawBranch(t.children.SE,e),this._drawBranch(t.children.SW,e)),e.strokeStyle=i,e.beginPath(),e.moveTo(t.range.minX,t.range.minY),e.lineTo(t.range.maxX,t.range.minY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.minY),e.lineTo(t.range.maxX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.maxX,t.range.maxY),e.lineTo(t.range.minX,t.range.maxY),e.stroke(),e.beginPath(),e.moveTo(t.range.minX,t.range.maxY),e.lineTo(t.range.minX,t.range.minY),e.stroke()}}class Gt{constructor(t,e,o){this._rng=i.Alea("REPULSION SOLVER"),this.body=t,this.physicsBody=e,this.setOptions(o)}setOptions(t){this.options=t}solve(){let t,e,i,o,s,n,r,d;const a=this.body.nodes,h=this.physicsBody.physicsNodeIndices,l=this.physicsBody.forces,c=this.options.nodeDistance,p=-2/3/c,u=4/3;for(let g=0;g<h.length-1;g++){r=a[h[g]];for(let b=g+1;b<h.length;b++)d=a[h[b]],t=d.x-r.x,e=d.y-r.y,i=Math.sqrt(t*t+e*e),0===i&&(i=.1*this._rng(),t=i),i<2*c&&(n=i<.5*c?1:p*i+u,n/=i,o=t*n,s=e*n,l[r.id].x-=o,l[r.id].y-=s,l[d.id].x+=o,l[d.id].y+=s)}}}class Zt{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t,this.overlapAvoidanceFactor=Math.max(0,Math.min(1,this.options.avoidOverlap||0))}solve(){const t=this.body.nodes,e=this.physicsBody.physicsNodeIndices,i=this.physicsBody.forces,o=this.options.nodeDistance;for(let s=0;s<e.length-1;s++){const n=t[e[s]];for(let r=s+1;r<e.length;r++){const s=t[e[r]];if(n.level===s.level){const t=o+this.overlapAvoidanceFactor*((n.shape.radius||0)/2+(s.shape.radius||0)/2),e=s.x-n.x,r=s.y-n.y,d=Math.sqrt(e*e+r*r),a=.05;let h;h=d<t?-Math.pow(a*d,2)+Math.pow(a*t,2):0,0!==d&&(h/=d);const l=e*h,c=r*h;i[n.id].x-=l,i[n.id].y-=c,i[s.id].x+=l,i[s.id].y+=c}}}}}class $t{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e;const i=this.physicsBody.physicsEdgeIndices,o=this.body.edges;let s,n,r;for(let d=0;d<i.length;d++)e=o[i[d]],!0===e.connected&&e.toId!==e.fromId&&void 0!==this.body.nodes[e.toId]&&void 0!==this.body.nodes[e.fromId]&&(void 0!==e.edgeType.via?(t=void 0===e.options.length?this.options.springLength:e.options.length,s=e.to,n=e.edgeType.via,r=e.from,this._calculateSpringForce(s,n,.5*t),this._calculateSpringForce(n,r,.5*t)):(t=void 0===e.options.length?1.5*this.options.springLength:e.options.length,this._calculateSpringForce(e.from,e.to,t)))}_calculateSpringForce(t,e,i){const o=t.x-e.x,s=t.y-e.y,n=Math.max(Math.sqrt(o*o+s*s),.01),r=this.options.springConstant*(i-n)/n,d=o*r,a=s*r;void 0!==this.physicsBody.forces[t.id]&&(this.physicsBody.forces[t.id].x+=d,this.physicsBody.forces[t.id].y+=a),void 0!==this.physicsBody.forces[e.id]&&(this.physicsBody.forces[e.id].x-=d,this.physicsBody.forces[e.id].y-=a)}}class Qt{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o,s,n,r,d;const a=this.body.edges,h=.5,l=this.physicsBody.physicsEdgeIndices,c=this.physicsBody.physicsNodeIndices,p=this.physicsBody.forces;for(let t=0;t<c.length;t++){const e=c[t];p[e].springFx=0,p[e].springFy=0}for(let c=0;c<l.length;c++)e=a[l[c]],!0===e.connected&&(t=void 0===e.options.length?this.options.springLength:e.options.length,i=e.from.x-e.to.x,o=e.from.y-e.to.y,d=Math.sqrt(i*i+o*o),d=0===d?.01:d,r=this.options.springConstant*(t-d)/d,s=i*r,n=o*r,e.to.level!=e.from.level?(void 0!==p[e.toId]&&(p[e.toId].springFx-=s,p[e.toId].springFy-=n),void 0!==p[e.fromId]&&(p[e.fromId].springFx+=s,p[e.fromId].springFy+=n)):(void 0!==p[e.toId]&&(p[e.toId].x-=h*s,p[e.toId].y-=h*n),void 0!==p[e.fromId]&&(p[e.fromId].x+=h*s,p[e.fromId].y+=h*n)));let u,g;r=1;for(let t=0;t<c.length;t++){const e=c[t];u=Math.min(r,Math.max(-r,p[e].springFx)),g=Math.min(r,Math.max(-r,p[e].springFy)),p[e].x+=u,p[e].y+=g}let b=0,m=0;for(let t=0;t<c.length;t++){const e=c[t];b+=p[e].x,m+=p[e].y}const f=b/c.length,y=m/c.length;for(let t=0;t<c.length;t++){const e=c[t];p[e].x-=f,p[e].y-=y}}}class Jt{constructor(t,e,i){this.body=t,this.physicsBody=e,this.setOptions(i)}setOptions(t){this.options=t}solve(){let t,e,i,o;const s=this.body.nodes,n=this.physicsBody.physicsNodeIndices,r=this.physicsBody.forces;for(let d=0;d<n.length;d++){o=s[n[d]],t=-o.x,e=-o.y,i=Math.sqrt(t*t+e*e),this._calculateForces(i,t,e,r,o)}}_calculateForces(t,e,i,o,s){const n=0===t?0:this.options.centralGravity/t;o[s.id].x=e*n,o[s.id].y=i*n}}class te extends Kt{constructor(t,e,o){super(t,e,o),this._rng=i.Alea("FORCE ATLAS 2 BASED REPULSION SOLVER")}_calculateForces(t,e,i,o,s){0===t&&(e=t=.1*this._rng()),this.overlapAvoidanceFactor<1&&o.shape.radius&&(t=Math.max(.1+this.overlapAvoidanceFactor*o.shape.radius,t-o.shape.radius));const n=o.edges.length+1,r=this.options.gravitationalConstant*s.mass*o.options.mass*n/Math.pow(t,2),d=e*r,a=i*r;this.physicsBody.forces[o.id].x+=d,this.physicsBody.forces[o.id].y+=a}}class ee extends Jt{constructor(t,e,i){super(t,e,i)}_calculateForces(t,e,i,o,s){if(t>0){const t=s.edges.length+1,n=this.options.centralGravity*t*s.options.mass;o[s.id].x=e*n,o[s.id].y=i*n}}}class ie{constructor(t){this.body=t,this.physicsBody={physicsNodeIndices:[],physicsEdgeIndices:[],forces:{},velocities:{}},this.physicsEnabled=!0,this.simulationInterval=1e3/60,this.requiresTimeout=!0,this.previousStates={},this.referenceState={},this.freezeCache={},this.renderTimer=void 0,this.adaptiveTimestep=!1,this.adaptiveTimestepEnabled=!1,this.adaptiveCounter=0,this.adaptiveInterval=3,this.stabilized=!1,this.startedStabilization=!1,this.stabilizationIterations=0,this.ready=!1,this.options={},this.defaultOptions={enabled:!0,barnesHut:{theta:.5,gravitationalConstant:-2e3,centralGravity:.3,springLength:95,springConstant:.04,damping:.09,avoidOverlap:0},forceAtlas2Based:{theta:.5,gravitationalConstant:-50,centralGravity:.01,springConstant:.08,springLength:100,damping:.4,avoidOverlap:0},repulsion:{centralGravity:.2,springLength:200,springConstant:.05,nodeDistance:100,damping:.09,avoidOverlap:0},hierarchicalRepulsion:{centralGravity:0,springLength:100,springConstant:.01,nodeDistance:120,damping:.09},maxVelocity:50,minVelocity:.75,solver:"barnesHut",stabilization:{enabled:!0,iterations:1e3,updateInterval:50,onlyDynamicEdges:!1,fit:!0},timestep:.5,adaptiveTimestep:!0,wind:{x:0,y:0}},Object.assign(this.options,this.defaultOptions),this.timestep=.5,this.layoutFailed=!1,this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("initPhysics",()=>{this.initPhysics()}),this.body.emitter.on("_layoutFailed",()=>{this.layoutFailed=!0}),this.body.emitter.on("resetPhysics",()=>{this.stopSimulation(),this.ready=!1}),this.body.emitter.on("disablePhysics",()=>{this.physicsEnabled=!1,this.stopSimulation()}),this.body.emitter.on("restorePhysics",()=>{this.setOptions(this.options),!0===this.ready&&this.startSimulation()}),this.body.emitter.on("startSimulation",()=>{!0===this.ready&&this.startSimulation()}),this.body.emitter.on("stopSimulation",()=>{this.stopSimulation()}),this.body.emitter.on("destroy",()=>{this.stopSimulation(!1),this.body.emitter.off()}),this.body.emitter.on("_dataChanged",()=>{this.updatePhysicsData()})}setOptions(t){if(void 0!==t)if(!1===t)this.options.enabled=!1,this.physicsEnabled=!1,this.stopSimulation();else if(!0===t)this.options.enabled=!0,this.physicsEnabled=!0,this.startSimulation();else{this.physicsEnabled=!0,i.selectiveNotDeepExtend(["stabilization"],this.options,t),i.mergeOptions(this.options,t,"stabilization"),void 0===t.enabled&&(this.options.enabled=!0),!1===this.options.enabled&&(this.physicsEnabled=!1,this.stopSimulation());const e=this.options.wind;e&&(("number"!=typeof e.x||Number.isNaN(e.x))&&(e.x=0),("number"!=typeof e.y||Number.isNaN(e.y))&&(e.y=0)),this.timestep=this.options.timestep}this.init()}init(){let t;"forceAtlas2Based"===this.options.solver?(t=this.options.forceAtlas2Based,this.nodesSolver=new te(this.body,this.physicsBody,t),this.edgesSolver=new $t(this.body,this.physicsBody,t),this.gravitySolver=new ee(this.body,this.physicsBody,t)):"repulsion"===this.options.solver?(t=this.options.repulsion,this.nodesSolver=new Gt(this.body,this.physicsBody,t),this.edgesSolver=new $t(this.body,this.physicsBody,t),this.gravitySolver=new Jt(this.body,this.physicsBody,t)):"hierarchicalRepulsion"===this.options.solver?(t=this.options.hierarchicalRepulsion,this.nodesSolver=new Zt(this.body,this.physicsBody,t),this.edgesSolver=new Qt(this.body,this.physicsBody,t),this.gravitySolver=new Jt(this.body,this.physicsBody,t)):(t=this.options.barnesHut,this.nodesSolver=new Kt(this.body,this.physicsBody,t),this.edgesSolver=new $t(this.body,this.physicsBody,t),this.gravitySolver=new Jt(this.body,this.physicsBody,t)),this.modelOptions=t}initPhysics(){!0===this.physicsEnabled&&!0===this.options.enabled?!0===this.options.stabilization.enabled?this.stabilize():(this.stabilized=!1,this.ready=!0,this.body.emitter.emit("fit",{},this.layoutFailed),this.startSimulation()):(this.ready=!0,this.body.emitter.emit("fit"))}startSimulation(){!0===this.physicsEnabled&&!0===this.options.enabled?(this.stabilized=!1,this.adaptiveTimestep=!1,this.body.emitter.emit("_resizeNodes"),void 0===this.viewFunction&&(this.viewFunction=this.simulationStep.bind(this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))):this.body.emitter.emit("_redraw")}stopSimulation(t=!0){this.stabilized=!0,!0===t&&this._emitStabilized(),void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.viewFunction=void 0,!0===t&&this.body.emitter.emit("_stopRendering"))}simulationStep(){const t=Date.now();this.physicsTick();(Date.now()-t<.4*this.simulationInterval||!0===this.runDoubleSpeed)&&!1===this.stabilized&&(this.physicsTick(),this.runDoubleSpeed=!0),!0===this.stabilized&&this.stopSimulation()}_emitStabilized(t=this.stabilizationIterations){(this.stabilizationIterations>1||!0===this.startedStabilization)&&setTimeout(()=>{this.body.emitter.emit("stabilized",{iterations:t}),this.startedStabilization=!1,this.stabilizationIterations=0},0)}physicsStep(){this.gravitySolver.solve(),this.nodesSolver.solve(),this.edgesSolver.solve(),this.moveNodes()}adjustTimeStep(){!0===this._evaluateStepQuality()?this.timestep=1.2*this.timestep:this.timestep/1.2<this.options.timestep?this.timestep=this.options.timestep:(this.adaptiveCounter=-1,this.timestep=Math.max(this.options.timestep,this.timestep/1.2))}physicsTick(){if(this._startStabilizing(),!0!==this.stabilized){if(!0===this.adaptiveTimestep&&!0===this.adaptiveTimestepEnabled){this.adaptiveCounter%this.adaptiveInterval===0?(this.timestep=2*this.timestep,this.physicsStep(),this.revert(),this.timestep=.5*this.timestep,this.physicsStep(),this.physicsStep(),this.adjustTimeStep()):this.physicsStep(),this.adaptiveCounter+=1}else this.timestep=this.options.timestep,this.physicsStep();!0===this.stabilized&&this.revert(),this.stabilizationIterations++}}updatePhysicsData(){this.physicsBody.forces={},this.physicsBody.physicsNodeIndices=[],this.physicsBody.physicsEdgeIndices=[];const t=this.body.nodes,e=this.body.edges;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&!0===t[e].options.physics&&this.physicsBody.physicsNodeIndices.push(t[e].id);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&!0===e[t].options.physics&&this.physicsBody.physicsEdgeIndices.push(e[t].id);for(let t=0;t<this.physicsBody.physicsNodeIndices.length;t++){const e=this.physicsBody.physicsNodeIndices[t];this.physicsBody.forces[e]={x:0,y:0},void 0===this.physicsBody.velocities[e]&&(this.physicsBody.velocities[e]={x:0,y:0})}for(const e in this.physicsBody.velocities)void 0===t[e]&&delete this.physicsBody.velocities[e]}revert(){const t=Object.keys(this.previousStates),e=this.body.nodes,i=this.physicsBody.velocities;this.referenceState={};for(let o=0;o<t.length;o++){const s=t[o];void 0!==e[s]?!0===e[s].options.physics&&(this.referenceState[s]={positions:{x:e[s].x,y:e[s].y}},i[s].x=this.previousStates[s].vx,i[s].y=this.previousStates[s].vy,e[s].x=this.previousStates[s].x,e[s].y=this.previousStates[s].y):delete this.previousStates[s]}}_evaluateStepQuality(){let t,e,i;const o=this.body.nodes,s=this.referenceState;for(const n in this.referenceState)if(Object.prototype.hasOwnProperty.call(this.referenceState,n)&&void 0!==o[n]&&(t=o[n].x-s[n].positions.x,e=o[n].y-s[n].positions.y,i=Math.sqrt(Math.pow(t,2)+Math.pow(e,2)),i>.3))return!1;return!0}moveNodes(){const t=this.physicsBody.physicsNodeIndices;let e=0,i=0;for(let o=0;o<t.length;o++){const s=t[o],n=this._performStep(s);e=Math.max(e,n),i+=n}this.adaptiveTimestepEnabled=i/t.length<5,this.stabilized=e<this.options.minVelocity}calculateComponentVelocity(t,e,i){t+=(e-this.modelOptions.damping*t)/i*this.timestep;const o=this.options.maxVelocity||1e9;return Math.abs(t)>o&&(t=t>0?o:-o),t}_performStep(t){const e=this.body.nodes[t],i=this.physicsBody.forces[t];this.options.wind&&(i.x+=this.options.wind.x,i.y+=this.options.wind.y);const o=this.physicsBody.velocities[t];this.previousStates[t]={x:e.x,y:e.y,vx:o.x,vy:o.y},!1===e.options.fixed.x?(o.x=this.calculateComponentVelocity(o.x,i.x,e.options.mass),e.x+=o.x*this.timestep):(i.x=0,o.x=0),!1===e.options.fixed.y?(o.y=this.calculateComponentVelocity(o.y,i.y,e.options.mass),e.y+=o.y*this.timestep):(i.y=0,o.y=0);return Math.sqrt(Math.pow(o.x,2)+Math.pow(o.y,2))}_freezeNodes(){const t=this.body.nodes;for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&t[e].x&&t[e].y){const i=t[e].options.fixed;this.freezeCache[e]={x:i.x,y:i.y},i.x=!0,i.y=!0}}_restoreFrozenNodes(){const t=this.body.nodes;for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.freezeCache[e]&&(t[e].options.fixed.x=this.freezeCache[e].x,t[e].options.fixed.y=this.freezeCache[e].y);this.freezeCache={}}stabilize(t=this.options.stabilization.iterations){"number"!=typeof t&&(t=this.options.stabilization.iterations,console.error("The stabilize method needs a numeric amount of iterations. Switching to default: ",t)),0!==this.physicsBody.physicsNodeIndices.length?(this.adaptiveTimestep=this.options.adaptiveTimestep,this.body.emitter.emit("_resizeNodes"),this.stopSimulation(),this.stabilized=!1,this.body.emitter.emit("_blockRedraw"),this.targetIterations=t,!0===this.options.stabilization.onlyDynamicEdges&&this._freezeNodes(),this.stabilizationIterations=0,setTimeout(()=>this._stabilizationBatch(),0)):this.ready=!0}_startStabilizing(){return!0!==this.startedStabilization&&(this.body.emitter.emit("startStabilizing"),this.startedStabilization=!0,!0)}_stabilizationBatch(){const t=()=>!1===this.stabilized&&this.stabilizationIterations<this.targetIterations,e=()=>{this.body.emitter.emit("stabilizationProgress",{iterations:this.stabilizationIterations,total:this.targetIterations})};this._startStabilizing()&&e();let i=0;for(;t()&&i<this.options.stabilization.updateInterval;)this.physicsTick(),i++;e(),t()?setTimeout(this._stabilizationBatch.bind(this),0):this._finalizeStabilization()}_finalizeStabilization(){this.body.emitter.emit("_allowRedraw"),!0===this.options.stabilization.fit&&this.body.emitter.emit("fit"),!0===this.options.stabilization.onlyDynamicEdges&&this._restoreFrozenNodes(),this.body.emitter.emit("stabilizationIterationsDone"),this.body.emitter.emit("_requestRedraw"),!0===this.stabilized?this._emitStabilized():this.startSimulation(),this.ready=!0}_drawForces(t){for(let e=0;e<this.physicsBody.physicsNodeIndices.length;e++){const o=this.physicsBody.physicsNodeIndices[e],s=this.body.nodes[o],n=this.physicsBody.forces[o],r=20,d=.03,a=Math.sqrt(Math.pow(n.x,2)+Math.pow(n.x,2)),h=Math.min(Math.max(5,a),15),l=3*h,c=i.HSVToHex((180-180*Math.min(1,Math.max(0,d*a)))/360,1,1),p={x:s.x+r*n.x,y:s.y+r*n.y};t.lineWidth=h,t.strokeStyle=c,t.beginPath(),t.moveTo(s.x,s.y),t.lineTo(p.x,p.y),t.stroke();const u=Math.atan2(n.y,n.x);t.fillStyle=c,At.draw(t,{type:"arrow",point:p,angle:u,length:l}),t.fill()}}}class oe{constructor(){}static getRange(t,e=[]){let i,o=1e9,s=-1e9,n=1e9,r=-1e9;if(e.length>0)for(let d=0;d<e.length;d++)i=t[e[d]],n>i.shape.boundingBox.left&&(n=i.shape.boundingBox.left),r<i.shape.boundingBox.right&&(r=i.shape.boundingBox.right),o>i.shape.boundingBox.top&&(o=i.shape.boundingBox.top),s<i.shape.boundingBox.bottom&&(s=i.shape.boundingBox.bottom);return 1e9===n&&-1e9===r&&1e9===o&&-1e9===s&&(o=0,s=0,n=0,r=0),{minX:n,maxX:r,minY:o,maxY:s}}static getRangeCore(t,e=[]){let i,o=1e9,s=-1e9,n=1e9,r=-1e9;if(e.length>0)for(let d=0;d<e.length;d++)i=t[e[d]],n>i.x&&(n=i.x),r<i.x&&(r=i.x),o>i.y&&(o=i.y),s<i.y&&(s=i.y);return 1e9===n&&-1e9===r&&1e9===o&&-1e9===s&&(o=0,s=0,n=0,r=0),{minX:n,maxX:r,minY:o,maxY:s}}static findCenter(t){return{x:.5*(t.maxX+t.minX),y:.5*(t.maxY+t.minY)}}static cloneOptions(t,e){const o={};return void 0===e||"node"===e?(i.deepExtend(o,t.options,!0),o.x=t.x,o.y=t.y,o.amountOfConnections=t.edges.length):i.deepExtend(o,t.options,!0),o}}class se extends Et{constructor(t,e,i,o,s,n){super(t,e,i,o,s,n),this.isCluster=!0,this.containedNodes={},this.containedEdges={}}_openChildCluster(t){const e=this.body.nodes[t];if(void 0===this.containedNodes[t])throw new Error("node with id: "+t+" not in current cluster");if(!e.isCluster)throw new Error("node with id: "+t+" is not a cluster");delete this.containedNodes[t],i.forEach(e.edges,t=>{delete this.containedEdges[t.id]}),i.forEach(e.containedNodes,(t,e)=>{this.containedNodes[e]=t}),e.containedNodes={},i.forEach(e.containedEdges,(t,e)=>{this.containedEdges[e]=t}),e.containedEdges={},i.forEach(e.edges,t=>{i.forEach(this.edges,e=>{const o=e.clusteringEdgeReplacingIds.indexOf(t.id);-1!==o&&(i.forEach(t.clusteringEdgeReplacingIds,t=>{e.clusteringEdgeReplacingIds.push(t),this.body.edges[t].edgeReplacedById=e.id}),e.clusteringEdgeReplacingIds.splice(o,1))})}),e.edges=[]}}class ne{constructor(t){this.body=t,this.clusteredNodes={},this.clusteredEdges={},this.options={},this.defaultOptions={},Object.assign(this.options,this.defaultOptions),this.body.emitter.on("_resetData",()=>{this.clusteredNodes={},this.clusteredEdges={}})}clusterByHubsize(t,e){void 0===t?t=this._getHubSize():"object"==typeof t&&(e=this._checkOptions(t),t=this._getHubSize());const i=[];for(let e=0;e<this.body.nodeIndices.length;e++){const o=this.body.nodes[this.body.nodeIndices[e]];o.edges.length>=t&&i.push(o.id)}for(let t=0;t<i.length;t++)this.clusterByConnection(i[t],e,!0);this.body.emitter.emit("_dataChanged")}cluster(t={},e=!0){if(void 0===t.joinCondition)throw new Error("Cannot call clusterByNodeData without a joinCondition function in the options.");t=this._checkOptions(t);const o={},s={};i.forEach(this.body.nodes,(e,n)=>{e.options&&!0===t.joinCondition(e.options)&&(o[n]=e,i.forEach(e.edges,t=>{void 0===this.clusteredEdges[t.id]&&(s[t.id]=t)}))}),this._cluster(o,s,t,e)}clusterByEdgeCount(t,e,i=!0){e=this._checkOptions(e);const o=[],s={};let n,r,d;for(let i=0;i<this.body.nodeIndices.length;i++){const a={},h={},l=this.body.nodeIndices[i],c=this.body.nodes[l];if(void 0===s[l]){d=0,r=[];for(let t=0;t<c.edges.length;t++)n=c.edges[t],void 0===this.clusteredEdges[n.id]&&(n.toId!==n.fromId&&d++,r.push(n));if(d===t){const t=function(t){if(void 0===e.joinCondition||null===e.joinCondition)return!0;const i=oe.cloneOptions(t);return e.joinCondition(i)};let i=!0;for(let e=0;e<r.length;e++){n=r[e];const o=this._getConnectedId(n,l);if(!t(c)){i=!1;break}h[n.id]=n,a[l]=c,a[o]=this.body.nodes[o],s[l]=!0}if(Object.keys(a).length>0&&Object.keys(h).length>0&&!0===i){const t=function(){for(let t=0;t<o.length;++t)for(const e in a)if(void 0!==o[t].nodes[e])return o[t]}();if(void 0!==t){for(const e in a)void 0===t.nodes[e]&&(t.nodes[e]=a[e]);for(const e in h)void 0===t.edges[e]&&(t.edges[e]=h[e])}else o.push({nodes:a,edges:h})}}}}for(let t=0;t<o.length;t++)this._cluster(o[t].nodes,o[t].edges,e,!1);!0===i&&this.body.emitter.emit("_dataChanged")}clusterOutliers(t,e=!0){this.clusterByEdgeCount(1,t,e)}clusterBridges(t,e=!0){this.clusterByEdgeCount(2,t,e)}clusterByConnection(t,e,i=!0){if(void 0===t)throw new Error("No nodeId supplied to clusterByConnection!");if(void 0===this.body.nodes[t])throw new Error("The nodeId given to clusterByConnection does not exist!");const o=this.body.nodes[t];void 0===(e=this._checkOptions(e,o)).clusterNodeProperties.x&&(e.clusterNodeProperties.x=o.x),void 0===e.clusterNodeProperties.y&&(e.clusterNodeProperties.y=o.y),void 0===e.clusterNodeProperties.fixed&&(e.clusterNodeProperties.fixed={},e.clusterNodeProperties.fixed.x=o.options.fixed.x,e.clusterNodeProperties.fixed.y=o.options.fixed.y);const s={},n={},r=o.id,d=oe.cloneOptions(o);s[r]=o;for(let t=0;t<o.edges.length;t++){const i=o.edges[t];if(void 0===this.clusteredEdges[i.id]){const t=this._getConnectedId(i,r);if(void 0===this.clusteredNodes[t])if(t!==r)if(void 0===e.joinCondition)n[i.id]=i,s[t]=this.body.nodes[t];else{const o=oe.cloneOptions(this.body.nodes[t]);!0===e.joinCondition(d,o)&&(n[i.id]=i,s[t]=this.body.nodes[t])}else n[i.id]=i}}const a=Object.keys(s).map(function(t){return s[t].id});for(const t in s){if(!Object.prototype.hasOwnProperty.call(s,t))continue;const e=s[t];for(let t=0;t<e.edges.length;t++){const i=e.edges[t];a.indexOf(this._getConnectedId(i,e.id))>-1&&(n[i.id]=i)}}this._cluster(s,n,e,i)}_createClusterEdges(t,e,i,o){let s,n,r,d,a,h;const l=Object.keys(t),c=[];for(let o=0;o<l.length;o++){n=l[o],r=t[n];for(let o=0;o<r.edges.length;o++)s=r.edges[o],void 0===this.clusteredEdges[s.id]&&(s.toId==s.fromId?e[s.id]=s:s.toId==n?(d=i.id,a=s.fromId,h=a):(d=s.toId,a=i.id,h=d),void 0===t[h]&&c.push({edge:s,fromId:a,toId:d}))}const p=[],u=function(t){for(let e=0;e<p.length;e++){const i=p[e],o=t.fromId===i.fromId&&t.toId===i.toId,s=t.fromId===i.toId&&t.toId===i.fromId;if(o||s)return i}return null};for(let t=0;t<c.length;t++){const e=c[t],i=e.edge;let s=u(e);null===s?(s=this._createClusteredEdge(e.fromId,e.toId,i,o),p.push(s)):s.clusteringEdgeReplacingIds.push(i.id),this.body.edges[i.id].edgeReplacedById=s.id,this._backupEdgeOptions(i),i.setOptions({physics:!1})}}_checkOptions(t={}){return void 0===t.clusterEdgeProperties&&(t.clusterEdgeProperties={}),void 0===t.clusterNodeProperties&&(t.clusterNodeProperties={}),t}_cluster(t,e,o,n=!0){const r=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&void 0!==this.clusteredNodes[e]&&r.push(e);for(let e=0;e<r.length;++e)delete t[r[e]];if(0==Object.keys(t).length)return;if(1==Object.keys(t).length&&1!=o.clusterNodeProperties.allowSingleNodeCluster)return;let d=i.deepExtend({},o.clusterNodeProperties);if(void 0!==o.processProperties){const i=[];for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const o=oe.cloneOptions(t[e]);i.push(o)}const s=[];for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"clusterEdge:"!==t.substr(0,12)){const i=oe.cloneOptions(e[t],"edge");s.push(i)}if(d=o.processProperties(d,i,s),!d)throw new Error("The processProperties function does not return properties!")}void 0===d.id&&(d.id="cluster:"+s.v4());const a=d.id;let h;void 0===d.label&&(d.label="cluster"),void 0===d.x&&(h=this._getClusterPosition(t),d.x=h.x),void 0===d.y&&(void 0===h&&(h=this._getClusterPosition(t)),d.y=h.y),d.id=a;const l=this.body.functions.createNode(d,se);l.containedNodes=t,l.containedEdges=e,l.clusterEdgeProperties=o.clusterEdgeProperties,this.body.nodes[d.id]=l,this._clusterEdges(t,e,d,o.clusterEdgeProperties),d.id=void 0,!0===n&&this.body.emitter.emit("_dataChanged")}_backupEdgeOptions(t){void 0===this.clusteredEdges[t.id]&&(this.clusteredEdges[t.id]={physics:t.options.physics})}_restoreEdge(t){const e=this.clusteredEdges[t.id];void 0!==e&&(t.setOptions({physics:e.physics}),delete this.clusteredEdges[t.id])}isCluster(t){return void 0!==this.body.nodes[t]?!0===this.body.nodes[t].isCluster:(console.error("Node does not exist."),!1)}_getClusterPosition(t){const e=Object.keys(t);let i,o=t[e[0]].x,s=t[e[0]].x,n=t[e[0]].y,r=t[e[0]].y;for(let d=1;d<e.length;d++)i=t[e[d]],o=i.x<o?i.x:o,s=i.x>s?i.x:s,n=i.y<n?i.y:n,r=i.y>r?i.y:r;return{x:.5*(o+s),y:.5*(n+r)}}openCluster(t,e,o=!0){if(void 0===t)throw new Error("No clusterNodeId supplied to openCluster.");const s=this.body.nodes[t];if(void 0===s)throw new Error("The clusterNodeId supplied to openCluster does not exist.");if(!0!==s.isCluster||void 0===s.containedNodes||void 0===s.containedEdges)throw new Error("The node:"+t+" is not a valid cluster.");const n=this.findNode(t),r=n.indexOf(t)-1;if(r>=0){const e=n[r];return this.body.nodes[e]._openChildCluster(t),delete this.body.nodes[t],void(!0===o&&this.body.emitter.emit("_dataChanged"))}const d=s.containedNodes,a=s.containedEdges;if(void 0!==e&&void 0!==e.releaseFunction&&"function"==typeof e.releaseFunction){const t={},i={x:s.x,y:s.y};for(const e in d)if(Object.prototype.hasOwnProperty.call(d,e)){const i=this.body.nodes[e];t[e]={x:i.x,y:i.y}}const o=e.releaseFunction(i,t);for(const t in d)if(Object.prototype.hasOwnProperty.call(d,t)){const e=this.body.nodes[t];void 0!==o[t]&&(e.x=void 0===o[t].x?s.x:o[t].x,e.y=void 0===o[t].y?s.y:o[t].y)}}else i.forEach(d,function(t){!1===t.options.fixed.x&&(t.x=s.x),!1===t.options.fixed.y&&(t.y=s.y)});for(const t in d)if(Object.prototype.hasOwnProperty.call(d,t)){const e=this.body.nodes[t];e.vx=s.vx,e.vy=s.vy,e.setOptions({physics:!0}),delete this.clusteredNodes[t]}const h=[];for(let t=0;t<s.edges.length;t++)h.push(s.edges[t]);for(let e=0;e<h.length;e++){const i=h[e],o=this._getConnectedId(i,t),s=this.clusteredNodes[o];for(let t=0;t<i.clusteringEdgeReplacingIds.length;t++){const e=i.clusteringEdgeReplacingIds[t],n=this.body.edges[e];if(void 0!==n)if(void 0!==s){const t=this.body.nodes[s.clusterId];t.containedEdges[n.id]=n,delete a[n.id];let e=n.fromId,i=n.toId;n.toId==o?i=s.clusterId:e=s.clusterId,this._createClusteredEdge(e,i,n,t.clusterEdgeProperties,{hidden:!1,physics:!0})}else this._restoreEdge(n)}i.remove()}for(const t in a)Object.prototype.hasOwnProperty.call(a,t)&&this._restoreEdge(a[t]);delete this.body.nodes[t],!0===o&&this.body.emitter.emit("_dataChanged")}getNodesInCluster(t){const e=[];if(!0===this.isCluster(t)){const i=this.body.nodes[t].containedNodes;for(const t in i)Object.prototype.hasOwnProperty.call(i,t)&&e.push(this.body.nodes[t].id)}return e}findNode(t){const e=[];let i,o=0;for(;void 0!==this.clusteredNodes[t]&&o<100;){if(i=this.body.nodes[t],void 0===i)return[];e.push(i.id),t=this.clusteredNodes[t].clusterId,o++}return i=this.body.nodes[t],void 0===i?[]:(e.push(i.id),e.reverse(),e)}updateClusteredNode(t,e){if(void 0===t)throw new Error("No clusteredNodeId supplied to updateClusteredNode.");if(void 0===e)throw new Error("No newOptions supplied to updateClusteredNode.");if(void 0===this.body.nodes[t])throw new Error("The clusteredNodeId supplied to updateClusteredNode does not exist.");this.body.nodes[t].setOptions(e),this.body.emitter.emit("_dataChanged")}updateEdge(t,e){if(void 0===t)throw new Error("No startEdgeId supplied to updateEdge.");if(void 0===e)throw new Error("No newOptions supplied to updateEdge.");if(void 0===this.body.edges[t])throw new Error("The startEdgeId supplied to updateEdge does not exist.");const i=this.getClusteredEdges(t);for(let t=0;t<i.length;t++){this.body.edges[i[t]].setOptions(e)}this.body.emitter.emit("_dataChanged")}getClusteredEdges(t){const e=[];let i=0;for(;void 0!==t&&void 0!==this.body.edges[t]&&i<100;)e.push(this.body.edges[t].id),t=this.body.edges[t].edgeReplacedById,i++;return e.reverse(),e}getBaseEdge(t){return this.getBaseEdges(t)[0]}getBaseEdges(t){const e=[t],i=[],o=[];let s=0;for(;e.length>0&&s<100;){const t=e.pop();if(void 0===t)continue;const n=this.body.edges[t];if(void 0===n)continue;s++;const r=n.clusteringEdgeReplacingIds;if(void 0===r)o.push(t);else for(let t=0;t<r.length;++t){const o=r[t];-1===e.indexOf(r)&&-1===i.indexOf(r)&&e.push(o)}i.push(t)}return o}_getConnectedId(t,e){return t.toId!=e?t.toId:(t.fromId,t.fromId)}_getHubSize(){let t=0,e=0,i=0,o=0;for(let s=0;s<this.body.nodeIndices.length;s++){const n=this.body.nodes[this.body.nodeIndices[s]];n.edges.length>o&&(o=n.edges.length),t+=n.edges.length,e+=Math.pow(n.edges.length,2),i+=1}t/=i,e/=i;const s=e-Math.pow(t,2),n=Math.sqrt(s);let r=Math.floor(t+2*n);return r>o&&(r=o),r}_createClusteredEdge(t,e,o,n,r){const d=oe.cloneOptions(o,"edge");i.deepExtend(d,n),d.from=t,d.to=e,d.id="clusterEdge:"+s.v4(),void 0!==r&&i.deepExtend(d,r);const a=this.body.functions.createEdge(d);return a.clusteringEdgeReplacingIds=[o.id],a.connect(),this.body.edges[a.id]=a,a}_clusterEdges(t,e,i,o){if(e instanceof Yt){const t=e,i={};i[t.id]=t,e=i}if(t instanceof Et){const e=t,i={};i[e.id]=e,t=i}if(null==i)throw new Error("_clusterEdges: parameter clusterNode required");void 0===o&&(o=i.clusterEdgeProperties),this._createClusterEdges(t,e,i,o);for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&void 0!==this.body.edges[t]){const e=this.body.edges[t];this._backupEdgeOptions(e),e.setOptions({physics:!1})}for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clusteredNodes[e]={clusterId:i.id,node:this.body.nodes[e]},this.body.nodes[e].setOptions({physics:!1}))}_getClusterNodeForNode(t){if(void 0===t)return;const e=this.clusteredNodes[t];if(void 0===e)return;const i=e.clusterId;return void 0!==i?this.body.nodes[i]:void 0}_filter(t,e){const o=[];return i.forEach(t,t=>{e(t)&&o.push(t)}),o}_updateState(){let t;const e=[],o={},s=t=>{i.forEach(this.body.nodes,e=>{!0===e.isCluster&&t(e)})};for(t in this.clusteredNodes){if(!Object.prototype.hasOwnProperty.call(this.clusteredNodes,t))continue;void 0===this.body.nodes[t]&&e.push(t)}s(function(t){for(let i=0;i<e.length;i++)delete t.containedNodes[e[i]]});for(let t=0;t<e.length;t++)delete this.clusteredNodes[e[t]];i.forEach(this.clusteredEdges,t=>{const e=this.body.edges[t];void 0!==e&&e.endPointsValid()||(o[t]=t)}),s(function(t){i.forEach(t.containedEdges,(t,e)=>{t.endPointsValid()||o[e]||(o[e]=e)})}),i.forEach(this.body.edges,(t,e)=>{let s=!0;const n=t.clusteringEdgeReplacingIds;if(void 0!==n){let t=0;i.forEach(n,e=>{const i=this.body.edges[e];void 0!==i&&i.endPointsValid()&&(t+=1)}),s=t>0}t.endPointsValid()&&s||(o[e]=e)}),s(t=>{i.forEach(o,e=>{delete t.containedEdges[e],i.forEach(t.edges,(i,s)=>{i.id!==e?i.clusteringEdgeReplacingIds=this._filter(i.clusteringEdgeReplacingIds,function(t){return!o[t]}):t.edges[s]=null}),t.edges=this._filter(t.edges,function(t){return null!==t})})}),i.forEach(o,t=>{delete this.clusteredEdges[t]}),i.forEach(o,t=>{delete this.body.edges[t]});const n=Object.keys(this.body.edges);i.forEach(n,t=>{const e=this.body.edges[t],i=this._isClusteredNode(e.fromId)||this._isClusteredNode(e.toId);if(i!==this._isClusteredEdge(e.id))if(i){const t=this._getClusterNodeForNode(e.fromId);void 0!==t&&this._clusterEdges(this.body.nodes[e.fromId],e,t);const i=this._getClusterNodeForNode(e.toId);void 0!==i&&this._clusterEdges(this.body.nodes[e.toId],e,i)}else delete this._clusterEdges[t],this._restoreEdge(e)});let r=!1,d=!0;for(;d;){const t=[];s(function(e){const i=Object.keys(e.containedNodes).length,o=!0===e.options.allowSingleNodeCluster;(o&&i<1||!o&&i<2)&&t.push(e.id)});for(let e=0;e<t.length;++e)this.openCluster(t[e],{},!1);d=t.length>0,r=r||d}r&&this._updateState()}_isClusteredNode(t){return void 0!==this.clusteredNodes[t]}_isClusteredEdge(t){return void 0!==this.clusteredEdges[t]}}class re{constructor(t,e){this.body=t,this.canvas=e,this.redrawRequested=!1,this.requestAnimationFrameRequestId=void 0,this.renderingActive=!1,this.renderRequests=0,this.allowRedraw=!0,this.dragging=!1,this.zooming=!1,this.options={},this.defaultOptions={hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1},Object.assign(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("dragStart",()=>{this.dragging=!0}),this.body.emitter.on("dragEnd",()=>{this.dragging=!1}),this.body.emitter.on("zoom",()=>{this.zooming=!0,window.clearTimeout(this.zoomTimeoutId),this.zoomTimeoutId=window.setTimeout(()=>{this.zooming=!1,this._requestRedraw.bind(this)()},250)}),this.body.emitter.on("_resizeNodes",()=>{this._resizeNodes()}),this.body.emitter.on("_redraw",()=>{!1===this.renderingActive&&this._redraw()}),this.body.emitter.on("_blockRedraw",()=>{this.allowRedraw=!1}),this.body.emitter.on("_allowRedraw",()=>{this.allowRedraw=!0,this.redrawRequested=!1}),this.body.emitter.on("_requestRedraw",this._requestRedraw.bind(this)),this.body.emitter.on("_startRendering",()=>{this.renderRequests+=1,this.renderingActive=!0,this._startRendering()}),this.body.emitter.on("_stopRendering",()=>{this.renderRequests-=1,this.renderingActive=this.renderRequests>0,this.requestAnimationFrameRequestId=void 0}),this.body.emitter.on("destroy",()=>{this.renderRequests=0,this.allowRedraw=!1,this.renderingActive=!1,window.cancelAnimationFrame(this.requestAnimationFrameRequestId),this.body.emitter.off()})}setOptions(t){if(void 0!==t){const e=["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag"];i.selectiveDeepExtend(e,this.options,t)}}_startRendering(){!0===this.renderingActive&&void 0===this.requestAnimationFrameRequestId&&(this.requestAnimationFrameRequestId=window.requestAnimationFrame(this._renderStep.bind(this),this.simulationInterval))}_renderStep(){!0===this.renderingActive&&(this.requestAnimationFrameRequestId=void 0,this._startRendering(),this._redraw())}redraw(){this.body.emitter.emit("setSize"),this._redraw()}_requestRedraw(){!0!==this.redrawRequested&&!1===this.renderingActive&&!0===this.allowRedraw&&(this.redrawRequested=!0,window.requestAnimationFrame(()=>{this._redraw(!1)}))}_redraw(t=!1){if(!0===this.allowRedraw){this.body.emitter.emit("initRedraw"),this.redrawRequested=!1;const e={drawExternalLabels:null};0!==this.canvas.frame.canvas.width&&0!==this.canvas.frame.canvas.height||this.canvas.setSize(),this.canvas.setTransform();const i=this.canvas.getContext(),o=this.canvas.frame.canvas.clientWidth,s=this.canvas.frame.canvas.clientHeight;if(i.clearRect(0,0,o,s),0===this.canvas.frame.clientWidth)return;if(i.save(),i.translate(this.body.view.translation.x,this.body.view.translation.y),i.scale(this.body.view.scale,this.body.view.scale),i.beginPath(),this.body.emitter.emit("beforeDrawing",i),i.closePath(),!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawEdges(i),!1===this.dragging||!0===this.dragging&&!1===this.options.hideNodesOnDrag){const{drawExternalLabels:o}=this._drawNodes(i,t);e.drawExternalLabels=o}!1===t&&(!1===this.dragging||!0===this.dragging&&!1===this.options.hideEdgesOnDrag)&&(!1===this.zooming||!0===this.zooming&&!1===this.options.hideEdgesOnZoom)&&this._drawArrows(i),null!=e.drawExternalLabels&&e.drawExternalLabels(),!1===t&&this._drawSelectionBox(i),i.beginPath(),this.body.emitter.emit("afterDrawing",i),i.closePath(),i.restore(),!0===t&&i.clearRect(0,0,o,s)}}_resizeNodes(){this.canvas.setTransform();const t=this.canvas.getContext();t.save(),t.translate(this.body.view.translation.x,this.body.view.translation.y),t.scale(this.body.view.scale,this.body.view.scale);const e=this.body.nodes;let i;for(const o in e)Object.prototype.hasOwnProperty.call(e,o)&&(i=e[o],i.resize(t),i.updateBoundingBox(t,i.selected));t.restore()}_drawNodes(t,e=!1){const i=this.body.nodes,o=this.body.nodeIndices;let s;const n=[],r=[],d=this.canvas.DOMtoCanvas({x:-20,y:-20}),a=this.canvas.DOMtoCanvas({x:this.canvas.frame.canvas.clientWidth+20,y:this.canvas.frame.canvas.clientHeight+20}),h={top:d.y,left:d.x,bottom:a.y,right:a.x},l=[];for(let d=0;d<o.length;d++)if(s=i[o[d]],s.hover)r.push(o[d]);else if(s.isSelected())n.push(o[d]);else if(!0===e){const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else if(!0===s.isBoundingBoxOverlappingWith(h)){const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}else s.updateBoundingBox(t,s.selected);let c;const p=n.length,u=r.length;for(c=0;c<p;c++){s=i[n[c]];const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}for(c=0;c<u;c++){s=i[r[c]];const e=s.draw(t);null!=e.drawExternalLabel&&l.push(e.drawExternalLabel)}return{drawExternalLabels:()=>{for(const t of l)t()}}}_drawEdges(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const s=e[i[o]];!0===s.connected&&s.draw(t)}}_drawArrows(t){const e=this.body.edges,i=this.body.edgeIndices;for(let o=0;o<i.length;o++){const s=e[i[o]];!0===s.connected&&s.drawArrows(t)}}_drawSelectionBox(t){if(this.body.selectionBox.show){t.beginPath();const e=this.body.selectionBox.position.end.x-this.body.selectionBox.position.start.x,i=this.body.selectionBox.position.end.y-this.body.selectionBox.position.start.y;t.rect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.fillStyle="rgba(151, 194, 252, 0.2)",t.fillRect(this.body.selectionBox.position.start.x,this.body.selectionBox.position.start.y,e,i),t.strokeStyle="rgba(151, 194, 252, 1)",t.stroke()}else t.closePath()}}function de(t,e){e.inputHandler=function(t){t.isFirst&&e(t)},t.on("hammer.input",e.inputHandler)}function ae(t,e){return e.inputHandler=function(t){t.isFinal&&e(t)},t.on("hammer.input",e.inputHandler)}class he{constructor(t){this.body=t,this.pixelRatio=1,this.cameraState={},this.initialized=!1,this.canvasViewCenter={},this._cleanupCallbacks=[],this.options={},this.defaultOptions={autoResize:!0,height:"100%",width:"100%"},Object.assign(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.once("resize",t=>{0!==t.width&&(this.body.view.translation.x=.5*t.width),0!==t.height&&(this.body.view.translation.y=.5*t.height)}),this.body.emitter.on("setSize",this.setSize.bind(this)),this.body.emitter.on("destroy",()=>{this.hammerFrame.destroy(),this.hammer.destroy(),this._cleanUp()})}setOptions(t){if(void 0!==t){const e=["width","height","autoResize"];i.selectiveDeepExtend(e,this.options,t)}if(this._cleanUp(),!0===this.options.autoResize){if(window.ResizeObserver){const t=new ResizeObserver(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")}),{frame:e}=this;t.observe(e),this._cleanupCallbacks.push(()=>{t.unobserve(e)})}else{const t=setInterval(()=>{!0===this.setSize()&&this.body.emitter.emit("_requestRedraw")},1e3);this._cleanupCallbacks.push(()=>{clearInterval(t)})}const t=this._onResize.bind(this);window.addEventListener("resize",t),this._cleanupCallbacks.push(()=>{window.removeEventListener("resize",t)})}}_cleanUp(){this._cleanupCallbacks.splice(0).reverse().forEach(t=>{try{t()}catch(t){console.error(t)}})}_onResize(){this.setSize(),this.body.emitter.emit("_redraw")}_getCameraState(t=this.pixelRatio){!0===this.initialized&&(this.cameraState.previousWidth=this.frame.canvas.width/t,this.cameraState.previousHeight=this.frame.canvas.height/t,this.cameraState.scale=this.body.view.scale,this.cameraState.position=this.DOMtoCanvas({x:.5*this.frame.canvas.width/t,y:.5*this.frame.canvas.height/t}))}_setCameraState(){if(void 0!==this.cameraState.scale&&0!==this.frame.canvas.clientWidth&&0!==this.frame.canvas.clientHeight&&0!==this.pixelRatio&&this.cameraState.previousWidth>0&&this.cameraState.previousHeight>0){const t=this.frame.canvas.width/this.pixelRatio/this.cameraState.previousWidth,e=this.frame.canvas.height/this.pixelRatio/this.cameraState.previousHeight;let i=this.cameraState.scale;1!=t&&1!=e?i=.5*this.cameraState.scale*(t+e):1!=t?i=this.cameraState.scale*t:1!=e&&(i=this.cameraState.scale*e),this.body.view.scale=i;const o=this.DOMtoCanvas({x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight}),s={x:o.x-this.cameraState.position.x,y:o.y-this.cameraState.position.y};this.body.view.translation.x+=s.x*this.body.view.scale,this.body.view.translation.y+=s.y*this.body.view.scale}}_prepareValue(t){if("number"==typeof t)return t+"px";if("string"==typeof t){if(-1!==t.indexOf("%")||-1!==t.indexOf("px"))return t;if(-1===t.indexOf("%"))return t+"px"}throw new Error("Could not use the value supplied for width or height:"+t)}_create(){for(;this.body.container.hasChildNodes();)this.body.container.removeChild(this.body.container.firstChild);if(this.frame=document.createElement("div"),this.frame.className="vis-network",this.frame.style.position="relative",this.frame.style.overflow="hidden",this.frame.tabIndex=0,this.frame.canvas=document.createElement("canvas"),this.frame.canvas.style.position="relative",this.frame.appendChild(this.frame.canvas),this.frame.canvas.getContext)this._setPixelRatio(),this.setTransform();else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.frame.canvas.appendChild(t)}this.body.container.appendChild(this.frame),this.body.view.scale=1,this.body.view.translation={x:.5*this.frame.canvas.clientWidth,y:.5*this.frame.canvas.clientHeight},this._bindHammer()}_bindHammer(){void 0!==this.hammer&&this.hammer.destroy(),this.drag={},this.pinch={},this.hammer=new i.Hammer(this.frame.canvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.get("pan").set({threshold:5,direction:i.Hammer.DIRECTION_ALL}),de(this.hammer,t=>{this.body.eventListeners.onTouch(t)}),this.hammer.on("tap",t=>{this.body.eventListeners.onTap(t)}),this.hammer.on("doubletap",t=>{this.body.eventListeners.onDoubleTap(t)}),this.hammer.on("press",t=>{this.body.eventListeners.onHold(t)}),this.hammer.on("panstart",t=>{this.body.eventListeners.onDragStart(t)}),this.hammer.on("panmove",t=>{this.body.eventListeners.onDrag(t)}),this.hammer.on("panend",t=>{this.body.eventListeners.onDragEnd(t)}),this.hammer.on("pinch",t=>{this.body.eventListeners.onPinch(t)}),this.frame.canvas.addEventListener("wheel",t=>{this.body.eventListeners.onMouseWheel(t)}),this.frame.canvas.addEventListener("mousemove",t=>{this.body.eventListeners.onMouseMove(t)}),this.frame.canvas.addEventListener("contextmenu",t=>{this.body.eventListeners.onContext(t)}),this.hammerFrame=new i.Hammer(this.frame),ae(this.hammerFrame,t=>{this.body.eventListeners.onRelease(t)})}setSize(t=this.options.width,e=this.options.height){t=this._prepareValue(t),e=this._prepareValue(e);let i=!1;const o=this.frame.canvas.width,s=this.frame.canvas.height,n=this.pixelRatio;if(this._setPixelRatio(),t!=this.options.width||e!=this.options.height||this.frame.style.width!=t||this.frame.style.height!=e)this._getCameraState(n),this.frame.style.width=t,this.frame.style.height=e,this.frame.canvas.style.width="100%",this.frame.canvas.style.height="100%",this.frame.canvas.width=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),this.frame.canvas.height=Math.round(this.frame.canvas.clientHeight*this.pixelRatio),this.options.width=t,this.options.height=e,this.canvasViewCenter={x:.5*this.frame.clientWidth,y:.5*this.frame.clientHeight},i=!0;else{const t=Math.round(this.frame.canvas.clientWidth*this.pixelRatio),e=Math.round(this.frame.canvas.clientHeight*this.pixelRatio);this.frame.canvas.width===t&&this.frame.canvas.height===e||this._getCameraState(n),this.frame.canvas.width!==t&&(this.frame.canvas.width=t,i=!0),this.frame.canvas.height!==e&&(this.frame.canvas.height=e,i=!0)}return!0===i&&(this.body.emitter.emit("resize",{width:Math.round(this.frame.canvas.width/this.pixelRatio),height:Math.round(this.frame.canvas.height/this.pixelRatio),oldWidth:Math.round(o/this.pixelRatio),oldHeight:Math.round(s/this.pixelRatio)}),this._setCameraState()),this.initialized=!0,i}getContext(){return this.frame.canvas.getContext("2d")}_determinePixelRatio(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");let e=1;"undefined"!=typeof window&&(e=window.devicePixelRatio||1);return e/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)}_setPixelRatio(){this.pixelRatio=this._determinePixelRatio()}setTransform(){const t=this.getContext();if(void 0===t)throw new Error("Could not get canvax context");t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}_XconvertDOMtoCanvas(t){return(t-this.body.view.translation.x)/this.body.view.scale}_XconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.x}_YconvertDOMtoCanvas(t){return(t-this.body.view.translation.y)/this.body.view.scale}_YconvertCanvasToDOM(t){return t*this.body.view.scale+this.body.view.translation.y}canvasToDOM(t){return{x:this._XconvertCanvasToDOM(t.x),y:this._YconvertCanvasToDOM(t.y)}}DOMtoCanvas(t){return{x:this._XconvertDOMtoCanvas(t.x),y:this._YconvertDOMtoCanvas(t.y)}}}class le{constructor(t,e){this.body=t,this.canvas=e,this.animationSpeed=1/this.renderRefreshRate,this.animationEasingFunction="easeInOutQuint",this.easingTime=0,this.sourceScale=0,this.targetScale=0,this.sourceTranslation=0,this.targetTranslation=0,this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0,this.touchTime=0,this.viewFunction=void 0,this.body.emitter.on("fit",this.fit.bind(this)),this.body.emitter.on("animationFinished",()=>{this.body.emitter.emit("_stopRendering")}),this.body.emitter.on("unlockNode",this.releaseNode.bind(this))}setOptions(t={}){this.options=t}fit(t,e=!1){t=function(t,e){const i=Object.assign({nodes:e,minZoomLevel:Number.MIN_VALUE,maxZoomLevel:1},t??{});if(!Array.isArray(i.nodes))throw new TypeError("Nodes has to be an array of ids.");if(0===i.nodes.length&&(i.nodes=e),!("number"==typeof i.minZoomLevel&&i.minZoomLevel>0))throw new TypeError("Min zoom level has to be a number higher than zero.");if(!("number"==typeof i.maxZoomLevel&&i.minZoomLevel<=i.maxZoomLevel))throw new TypeError("Max zoom level has to be a number higher than min zoom level.");return i}(t,this.body.nodeIndices);const i=this.canvas.frame.canvas.clientWidth,o=this.canvas.frame.canvas.clientHeight;let s,n;if(0===i||0===o)n=1,s=oe.getRange(this.body.nodes,t.nodes);else if(!0===e){let e=0;for(const t in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,t)){!0===this.body.nodes[t].predefinedPosition&&(e+=1)}if(e>.5*this.body.nodeIndices.length)return void this.fit(t,!1);s=oe.getRange(this.body.nodes,t.nodes);n=12.662/(this.body.nodeIndices.length+7.4147)+.0964822;n*=Math.min(i/600,o/600)}else{this.body.emitter.emit("_resizeNodes"),s=oe.getRange(this.body.nodes,t.nodes);const e=i/(1.1*Math.abs(s.maxX-s.minX)),r=o/(1.1*Math.abs(s.maxY-s.minY));n=e<=r?e:r}n>t.maxZoomLevel?n=t.maxZoomLevel:n<t.minZoomLevel&&(n=t.minZoomLevel);const r={position:oe.findCenter(s),scale:n,animation:t.animation};this.moveTo(r)}focus(t,e={}){if(void 0!==this.body.nodes[t]){const i={x:this.body.nodes[t].x,y:this.body.nodes[t].y};e.position=i,e.lockedOnNode=t,this.moveTo(e)}else console.error("Node: "+t+" cannot be found.")}moveTo(t){if(void 0!==t){if(null!=t.offset){if(null!=t.offset.x){if(t.offset.x=+t.offset.x,!Number.isFinite(t.offset.x))throw new TypeError('The option "offset.x" has to be a finite number.')}else t.offset.x=0;if(null!=t.offset.y){if(t.offset.y=+t.offset.y,!Number.isFinite(t.offset.y))throw new TypeError('The option "offset.y" has to be a finite number.')}else t.offset.x=0}else t.offset={x:0,y:0};if(null!=t.position){if(null!=t.position.x){if(t.position.x=+t.position.x,!Number.isFinite(t.position.x))throw new TypeError('The option "position.x" has to be a finite number.')}else t.position.x=0;if(null!=t.position.y){if(t.position.y=+t.position.y,!Number.isFinite(t.position.y))throw new TypeError('The option "position.y" has to be a finite number.')}else t.position.x=0}else t.position=this.getViewPosition();if(null!=t.scale){if(t.scale=+t.scale,!(t.scale>0))throw new TypeError('The option "scale" has to be a number greater than zero.')}else t.scale=this.body.view.scale;void 0===t.animation&&(t.animation={duration:0}),!1===t.animation&&(t.animation={duration:0}),!0===t.animation&&(t.animation={}),void 0===t.animation.duration&&(t.animation.duration=1e3),void 0===t.animation.easingFunction&&(t.animation.easingFunction="easeInOutQuad"),this.animateView(t)}else t={}}animateView(t){if(void 0===t)return;this.animationEasingFunction=t.animation.easingFunction,this.releaseNode(),!0===t.locked&&(this.lockedOnNodeId=t.lockedOnNode,this.lockedOnNodeOffset=t.offset),0!=this.easingTime&&this._transitionRedraw(!0),this.sourceScale=this.body.view.scale,this.sourceTranslation=this.body.view.translation,this.targetScale=t.scale,this.body.view.scale=this.targetScale;const e=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),i=e.x-t.position.x,o=e.y-t.position.y;this.targetTranslation={x:this.sourceTranslation.x+i*this.targetScale+t.offset.x,y:this.sourceTranslation.y+o*this.targetScale+t.offset.y},0===t.animation.duration?null!=this.lockedOnNodeId?(this.viewFunction=this._lockedRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction)):(this.body.view.scale=this.targetScale,this.body.view.translation=this.targetTranslation,this.body.emitter.emit("_requestRedraw")):(this.animationSpeed=1/(60*t.animation.duration*.001)||1/60,this.animationEasingFunction=t.animation.easingFunction,this.viewFunction=this._transitionRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction),this.body.emitter.emit("_startRendering"))}_lockedRedraw(){const t=this.body.nodes[this.lockedOnNodeId].x,e=this.body.nodes[this.lockedOnNodeId].y,i=this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight}),o=i.x-t,s=i.y-e,n=this.body.view.translation,r={x:n.x+o*this.body.view.scale+this.lockedOnNodeOffset.x,y:n.y+s*this.body.view.scale+this.lockedOnNodeOffset.y};this.body.view.translation=r}releaseNode(){void 0!==this.lockedOnNodeId&&void 0!==this.viewFunction&&(this.body.emitter.off("initRedraw",this.viewFunction),this.lockedOnNodeId=void 0,this.lockedOnNodeOffset=void 0)}_transitionRedraw(t=!1){this.easingTime+=this.animationSpeed,this.easingTime=!0===t?1:this.easingTime;const e=i.easingFunctions[this.animationEasingFunction](this.easingTime);this.body.view.scale=this.sourceScale+(this.targetScale-this.sourceScale)*e,this.body.view.translation={x:this.sourceTranslation.x+(this.targetTranslation.x-this.sourceTranslation.x)*e,y:this.sourceTranslation.y+(this.targetTranslation.y-this.sourceTranslation.y)*e},this.easingTime>=1&&(this.body.emitter.off("initRedraw",this.viewFunction),this.easingTime=0,null!=this.lockedOnNodeId&&(this.viewFunction=this._lockedRedraw.bind(this),this.body.emitter.on("initRedraw",this.viewFunction)),this.body.emitter.emit("animationFinished"))}getScale(){return this.body.view.scale}getViewPosition(){return this.canvas.DOMtoCanvas({x:.5*this.canvas.frame.canvas.clientWidth,y:.5*this.canvas.frame.canvas.clientHeight})}}class ce{constructor(t,e){this.body=t,this.canvas=e,this.iconsCreated=!1,this.navigationHammers=[],this.boundFunctions={},this.touchTime=0,this.activated=!1,this.body.emitter.on("activate",()=>{this.activated=!0,this.configureKeyboardBindings()}),this.body.emitter.on("deactivate",()=>{this.activated=!1,this.configureKeyboardBindings()}),this.body.emitter.on("destroy",()=>{void 0!==this.keycharm&&this.keycharm.destroy()}),this.options={}}setOptions(t){void 0!==t&&(this.options=t,this.create())}create(){!0===this.options.navigationButtons?!1===this.iconsCreated&&this.loadNavigationElements():!0===this.iconsCreated&&this.cleanNavigation(),this.configureKeyboardBindings()}cleanNavigation(){if(0!=this.navigationHammers.length){for(let t=0;t<this.navigationHammers.length;t++)this.navigationHammers[t].destroy();this.navigationHammers=[]}this.navigationDOM&&this.navigationDOM.wrapper&&this.navigationDOM.wrapper.parentNode&&this.navigationDOM.wrapper.parentNode.removeChild(this.navigationDOM.wrapper),this.iconsCreated=!1}loadNavigationElements(){this.cleanNavigation(),this.navigationDOM={};const t=["up","down","left","right","zoomIn","zoomOut","zoomExtends"],e=["_moveUp","_moveDown","_moveLeft","_moveRight","_zoomIn","_zoomOut","_fit"];this.navigationDOM.wrapper=document.createElement("div"),this.navigationDOM.wrapper.className="vis-navigation",this.canvas.frame.appendChild(this.navigationDOM.wrapper);for(let o=0;o<t.length;o++){this.navigationDOM[t[o]]=document.createElement("div"),this.navigationDOM[t[o]].className="vis-button vis-"+t[o],this.navigationDOM.wrapper.appendChild(this.navigationDOM[t[o]]);const s=new i.Hammer(this.navigationDOM[t[o]]);de(s,"_fit"===e[o]?this._fit.bind(this):this.bindToRedraw.bind(this,e[o])),this.navigationHammers.push(s)}const o=new i.Hammer(this.canvas.frame);ae(o,()=>{this._stopMovement()}),this.navigationHammers.push(o),this.iconsCreated=!0}bindToRedraw(t){void 0===this.boundFunctions[t]&&(this.boundFunctions[t]=this[t].bind(this),this.body.emitter.on("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_startRendering"))}unbindFromRedraw(t){void 0!==this.boundFunctions[t]&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"),delete this.boundFunctions[t])}_fit(){(new Date).valueOf()-this.touchTime>700&&(this.body.emitter.emit("fit",{duration:700}),this.touchTime=(new Date).valueOf())}_stopMovement(){for(const t in this.boundFunctions)Object.prototype.hasOwnProperty.call(this.boundFunctions,t)&&(this.body.emitter.off("initRedraw",this.boundFunctions[t]),this.body.emitter.emit("_stopRendering"));this.boundFunctions={}}_moveUp(){this.body.view.translation.y+=this.options.keyboard.speed.y}_moveDown(){this.body.view.translation.y-=this.options.keyboard.speed.y}_moveLeft(){this.body.view.translation.x+=this.options.keyboard.speed.x}_moveRight(){this.body.view.translation.x-=this.options.keyboard.speed.x}_zoomIn(){const t=this.body.view.scale,e=this.body.view.scale*(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,s=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,n=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:s,y:n},this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:null})}_zoomOut(){const t=this.body.view.scale,e=this.body.view.scale/(1+this.options.keyboard.speed.zoom),i=this.body.view.translation,o=e/t,s=(1-o)*this.canvas.canvasViewCenter.x+i.x*o,n=(1-o)*this.canvas.canvasViewCenter.y+i.y*o;this.body.view.scale=e,this.body.view.translation={x:s,y:n},this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:null})}configureKeyboardBindings(){void 0!==this.keycharm&&this.keycharm.destroy(),!0===this.options.keyboard.enabled&&(!0===this.options.keyboard.bindToWindow?this.keycharm=n({container:window,preventDefault:!0}):this.keycharm=n({container:this.canvas.frame,preventDefault:!0}),this.keycharm.reset(),!0===this.activated&&(this.keycharm.bind("up",()=>{this.bindToRedraw("_moveUp")},"keydown"),this.keycharm.bind("down",()=>{this.bindToRedraw("_moveDown")},"keydown"),this.keycharm.bind("left",()=>{this.bindToRedraw("_moveLeft")},"keydown"),this.keycharm.bind("right",()=>{this.bindToRedraw("_moveRight")},"keydown"),this.keycharm.bind("=",()=>{this.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("num+",()=>{this.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("num-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("-",()=>{this.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("[",()=>{this.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("]",()=>{this.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("pageup",()=>{this.bindToRedraw("_zoomIn")},"keydown"),this.keycharm.bind("pagedown",()=>{this.bindToRedraw("_zoomOut")},"keydown"),this.keycharm.bind("up",()=>{this.unbindFromRedraw("_moveUp")},"keyup"),this.keycharm.bind("down",()=>{this.unbindFromRedraw("_moveDown")},"keyup"),this.keycharm.bind("left",()=>{this.unbindFromRedraw("_moveLeft")},"keyup"),this.keycharm.bind("right",()=>{this.unbindFromRedraw("_moveRight")},"keyup"),this.keycharm.bind("=",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("num+",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("num-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("-",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("[",()=>{this.unbindFromRedraw("_zoomOut")},"keyup"),this.keycharm.bind("]",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("pageup",()=>{this.unbindFromRedraw("_zoomIn")},"keyup"),this.keycharm.bind("pagedown",()=>{this.unbindFromRedraw("_zoomOut")},"keyup")))}}class pe{constructor(t,e,i){this.body=t,this.canvas=e,this.selectionHandler=i,this.navigationHandler=new ce(t,e),this.body.eventListeners.onTap=this.onTap.bind(this),this.body.eventListeners.onTouch=this.onTouch.bind(this),this.body.eventListeners.onDoubleTap=this.onDoubleTap.bind(this),this.body.eventListeners.onHold=this.onHold.bind(this),this.body.eventListeners.onDragStart=this.onDragStart.bind(this),this.body.eventListeners.onDrag=this.onDrag.bind(this),this.body.eventListeners.onDragEnd=this.onDragEnd.bind(this),this.body.eventListeners.onMouseWheel=this.onMouseWheel.bind(this),this.body.eventListeners.onPinch=this.onPinch.bind(this),this.body.eventListeners.onMouseMove=this.onMouseMove.bind(this),this.body.eventListeners.onRelease=this.onRelease.bind(this),this.body.eventListeners.onContext=this.onContext.bind(this),this.touchTime=0,this.drag={},this.pinch={},this.popup=void 0,this.popupObj=void 0,this.popupTimer=void 0,this.body.functions.getPointer=this.getPointer.bind(this),this.options={},this.defaultOptions={dragNodes:!0,dragView:!0,hover:!1,keyboard:{enabled:!1,speed:{x:10,y:10,zoom:.02},bindToWindow:!0,autoFocus:!0},navigationButtons:!1,tooltipDelay:300,zoomView:!0,zoomSpeed:1},Object.assign(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("destroy",()=>{clearTimeout(this.popupTimer),delete this.body.functions.getPointer})}setOptions(t){if(void 0!==t){const e=["hideEdgesOnDrag","hideEdgesOnZoom","hideNodesOnDrag","keyboard","multiselect","selectable","selectConnectedEdges"];i.selectiveNotDeepExtend(e,this.options,t),i.mergeOptions(this.options,t,"keyboard"),t.tooltip&&(Object.assign(this.options.tooltip,t.tooltip),t.tooltip.color&&(this.options.tooltip.color=i.parseColor(t.tooltip.color)))}this.navigationHandler.setOptions(this.options)}getPointer(t){return{x:t.x-i.getAbsoluteLeft(this.canvas.frame.canvas),y:t.y-i.getAbsoluteTop(this.canvas.frame.canvas)}}onTouch(t){(new Date).valueOf()-this.touchTime>50&&(this.drag.pointer=this.getPointer(t.center),this.drag.pinched=!1,this.pinch.scale=this.body.view.scale,this.touchTime=(new Date).valueOf())}onTap(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect&&(t.changedPointers[0].ctrlKey||t.changedPointers[0].metaKey);this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e)}onDoubleTap(t){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("doubleClick",t,e)}onHold(t){const e=this.getPointer(t.center),i=this.selectionHandler.options.multiselect;this.checkSelectionChanges(e,i),this.selectionHandler.commitAndEmit(e,t),this.selectionHandler.generateClickEvent("click",t,e),this.selectionHandler.generateClickEvent("hold",t,e)}onRelease(t){if((new Date).valueOf()-this.touchTime>10){const e=this.getPointer(t.center);this.selectionHandler.generateClickEvent("release",t,e),this.touchTime=(new Date).valueOf()}}onContext(t){const e=this.getPointer({x:t.clientX,y:t.clientY});this.selectionHandler.generateClickEvent("oncontext",t,e)}checkSelectionChanges(t,e=!1){!0===e?this.selectionHandler.selectAdditionalOnPoint(t):this.selectionHandler.selectOnPoint(t)}_determineDifference(t,e){const i=function(t,e){const i=[];for(let o=0;o<t.length;o++){const s=t[o];-1===e.indexOf(s)&&i.push(s)}return i};return{nodes:i(t.nodes,e.nodes),edges:i(t.edges,e.edges)}}onDragStart(t){if(this.drag.dragging)return;void 0===this.drag.pointer&&this.onTouch(t);const e=this.selectionHandler.getNodeAt(this.drag.pointer);if(this.drag.dragging=!0,this.drag.selection=[],this.drag.translation=Object.assign({},this.body.view.translation),this.drag.nodeId=void 0,t.srcEvent.shiftKey){this.body.selectionBox.show=!0;const e=this.getPointer(t.center);this.body.selectionBox.position.start={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)}}else if(void 0!==e&&!0===this.options.dragNodes){this.drag.nodeId=e.id,!1===e.isSelected()&&this.selectionHandler.setSelection({nodes:[e.id]}),this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer);for(const t of this.selectionHandler.getSelectedNodes()){const e={id:t.id,node:t,x:t.x,y:t.y,xFixed:t.options.fixed.x,yFixed:t.options.fixed.y};t.options.fixed.x=!0,t.options.fixed.y=!0,this.drag.selection.push(e)}}else this.selectionHandler.generateClickEvent("dragStart",t,this.drag.pointer,void 0,!0)}onDrag(t){if(!0===this.drag.pinched)return;this.body.emitter.emit("unlockNode");const e=this.getPointer(t.center),i=this.drag.selection;if(i&&i.length&&!0===this.options.dragNodes){this.selectionHandler.generateClickEvent("dragging",t,e);const o=e.x-this.drag.pointer.x,s=e.y-this.drag.pointer.y;i.forEach(t=>{const e=t.node;!1===t.xFixed&&(e.x=this.canvas._XconvertDOMtoCanvas(this.canvas._XconvertCanvasToDOM(t.x)+o)),!1===t.yFixed&&(e.y=this.canvas._YconvertDOMtoCanvas(this.canvas._YconvertCanvasToDOM(t.y)+s))}),this.body.emitter.emit("startSimulation")}else{if(t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);this.body.selectionBox.position.end={x:this.canvas._XconvertDOMtoCanvas(e.x),y:this.canvas._YconvertDOMtoCanvas(e.y)},this.body.emitter.emit("_requestRedraw")}if(!0===this.options.dragView&&!t.srcEvent.shiftKey){if(this.selectionHandler.generateClickEvent("dragging",t,e,void 0,!0),void 0===this.drag.pointer)return void this.onDragStart(t);const i=e.x-this.drag.pointer.x,o=e.y-this.drag.pointer.y;this.body.view.translation={x:this.drag.translation.x+i,y:this.drag.translation.y+o},this.body.emitter.emit("_requestRedraw")}}}onDragEnd(t){if(this.drag.dragging=!1,this.body.selectionBox.show){this.body.selectionBox.show=!1;const e=this.body.selectionBox.position,i={minX:Math.min(e.start.x,e.end.x),minY:Math.min(e.start.y,e.end.y),maxX:Math.max(e.start.x,e.end.x),maxY:Math.max(e.start.y,e.end.y)};this.body.nodeIndices.filter(t=>{const e=this.body.nodes[t];return e.x>=i.minX&&e.x<=i.maxX&&e.y>=i.minY&&e.y<=i.maxY}).forEach(t=>this.selectionHandler.selectObject(this.body.nodes[t]));const o=this.getPointer(t.center);this.selectionHandler.commitAndEmit(o,t),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw")}else{const e=this.drag.selection;e&&e.length?(e.forEach(function(t){t.node.options.fixed.x=t.xFixed,t.node.options.fixed.y=t.yFixed}),this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center)),this.body.emitter.emit("startSimulation")):(this.selectionHandler.generateClickEvent("dragEnd",t,this.getPointer(t.center),void 0,!0),this.body.emitter.emit("_requestRedraw"))}}onPinch(t){const e=this.getPointer(t.center);this.drag.pinched=!0,void 0===this.pinch.scale&&(this.pinch.scale=1);const i=this.pinch.scale*t.scale;this.zoom(i,e)}zoom(t,e){if(!0===this.options.zoomView){const i=this.body.view.scale;let o;t<1e-5&&(t=1e-5),t>10&&(t=10),void 0!==this.drag&&!0===this.drag.dragging&&(o=this.canvas.DOMtoCanvas(this.drag.pointer));const s=this.body.view.translation,n=t/i,r=(1-n)*e.x+s.x*n,d=(1-n)*e.y+s.y*n;if(this.body.view.scale=t,this.body.view.translation={x:r,y:d},null!=o){const t=this.canvas.canvasToDOM(o);this.drag.pointer.x=t.x,this.drag.pointer.y=t.y}this.body.emitter.emit("_requestRedraw"),i<t?this.body.emitter.emit("zoom",{direction:"+",scale:this.body.view.scale,pointer:e}):this.body.emitter.emit("zoom",{direction:"-",scale:this.body.view.scale,pointer:e})}}onMouseWheel(t){if(!0===this.options.zoomView){if(0!==t.deltaY){let e=this.body.view.scale;e*=1+(t.deltaY<0?1:-1)*(.1*this.options.zoomSpeed);const i=this.getPointer({x:t.clientX,y:t.clientY});this.zoom(e,i)}t.preventDefault()}}onMouseMove(t){const e=this.getPointer({x:t.clientX,y:t.clientY});let i=!1;void 0!==this.popup&&(!1===this.popup.hidden&&this._checkHidePopup(e),!1===this.popup.hidden&&(i=!0,this.popup.setPosition(e.x+3,e.y-5),this.popup.show())),this.options.keyboard.autoFocus&&!1===this.options.keyboard.bindToWindow&&!0===this.options.keyboard.enabled&&this.canvas.frame.focus(),!1===i&&(void 0!==this.popupTimer&&(clearInterval(this.popupTimer),this.popupTimer=void 0),this.drag.dragging||(this.popupTimer=setTimeout(()=>this._checkShowPopup(e),this.options.tooltipDelay))),!0===this.options.hover&&this.selectionHandler.hoverObject(t,e)}_checkShowPopup(t){const e=this.canvas._XconvertDOMtoCanvas(t.x),o=this.canvas._YconvertDOMtoCanvas(t.y),s={left:e,top:o,right:e,bottom:o},n=void 0===this.popupObj?void 0:this.popupObj.id;let r=!1,d="node";if(void 0===this.popupObj){const t=this.body.nodeIndices,e=this.body.nodes;let i;const o=[];for(let n=0;n<t.length;n++)i=e[t[n]],!0===i.isOverlappingWith(s)&&(r=!0,void 0!==i.getTitle()&&o.push(t[n]));o.length>0&&(this.popupObj=e[o[o.length-1]],r=!0)}if(void 0===this.popupObj&&!1===r){const t=this.body.edgeIndices,e=this.body.edges;let i;const o=[];for(let n=0;n<t.length;n++)i=e[t[n]],!0===i.isOverlappingWith(s)&&!0===i.connected&&void 0!==i.getTitle()&&o.push(t[n]);o.length>0&&(this.popupObj=e[o[o.length-1]],d="edge")}void 0!==this.popupObj?this.popupObj.id!==n&&(void 0===this.popup&&(this.popup=new i.Popup(this.canvas.frame)),this.popup.popupTargetType=d,this.popup.popupTargetId=this.popupObj.id,this.popup.setPosition(t.x+3,t.y-5),this.popup.setText(this.popupObj.getTitle()),this.popup.show(),this.body.emitter.emit("showPopup",this.popupObj.id)):void 0!==this.popup&&(this.popup.hide(),this.body.emitter.emit("hidePopup"))}_checkHidePopup(t){const e=this.selectionHandler._pointerToPositionObject(t);let i=!1;if("node"===this.popup.popupTargetType){if(void 0!==this.body.nodes[this.popup.popupTargetId]&&(i=this.body.nodes[this.popup.popupTargetId].isOverlappingWith(e),!0===i)){const e=this.selectionHandler.getNodeAt(t);i=void 0!==e&&e.id===this.popup.popupTargetId}}else void 0===this.selectionHandler.getNodeAt(t)&&void 0!==this.body.edges[this.popup.popupTargetId]&&(i=this.body.edges[this.popup.popupTargetId].isOverlappingWith(e));!1===i&&(this.popupObj=void 0,this.popup.hide(),this.body.emitter.emit("hidePopup"))}}function ue(t,e){const i=new Set;for(const o of e)t.has(o)||i.add(o);return i}class ge{#t=new Set;#e=new Set;get size(){return this.#e.size}add(...t){for(const e of t)this.#e.add(e)}delete(...t){for(const e of t)this.#e.delete(e)}clear(){this.#e.clear()}getSelection(){return[...this.#e]}getChanges(){return{added:[...ue(this.#t,this.#e)],deleted:[...ue(this.#e,this.#t)],previous:[...new Set(this.#t)],current:[...new Set(this.#e)]}}commit(){const t=this.getChanges();this.#t=this.#e,this.#e=new Set(this.#t);for(const e of t.added)e.select();for(const e of t.deleted)e.unselect();return t}}class be{#i=new ge;#o=new ge;#s;constructor(t=()=>{}){this.#s=t}get sizeNodes(){return this.#i.size}get sizeEdges(){return this.#o.size}getNodes(){return this.#i.getSelection()}getEdges(){return this.#o.getSelection()}addNodes(...t){this.#i.add(...t)}addEdges(...t){this.#o.add(...t)}deleteNodes(t){this.#i.delete(t)}deleteEdges(t){this.#o.delete(t)}clear(){this.#i.clear(),this.#o.clear()}commit(...t){const e={nodes:this.#i.commit(),edges:this.#o.commit()};return this.#s(e,...t),e}}class me{constructor(t,e){this.body=t,this.canvas=e,this._selectionAccumulator=new be,this.hoverObj={nodes:{},edges:{}},this.options={},this.defaultOptions={multiselect:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0},Object.assign(this.options,this.defaultOptions),this.body.emitter.on("_dataChanged",()=>{this.updateSelection()})}setOptions(t){if(void 0!==t){const e=["multiselect","hoverConnectedEdges","selectable","selectConnectedEdges"];i.selectiveDeepExtend(e,this.options,t)}}selectOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);this.unselectAll(),void 0!==i&&(e=this.selectObject(i)),this.body.emitter.emit("_requestRedraw")}return e}selectAdditionalOnPoint(t){let e=!1;if(!0===this.options.selectable){const i=this.getNodeAt(t)||this.getEdgeAt(t);void 0!==i&&(e=!0,!0===i.isSelected()?this.deselectObject(i):this.selectObject(i),this.body.emitter.emit("_requestRedraw"))}return e}_initBaseEvent(t,e){const i={};return i.pointer={DOM:{x:e.x,y:e.y},canvas:this.canvas.DOMtoCanvas(e)},i.event=t,i}generateClickEvent(t,e,i,o,s=!1){const n=this._initBaseEvent(e,i);if(!0===s)n.nodes=[],n.edges=[];else{const t=this.getSelection();n.nodes=t.nodes,n.edges=t.edges}void 0!==o&&(n.previousSelection=o),"click"==t&&(n.items=this.getClickedItems(i)),void 0!==e.controlEdge&&(n.controlEdge=e.controlEdge),this.body.emitter.emit(t,n)}selectObject(t,e=this.options.selectConnectedEdges){return void 0!==t&&(t instanceof Et?(!0===e&&this._selectionAccumulator.addEdges(...t.edges),this._selectionAccumulator.addNodes(t)):this._selectionAccumulator.addEdges(t),!0)}deselectObject(t){!0===t.isSelected()&&(t.selected=!1,this._removeFromSelection(t))}_getAllNodesOverlappingWith(t){const e=[],i=this.body.nodes;for(let o=0;o<this.body.nodeIndices.length;o++){const s=this.body.nodeIndices[o];i[s].isOverlappingWith(t)&&e.push(s)}return e}_pointerToPositionObject(t){const e=this.canvas.DOMtoCanvas(t);return{left:e.x-1,top:e.y+1,right:e.x+1,bottom:e.y-1}}getNodeAt(t,e=!0){const i=this._pointerToPositionObject(t),o=this._getAllNodesOverlappingWith(i);return o.length>0?!0===e?this.body.nodes[o[o.length-1]]:o[o.length-1]:void 0}_getEdgesOverlappingWith(t,e){const i=this.body.edges;for(let o=0;o<this.body.edgeIndices.length;o++){const s=this.body.edgeIndices[o];i[s].isOverlappingWith(t)&&e.push(s)}}_getAllEdgesOverlappingWith(t){const e=[];return this._getEdgesOverlappingWith(t,e),e}getEdgeAt(t,e=!0){const i=this.canvas.DOMtoCanvas(t);let o=10,s=null;const n=this.body.edges;for(let t=0;t<this.body.edgeIndices.length;t++){const e=this.body.edgeIndices[t],r=n[e];if(r.connected){const t=r.from.x,n=r.from.y,d=r.to.x,a=r.to.y,h=r.edgeType.getDistanceToEdge(t,n,d,a,i.x,i.y);h<o&&(s=e,o=h)}}return null!==s?!0===e?this.body.edges[s]:s:void 0}_addToHover(t){t instanceof Et?this.hoverObj.nodes[t.id]=t:this.hoverObj.edges[t.id]=t}_removeFromSelection(t){t instanceof Et?(this._selectionAccumulator.deleteNodes(t),this._selectionAccumulator.deleteEdges(...t.edges)):this._selectionAccumulator.deleteEdges(t)}unselectAll(){this._selectionAccumulator.clear()}getSelectedNodeCount(){return this._selectionAccumulator.sizeNodes}getSelectedEdgeCount(){return this._selectionAccumulator.sizeEdges}_hoverConnectedEdges(t){for(let e=0;e<t.edges.length;e++){const i=t.edges[e];i.hover=!0,this._addToHover(i)}}emitBlurEvent(t,e,i){const o=this._initBaseEvent(t,e);!0===i.hover&&(i.hover=!1,i instanceof Et?(o.node=i.id,this.body.emitter.emit("blurNode",o)):(o.edge=i.id,this.body.emitter.emit("blurEdge",o)))}emitHoverEvent(t,e,i){const o=this._initBaseEvent(t,e);let s=!1;return!1===i.hover&&(i.hover=!0,this._addToHover(i),s=!0,i instanceof Et?(o.node=i.id,this.body.emitter.emit("hoverNode",o)):(o.edge=i.id,this.body.emitter.emit("hoverEdge",o))),s}hoverObject(t,e){let i=this.getNodeAt(e);void 0===i&&(i=this.getEdgeAt(e));let o=!1;for(const s in this.hoverObj.nodes)Object.prototype.hasOwnProperty.call(this.hoverObj.nodes,s)&&(void 0===i||i instanceof Et&&i.id!=s||i instanceof Yt)&&(this.emitBlurEvent(t,e,this.hoverObj.nodes[s]),delete this.hoverObj.nodes[s],o=!0);for(const s in this.hoverObj.edges)Object.prototype.hasOwnProperty.call(this.hoverObj.edges,s)&&(!0===o?(this.hoverObj.edges[s].hover=!1,delete this.hoverObj.edges[s]):(void 0===i||i instanceof Yt&&i.id!=s||i instanceof Et&&!i.hover)&&(this.emitBlurEvent(t,e,this.hoverObj.edges[s]),delete this.hoverObj.edges[s],o=!0));if(void 0!==i){const s=Object.keys(this.hoverObj.edges).length,n=Object.keys(this.hoverObj.nodes).length;(o||i instanceof Yt&&0===s&&0===n||i instanceof Et&&0===s&&0===n)&&(o=this.emitHoverEvent(t,e,i)),i instanceof Et&&!0===this.options.hoverConnectedEdges&&this._hoverConnectedEdges(i)}!0===o&&this.body.emitter.emit("_requestRedraw")}commitWithoutEmitting(){this._selectionAccumulator.commit()}commitAndEmit(t,e){let i=!1;const o=this._selectionAccumulator.commit(),s={nodes:o.nodes.previous,edges:o.edges.previous};o.edges.deleted.length>0&&(this.generateClickEvent("deselectEdge",e,t,s),i=!0),o.nodes.deleted.length>0&&(this.generateClickEvent("deselectNode",e,t,s),i=!0),o.nodes.added.length>0&&(this.generateClickEvent("selectNode",e,t),i=!0),o.edges.added.length>0&&(this.generateClickEvent("selectEdge",e,t),i=!0),!0===i&&this.generateClickEvent("select",e,t)}getSelection(){return{nodes:this.getSelectedNodeIds(),edges:this.getSelectedEdgeIds()}}getSelectedNodes(){return this._selectionAccumulator.getNodes()}getSelectedEdges(){return this._selectionAccumulator.getEdges()}getSelectedNodeIds(){return this._selectionAccumulator.getNodes().map(t=>t.id)}getSelectedEdgeIds(){return this._selectionAccumulator.getEdges().map(t=>t.id)}setSelection(t,e={}){if(!t||!t.nodes&&!t.edges)throw new TypeError("Selection must be an object with nodes and/or edges properties");if((e.unselectAll||void 0===e.unselectAll)&&this.unselectAll(),t.nodes)for(const i of t.nodes){const t=this.body.nodes[i];if(!t)throw new RangeError('Node with id "'+i+'" not found');this.selectObject(t,e.highlightEdges)}if(t.edges)for(const e of t.edges){const t=this.body.edges[e];if(!t)throw new RangeError('Edge with id "'+e+'" not found');this.selectObject(t)}this.body.emitter.emit("_requestRedraw"),this._selectionAccumulator.commit()}selectNodes(t,e=!0){if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({nodes:t},{highlightEdges:e})}selectEdges(t){if(!t||void 0===t.length)throw"Selection must be an array with ids";this.setSelection({edges:t})}updateSelection(){for(const t in this._selectionAccumulator.getNodes())Object.prototype.hasOwnProperty.call(this.body.nodes,t.id)||this._selectionAccumulator.deleteNodes(t);for(const t in this._selectionAccumulator.getEdges())Object.prototype.hasOwnProperty.call(this.body.edges,t.id)||this._selectionAccumulator.deleteEdges(t)}getClickedItems(t){const e=this.canvas.DOMtoCanvas(t),i=[],o=this.body.nodeIndices,s=this.body.nodes;for(let t=o.length-1;t>=0;t--){const n=s[o[t]].getItemsOnPoint(e);i.push.apply(i,n)}const n=this.body.edgeIndices,r=this.body.edges;for(let t=n.length-1;t>=0;t--){const o=r[n[t]].getItemsOnPoint(e);i.push.apply(i,o)}return i}}class fe{abstract(){throw new Error("Can't instantiate abstract class!")}fake_use(){}curveType(){return this.abstract()}getPosition(t){return this.fake_use(t),this.abstract()}setPosition(t,e,i=void 0){this.fake_use(t,e,i),this.abstract()}getTreeSize(t){return this.fake_use(t),this.abstract()}sort(t){this.fake_use(t),this.abstract()}fix(t,e){this.fake_use(t,e),this.abstract()}shift(t,e){this.fake_use(t,e),this.abstract()}}class ye extends fe{constructor(t){super(),this.layout=t}curveType(){return"horizontal"}getPosition(t){return t.x}setPosition(t,e,i=void 0){void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.x=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_x,max:e.max_x}}sort(t){t.sort(function(t,e){return t.x-e.x})}fix(t,e){t.y=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.y=!0}shift(t,e){this.layout.body.nodes[t].x+=e}}class ve extends fe{constructor(t){super(),this.layout=t}curveType(){return"vertical"}getPosition(t){return t.y}setPosition(t,e,i=void 0){void 0!==i&&this.layout.hierarchical.addToOrdering(t,i),t.y=e}getTreeSize(t){const e=this.layout.hierarchical.getTreeSize(this.layout.body.nodes,t);return{min:e.min_y,max:e.max_y}}sort(t){t.sort(function(t,e){return t.y-e.y})}fix(t,e){t.x=this.layout.options.hierarchical.levelSeparation*e,t.options.fixed.x=!0}shift(t,e){this.layout.body.nodes[t].y+=e}}function we(t,e){const i=new Set;return t.forEach(t=>{t.edges.forEach(t=>{t.connected&&i.add(t)})}),i.forEach(t=>{const i=t.from.id,o=t.to.id;null==e[i]&&(e[i]=0),(null==e[o]||e[i]>=e[o])&&(e[o]=e[i]+1)}),e}function xe(t,e,i,o){const s=Object.create(null),n=[...o.values()].reduce((t,e)=>t+1+e.edges.length,0),r=i+"Id",d="to"===i?1:-1;for(const[a,h]of o){if(!o.has(a)||!t(h))continue;s[a]=0;const l=[h];let c,p=0;for(;c=l.pop();){if(!o.has(a))continue;const t=s[c.id]+d;if(c.edges.filter(t=>t.connected&&t.to!==t.from&&t[i]!==c&&o.has(t.toId)&&o.has(t.fromId)).forEach(o=>{const n=o[r],d=s[n];(null==d||e(t,d))&&(s[n]=t,l.push(o[i]))}),p>n)return we(o,s);++p}}return s}class _e{constructor(){this.childrenReference={},this.parentReference={},this.trees={},this.distributionOrdering={},this.levels={},this.distributionIndex={},this.isTree=!1,this.treeIndex=-1}addRelation(t,e){void 0===this.childrenReference[t]&&(this.childrenReference[t]=[]),this.childrenReference[t].push(e),void 0===this.parentReference[e]&&(this.parentReference[e]=[]),this.parentReference[e].push(t)}checkIfTree(){for(const t in this.parentReference)if(this.parentReference[t].length>1)return void(this.isTree=!1);this.isTree=!0}numTrees(){return this.treeIndex+1}setTreeIndex(t,e){void 0!==e&&void 0===this.trees[t.id]&&(this.trees[t.id]=e,this.treeIndex=Math.max(e,this.treeIndex))}ensureLevel(t){void 0===this.levels[t]&&(this.levels[t]=0)}getMaxLevel(t){const e={},i=t=>{if(void 0!==e[t])return e[t];let o=this.levels[t];if(this.childrenReference[t]){const e=this.childrenReference[t];if(e.length>0)for(let t=0;t<e.length;t++)o=Math.max(o,i(e[t]))}return e[t]=o,o};return i(t)}levelDownstream(t,e){void 0===this.levels[e.id]&&(void 0===this.levels[t.id]&&(this.levels[t.id]=0),this.levels[e.id]=this.levels[t.id]+1)}setMinLevelToZero(){const t=new Map;let e=0;const i=[...new Set(Object.values(this.levels))].sort((t,e)=>t-e);for(const o of i)t.set(o,e++);for(const e in this.levels)Object.prototype.hasOwnProperty.call(this.levels,e)&&(this.levels[e]=t.get(this.levels[e]))}getTreeSize(t,e){let i=1e9,o=-1e9,s=1e9,n=-1e9;for(const r in this.trees)if(Object.prototype.hasOwnProperty.call(this.trees,r)&&this.trees[r]===e){const e=t[r];i=Math.min(e.x,i),o=Math.max(e.x,o),s=Math.min(e.y,s),n=Math.max(e.y,n)}return{min_x:i,max_x:o,min_y:s,max_y:n}}hasSameParent(t,e){const i=this.parentReference[t.id],o=this.parentReference[e.id];if(void 0===i||void 0===o)return!1;for(let t=0;t<i.length;t++)for(let e=0;e<o.length;e++)if(i[t]==o[e])return!0;return!1}inSameSubNetwork(t,e){return this.trees[t.id]===this.trees[e.id]}getLevels(){return Object.keys(this.distributionOrdering)}addToOrdering(t,e){void 0===this.distributionOrdering[e]&&(this.distributionOrdering[e]=[]);let i=!1;const o=this.distributionOrdering[e];for(const e in o)if(o[e]===t){i=!0;break}i||(this.distributionOrdering[e].push(t),this.distributionIndex[t.id]=this.distributionOrdering[e].length-1)}}class Ee{constructor(t){this.body=t,this._resetRNG(Math.random()+":"+Date.now()),this.setPhysics=!1,this.options={},this.optionsBackup={physics:{}},this.defaultOptions={randomSeed:void 0,improvedLayout:!0,clusterThreshold:150,hierarchical:{enabled:!1,levelSeparation:150,nodeSpacing:100,treeSpacing:200,blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:"UD",sortMethod:"hubsize"}},Object.assign(this.options,this.defaultOptions),this.bindEventListeners()}bindEventListeners(){this.body.emitter.on("_dataChanged",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_dataLoaded",()=>{this.layoutNetwork()}),this.body.emitter.on("_resetHierarchicalLayout",()=>{this.setupHierarchicalLayout()}),this.body.emitter.on("_adjustEdgesForHierarchicalLayout",()=>{if(!0!==this.options.hierarchical.enabled)return;const t=this.direction.curveType();this.body.emitter.emit("_forceDisableDynamicCurves",t,!1)})}setOptions(t,e){if(void 0!==t){const o=this.options.hierarchical,s=o.enabled;if(i.selectiveDeepExtend(["randomSeed","improvedLayout","clusterThreshold"],this.options,t),i.mergeOptions(this.options,t,"hierarchical"),void 0!==t.randomSeed&&this._resetRNG(t.randomSeed),!0===o.enabled)return!0===s&&this.body.emitter.emit("refresh",!0),"RL"===o.direction||"DU"===o.direction?o.levelSeparation>0&&(o.levelSeparation*=-1):o.levelSeparation<0&&(o.levelSeparation*=-1),this.setDirectionStrategy(),this.body.emitter.emit("_resetHierarchicalLayout"),this.adaptAllOptionsForHierarchicalLayout(e);if(!0===s)return this.body.emitter.emit("refresh"),i.deepExtend(e,this.optionsBackup)}return e}_resetRNG(t){this.initialRandomSeed=t,this._rng=i.Alea(this.initialRandomSeed)}adaptAllOptionsForHierarchicalLayout(t){if(!0===this.options.hierarchical.enabled){const e=this.optionsBackup.physics;void 0===t.physics||!0===t.physics?(t.physics={enabled:void 0===e.enabled||e.enabled,solver:"hierarchicalRepulsion"},e.enabled=void 0===e.enabled||e.enabled,e.solver=e.solver||"barnesHut"):"object"==typeof t.physics?(e.enabled=void 0===t.physics.enabled||t.physics.enabled,e.solver=t.physics.solver||"barnesHut",t.physics.solver="hierarchicalRepulsion"):!1!==t.physics&&(e.solver="barnesHut",t.physics={solver:"hierarchicalRepulsion"});let i=this.direction.curveType();if(void 0===t.edges)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges={smooth:!1};else if(void 0===t.edges.smooth)this.optionsBackup.edges={smooth:{enabled:!0,type:"dynamic"}},t.edges.smooth=!1;else if("boolean"==typeof t.edges.smooth)this.optionsBackup.edges={smooth:t.edges.smooth},t.edges.smooth={enabled:t.edges.smooth,type:i};else{const e=t.edges.smooth;void 0!==e.type&&"dynamic"!==e.type&&(i=e.type),this.optionsBackup.edges={smooth:{enabled:void 0===e.enabled||e.enabled,type:void 0===e.type?"dynamic":e.type,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}},t.edges.smooth={enabled:void 0===e.enabled||e.enabled,type:i,roundness:void 0===e.roundness?.5:e.roundness,forceDirection:void 0!==e.forceDirection&&e.forceDirection}}this.body.emitter.emit("_forceDisableDynamicCurves",i)}return t}positionInitially(t){if(!0!==this.options.hierarchical.enabled){this._resetRNG(this.initialRandomSeed);const e=t.length+50;for(let i=0;i<t.length;i++){const o=t[i],s=2*Math.PI*this._rng();void 0===o.x&&(o.x=e*Math.cos(s)),void 0===o.y&&(o.y=e*Math.sin(s))}}}layoutNetwork(){if(!0!==this.options.hierarchical.enabled&&!0===this.options.improvedLayout){const t=this.body.nodeIndices;let e=0;for(let i=0;i<t.length;i++){!0===this.body.nodes[t[i]].predefinedPosition&&(e+=1)}if(e<.5*t.length){const e=10;let i=0;const o=this.options.clusterThreshold,s={clusterNodeProperties:{shape:"ellipse",label:"",group:"",font:{multi:!1}},clusterEdgeProperties:{label:"",font:{multi:!1},smooth:{enabled:!1}}};if(t.length>o){const n=t.length;for(;t.length>o&&i<=e;){i+=1;const e=t.length;i%3==0?this.body.modules.clustering.clusterBridges(s):this.body.modules.clustering.clusterOutliers(s);if(e==t.length&&i%3!=0)return this._declusterAll(),this.body.emitter.emit("_layoutFailed"),void console.info("This network could not be positioned by this version of the improved layout algorithm. Please disable improvedLayout for better performance.")}this.body.modules.kamadaKawai.setOptions({springLength:Math.max(150,2*n)})}i>e&&console.info("The clustering didn't succeed within the amount of interations allowed, progressing with partial result."),this.body.modules.kamadaKawai.solve(t,this.body.edgeIndices,!0),this._shiftToCenter();const n=70;for(let e=0;e<t.length;e++){const i=this.body.nodes[t[e]];!1===i.predefinedPosition&&(i.x+=(.5-this._rng())*n,i.y+=(.5-this._rng())*n)}this._declusterAll(),this.body.emitter.emit("_repositionBezierNodes")}}}_shiftToCenter(){const t=oe.getRangeCore(this.body.nodes,this.body.nodeIndices),e=oe.findCenter(t);for(let t=0;t<this.body.nodeIndices.length;t++){const i=this.body.nodes[this.body.nodeIndices[t]];i.x-=e.x,i.y-=e.y}}_declusterAll(){let t=!0;for(;!0===t;){t=!1;for(let e=0;e<this.body.nodeIndices.length;e++)!0===this.body.nodes[this.body.nodeIndices[e]].isCluster&&(t=!0,this.body.modules.clustering.openCluster(this.body.nodeIndices[e],{},!1));!0===t&&this.body.emitter.emit("_dataChanged")}}getSeed(){return this.initialRandomSeed}setupHierarchicalLayout(){if(!0===this.options.hierarchical.enabled&&this.body.nodeIndices.length>0){let t,e,i=!1,o=!1;for(e in this.lastNodeOnLevel={},this.hierarchical=new _e,this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,e)&&(t=this.body.nodes[e],void 0!==t.options.level?(i=!0,this.hierarchical.levels[e]=t.options.level):o=!0);if(!0===o&&!0===i)throw new Error("To use the hierarchical layout, nodes require either no predefined levels or levels have to be defined for all nodes.");{if(!0===o){const t=this.options.hierarchical.sortMethod;"hubsize"===t?this._determineLevelsByHubsize():"directed"===t?this._determineLevelsDirected():"custom"===t&&this._determineLevelsCustomCallback()}for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this.hierarchical.ensureLevel(t);const t=this._getDistribution();this._generateMap(),this._placeNodesByHierarchy(t),this._condenseHierarchy(),this._shiftToCenter()}}}_condenseHierarchy(){let t=!1;const e={},i=(t,e)=>{const i=this.hierarchical.trees;for(const o in i)Object.prototype.hasOwnProperty.call(i,o)&&i[o]===t&&this.direction.shift(o,e)},o=()=>{const t=[];for(let e=0;e<this.hierarchical.numTrees();e++)t.push(this.direction.getTreeSize(e));return t},s=(t,e)=>{if(!e[t.id]&&(e[t.id]=!0,this.hierarchical.childrenReference[t.id])){const i=this.hierarchical.childrenReference[t.id];if(i.length>0)for(let t=0;t<i.length;t++)s(this.body.nodes[i[t]],e)}},n=(t,e=1e9)=>{let i=1e9,o=1e9,s=1e9,n=-1e9;for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)){const d=this.body.nodes[r],a=this.hierarchical.levels[d.id],h=this.direction.getPosition(d),[l,c]=this._getSpaceAroundNode(d,t);i=Math.min(l,i),o=Math.min(c,o),a<=e&&(s=Math.min(h,s),n=Math.max(h,n))}return[s,n,i,o]},r=(t,e)=>{const i=this.hierarchical.getMaxLevel(t.id),o=this.hierarchical.getMaxLevel(e.id);return Math.min(i,o)},d=(t,e,i)=>{const o=this.hierarchical;for(let s=0;s<e.length;s++){const n=e[s],r=o.distributionOrdering[n];if(r.length>1)for(let e=0;e<r.length-1;e++){const s=r[e],n=r[e+1];o.hasSameParent(s,n)&&o.inSameSubNetwork(s,n)&&t(s,n,i)}}},a=(e,i,o=!1)=>{const d=this.direction.getPosition(e),a=this.direction.getPosition(i),h=Math.abs(a-d),l=this.options.hierarchical.nodeSpacing;if(h>l){const d={},a={};s(e,d),s(i,a);const h=r(e,i),c=n(d,h),p=n(a,h),u=c[1],g=p[0],b=p[2];if(Math.abs(u-g)>l){let e=u-g+l;e<-b+l&&(e=-b+l),e<0&&(this._shiftBlock(i.id,e),t=!0,!0===o&&this._centerParent(i))}}},h=(i,o)=>{const r=o.id,d=o.edges,a=this.hierarchical.levels[o.id],h=this.options.hierarchical.levelSeparation*this.options.hierarchical.levelSeparation,l={},c=[];for(let t=0;t<d.length;t++){const e=d[t];if(e.toId!=e.fromId){const i=e.toId==r?e.from:e.to;l[d[t].id]=i,this.hierarchical.levels[i.id]<a&&c.push(e)}}const p=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const s=this.direction.getPosition(l[e[o].id])-t;i+=s/Math.sqrt(s*s+h)}return i},u=(t,e)=>{let i=0;for(let o=0;o<e.length;o++)if(void 0!==l[e[o].id]){const s=this.direction.getPosition(l[e[o].id])-t;i-=h*Math.pow(s*s+h,-1.5)}return i},g=(t,e)=>{let i=this.direction.getPosition(o);const s={};for(let o=0;o<t;o++){const t=p(i,e),n=u(i,e),r=40;if(i-=Math.max(-r,Math.min(r,Math.round(t/n))),void 0!==s[i])break;s[i]=o}return i};let b=g(i,c);(i=>{const r=this.direction.getPosition(o);if(void 0===e[o.id]){const t={};s(o,t),e[o.id]=t}const d=n(e[o.id]),a=d[2],h=d[3],l=i-r;let c=0;l>0?c=Math.min(l,h-this.options.hierarchical.nodeSpacing):l<0&&(c=-Math.min(-l,a-this.options.hierarchical.nodeSpacing)),0!=c&&(this._shiftBlock(o.id,c),t=!0)})(b),b=g(i,d),(e=>{const i=this.direction.getPosition(o),[s,n]=this._getSpaceAroundNode(o),r=e-i;let d=i;r>0?d=Math.min(i+(n-this.options.hierarchical.nodeSpacing),e):r<0&&(d=Math.max(i-(s-this.options.hierarchical.nodeSpacing),e)),d!==i&&(this.direction.setPosition(o,d),t=!0)})(b)},l=e=>{let i=this.hierarchical.getLevels();i=i.reverse();for(let o=0;o<e;o++){t=!1;for(let t=0;t<i.length;t++){const e=i[t],o=this.hierarchical.distributionOrdering[e];for(let t=0;t<o.length;t++)h(1e3,o[t])}if(!0!==t)break}},c=e=>{let i=this.hierarchical.getLevels();i=i.reverse();for(let o=0;o<e&&(t=!1,d(a,i,!0),!0===t);o++);},p=()=>{for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&this._centerParent(this.body.nodes[t])},u=()=>{let t=this.hierarchical.getLevels();t=t.reverse();for(let e=0;e<t.length;e++){const i=t[e],o=this.hierarchical.distributionOrdering[i];for(let t=0;t<o.length;t++)this._centerParent(o[t])}};!0===this.options.hierarchical.blockShifting&&(c(5),p()),!0===this.options.hierarchical.edgeMinimization&&l(20),!0===this.options.hierarchical.parentCentralization&&u(),(()=>{const t=o();let e=0;for(let o=0;o<t.length-1;o++){e+=t[o].max-t[o+1].min+this.options.hierarchical.treeSpacing,i(o+1,e)}})()}_getSpaceAroundNode(t,e){let i=!0;void 0===e&&(i=!1);const o=this.hierarchical.levels[t.id];if(void 0!==o){const s=this.hierarchical.distributionIndex[t.id],n=this.direction.getPosition(t),r=this.hierarchical.distributionOrdering[o];let d=1e9,a=1e9;if(0!==s){const t=r[s-1];if(!0===i&&void 0===e[t.id]||!1===i){d=n-this.direction.getPosition(t)}}if(s!=r.length-1){const t=r[s+1];if(!0===i&&void 0===e[t.id]||!1===i){const e=this.direction.getPosition(t);a=Math.min(a,e-n)}}return[d,a]}return[0,0]}_centerParent(t){if(this.hierarchical.parentReference[t.id]){const e=this.hierarchical.parentReference[t.id];for(let t=0;t<e.length;t++){const i=e[t],o=this.body.nodes[i],s=this.hierarchical.childrenReference[i];if(void 0!==s){const t=this._getCenterPosition(s),e=this.direction.getPosition(o),[i,n]=this._getSpaceAroundNode(o),r=e-t;(r<0&&Math.abs(r)<n-this.options.hierarchical.nodeSpacing||r>0&&Math.abs(r)<i-this.options.hierarchical.nodeSpacing)&&this.direction.setPosition(o,t)}}}}_placeNodesByHierarchy(t){this.positionedNodes={};for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)){let i=Object.keys(t[e]);i=this._indexArrayToNodes(i),this.direction.sort(i);let o=0;for(let t=0;t<i.length;t++){const s=i[t];if(void 0===this.positionedNodes[s.id]){const n=this.options.hierarchical.nodeSpacing;let r=n*o;o>0&&(r=this.direction.getPosition(i[t-1])+n),this.direction.setPosition(s,r,e),this._validatePositionAndContinue(s,e,r),o++}}}}_placeBranchNodes(t,e){const i=this.hierarchical.childrenReference[t];if(void 0===i)return;const o=[];for(let t=0;t<i.length;t++)o.push(this.body.nodes[i[t]]);this.direction.sort(o);for(let i=0;i<o.length;i++){const s=o[i],n=this.hierarchical.levels[s.id];if(!(n>e&&void 0===this.positionedNodes[s.id]))return;{const e=this.options.hierarchical.nodeSpacing;let r;r=0===i?this.direction.getPosition(this.body.nodes[t]):this.direction.getPosition(o[i-1])+e,this.direction.setPosition(s,r,n),this._validatePositionAndContinue(s,n,r)}}const s=this._getCenterPosition(o);this.direction.setPosition(this.body.nodes[t],s,e)}_validatePositionAndContinue(t,e,i){if(this.hierarchical.isTree){if(void 0!==this.lastNodeOnLevel[e]){const o=this.direction.getPosition(this.body.nodes[this.lastNodeOnLevel[e]]);if(i-o<this.options.hierarchical.nodeSpacing){const s=o+this.options.hierarchical.nodeSpacing-i,n=this._findCommonParent(this.lastNodeOnLevel[e],t.id);this._shiftBlock(n.withChild,s)}}this.lastNodeOnLevel[e]=t.id,this.positionedNodes[t.id]=!0,this._placeBranchNodes(t.id,e)}}_indexArrayToNodes(t){const e=[];for(let i=0;i<t.length;i++)e.push(this.body.nodes[t[i]]);return e}_getDistribution(){const t={};let e,i;for(e in this.body.nodes)if(Object.prototype.hasOwnProperty.call(this.body.nodes,e)){i=this.body.nodes[e];const o=void 0===this.hierarchical.levels[e]?0:this.hierarchical.levels[e];this.direction.fix(i,o),void 0===t[o]&&(t[o]={}),t[o][e]=i}return t}_getActiveEdges(t){const e=[];return i.forEach(t.edges,t=>{-1!==this.body.edgeIndices.indexOf(t.id)&&e.push(t)}),e}_getHubSizes(){const t={},e=this.body.nodeIndices;i.forEach(e,e=>{const i=this.body.nodes[e],o=this._getActiveEdges(i).length;t[o]=!0});const o=[];return i.forEach(t,t=>{o.push(Number(t))}),o.sort(function(t,e){return e-t}),o}_determineLevelsByHubsize(){const t=(t,e)=>{this.hierarchical.levelDownstream(t,e)},e=this._getHubSizes();for(let o=0;o<e.length;++o){const s=e[o];if(0===s)break;i.forEach(this.body.nodeIndices,e=>{const i=this.body.nodes[e];s===this._getActiveEdges(i).length&&this._crawlNetwork(t,e)})}}_determineLevelsCustomCallback(){this._crawlNetwork((t,e,i)=>{let o=this.hierarchical.levels[t.id];void 0===o&&(o=this.hierarchical.levels[t.id]=1e5);const s=(oe.cloneOptions(t,"node"),oe.cloneOptions(e,"node"),void oe.cloneOptions(i,"edge"));this.hierarchical.levels[e.id]=o+s}),this.hierarchical.setMinLevelToZero()}_determineLevelsDirected(){const t=this.body.nodeIndices.reduce((t,e)=>(t.set(e,this.body.nodes[e]),t),new Map);"roots"===this.options.hierarchical.shakeTowards?this.hierarchical.levels=function(t){return xe(e=>e.edges.filter(e=>t.has(e.toId)).every(t=>t.from===e),(t,e)=>e<t,"to",t)}(t):this.hierarchical.levels=function(t){return xe(e=>e.edges.filter(e=>t.has(e.toId)).every(t=>t.to===e),(t,e)=>e>t,"from",t)}(t),this.hierarchical.setMinLevelToZero()}_generateMap(){this._crawlNetwork((t,e)=>{this.hierarchical.levels[e.id]>this.hierarchical.levels[t.id]&&this.hierarchical.addRelation(t.id,e.id)}),this.hierarchical.checkIfTree()}_crawlNetwork(t=function(){},e){const i={},o=(e,s)=>{if(void 0===i[e.id]){let n;this.hierarchical.setTreeIndex(e,s),i[e.id]=!0;const r=this._getActiveEdges(e);for(let i=0;i<r.length;i++){const d=r[i];!0===d.connected&&(n=d.toId==e.id?d.from:d.to,e.id!=n.id&&(t(e,n,d),o(n,s)))}}};if(void 0===e){let t=0;for(let e=0;e<this.body.nodeIndices.length;e++){const s=this.body.nodeIndices[e];if(void 0===i[s]){const e=this.body.nodes[s];o(e,t),t+=1}}}else{const t=this.body.nodes[e];if(void 0===t)return void console.error("Node not found:",e);o(t)}}_shiftBlock(t,e){const i={},o=t=>{if(i[t])return;i[t]=!0,this.direction.shift(t,e);const s=this.hierarchical.childrenReference[t];if(void 0!==s)for(let t=0;t<s.length;t++)o(s[t])};o(t)}_findCommonParent(t,e){const i={},o=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let e=0;e<i.length;e++){const s=i[e];t[s]=!0,o(t,s)}},s=(t,e)=>{const i=this.hierarchical.parentReference[e];if(void 0!==i)for(let o=0;o<i.length;o++){const n=i[o];if(void 0!==t[n])return{foundParent:n,withChild:e};const r=s(t,n);if(null!==r.foundParent)return r}return{foundParent:null,withChild:e}};return o(i,t),s(i,e)}setDirectionStrategy(){const t="UD"===this.options.hierarchical.direction||"DU"===this.options.hierarchical.direction;this.direction=t?new ye(this):new ve(this)}_getCenterPosition(t){let e=1e9,i=-1e9;for(let o=0;o<t.length;o++){let s;if(void 0!==t[o].id)s=t[o];else{const e=t[o];s=this.body.nodes[e]}const n=this.direction.getPosition(s);e=Math.min(e,n),i=Math.max(i,n)}return.5*(e+i)}}class Oe{constructor(t,e,i,o){this.body=t,this.canvas=e,this.selectionHandler=i,this.interactionHandler=o,this.editMode=!1,this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0,this._domEventListenerCleanupQueue=[],this.temporaryUIFunctions={},this.temporaryEventFunctions=[],this.touchTime=0,this.temporaryIds={nodes:[],edges:[]},this.guiEnabled=!1,this.inMode=!1,this.selectedControlNode=void 0,this.options={},this.defaultOptions={enabled:!1,initiallyActive:!1,addNode:!0,addEdge:!0,editNode:void 0,editEdge:!0,deleteNode:!0,deleteEdge:!0,controlNodeStyle:{shape:"dot",size:6,color:{background:"#ff0000",border:"#3c3c3c",highlight:{background:"#07f968",border:"#3c3c3c"}},borderWidth:2,borderWidthSelected:2}},Object.assign(this.options,this.defaultOptions),this.body.emitter.on("destroy",()=>{this._clean()}),this.body.emitter.on("_dataChanged",this._restore.bind(this)),this.body.emitter.on("_resetData",this._restore.bind(this))}_restore(){!1!==this.inMode&&(!0===this.options.initiallyActive?this.enableEditMode():this.disableEditMode())}setOptions(t,e,o){void 0!==e&&(void 0!==e.locale?this.options.locale=e.locale:this.options.locale=o.locale,void 0!==e.locales?this.options.locales=e.locales:this.options.locales=o.locales),void 0!==t&&("boolean"==typeof t?this.options.enabled=t:(this.options.enabled=!0,i.deepExtend(this.options,t)),!0===this.options.initiallyActive&&(this.editMode=!0),this._setup())}toggleEditMode(){!0===this.editMode?this.disableEditMode():this.enableEditMode()}enableEditMode(){this.editMode=!0,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="block",this.closeDiv.style.display="block",this.editModeDiv.style.display="none",this.showManipulatorToolbar())}disableEditMode(){this.editMode=!1,this._clean(),!0===this.guiEnabled&&(this.manipulationDiv.style.display="none",this.closeDiv.style.display="none",this.editModeDiv.style.display="block",this._createEditButton())}showManipulatorToolbar(){if(this._clean(),this.manipulationDOM={},!0===this.guiEnabled){this.editMode=!0,this.manipulationDiv.style.display="block",this.closeDiv.style.display="block";const t=this.selectionHandler.getSelectedNodeCount(),e=this.selectionHandler.getSelectedEdgeCount(),i=t+e,o=this.options.locales[this.options.locale];let s=!1;!1!==this.options.addNode&&(this._createAddNodeButton(o),s=!0),!1!==this.options.addEdge&&(!0===s?this._createSeperator(1):s=!0,this._createAddEdgeButton(o)),1===t&&"function"==typeof this.options.editNode?(!0===s?this._createSeperator(2):s=!0,this._createEditNodeButton(o)):1===e&&0===t&&!1!==this.options.editEdge&&(!0===s?this._createSeperator(3):s=!0,this._createEditEdgeButton(o)),0!==i&&(t>0&&!1!==this.options.deleteNode||0===t&&!1!==this.options.deleteEdge)&&(!0===s&&this._createSeperator(4),this._createDeleteButton(o)),this._bindElementEvents(this.closeDiv,this.toggleEditMode.bind(this)),this._temporaryBindEvent("select",this.showManipulatorToolbar.bind(this))}this.body.emitter.emit("_redraw")}addNodeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addNode",!0===this.guiEnabled){const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.addDescription||this.options.locales.en.addDescription),this._bindElementEvents(this.closeDiv,this.toggleEditMode.bind(this))}this._temporaryBindEvent("click",this._performAddNode.bind(this))}editNode(){!0!==this.editMode&&this.enableEditMode(),this._clean();const t=this.selectionHandler.getSelectedNodes()[0];if(void 0!==t){if(this.inMode="editNode","function"!=typeof this.options.editNode)throw new Error("No function has been configured to handle the editing of nodes.");if(!0!==t.isCluster){const e=i.deepExtend({},t.options,!1);if(e.x=t.x,e.y=t.y,2!==this.options.editNode.length)throw new Error("The function for edit does not support two arguments (data, callback)");this.options.editNode(e,t=>{null!=t&&"editNode"===this.inMode&&this.body.data.nodes.getDataSet().update(t),this.showManipulatorToolbar()})}else alert(this.options.locales[this.options.locale].editClusterError||this.options.locales.en.editClusterError)}else this.showManipulatorToolbar()}addEdgeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="addEdge",!0===this.guiEnabled){const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.edgeDescription||this.options.locales.en.edgeDescription),this._bindElementEvents(this.closeDiv,this.toggleEditMode.bind(this))}this._temporaryBindUI("onTouch",this._handleConnect.bind(this)),this._temporaryBindUI("onDragEnd",this._finishConnect.bind(this)),this._temporaryBindUI("onDrag",this._dragControlNode.bind(this)),this._temporaryBindUI("onRelease",this._finishConnect.bind(this)),this._temporaryBindUI("onDragStart",this._dragStartEdge.bind(this)),this._temporaryBindUI("onHold",()=>{})}editEdgeMode(){if(!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="editEdge","object"==typeof this.options.editEdge&&"function"==typeof this.options.editEdge.editWithoutDrag&&(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId)){const t=this.body.edges[this.edgeBeingEditedId];return void this._performEditEdge(t.from.id,t.to.id)}if(!0===this.guiEnabled){const t=this.options.locales[this.options.locale];this.manipulationDOM={},this._createBackButton(t),this._createSeperator(),this._createDescription(t.editEdgeDescription||this.options.locales.en.editEdgeDescription),this._bindElementEvents(this.closeDiv,this.toggleEditMode.bind(this))}if(this.edgeBeingEditedId=this.selectionHandler.getSelectedEdgeIds()[0],void 0!==this.edgeBeingEditedId){const t=this.body.edges[this.edgeBeingEditedId],e=this._getNewTargetNode(t.from.x,t.from.y),i=this._getNewTargetNode(t.to.x,t.to.y);this.temporaryIds.nodes.push(e.id),this.temporaryIds.nodes.push(i.id),this.body.nodes[e.id]=e,this.body.nodeIndices.push(e.id),this.body.nodes[i.id]=i,this.body.nodeIndices.push(i.id),this._temporaryBindUI("onTouch",this._controlNodeTouch.bind(this)),this._temporaryBindUI("onTap",()=>{}),this._temporaryBindUI("onHold",()=>{}),this._temporaryBindUI("onDragStart",this._controlNodeDragStart.bind(this)),this._temporaryBindUI("onDrag",this._controlNodeDrag.bind(this)),this._temporaryBindUI("onDragEnd",this._controlNodeDragEnd.bind(this)),this._temporaryBindUI("onMouseMove",()=>{}),this._temporaryBindEvent("beforeDrawing",o=>{const s=t.edgeType.findBorderPositions(o);!1===e.selected&&(e.x=s.from.x,e.y=s.from.y),!1===i.selected&&(i.x=s.to.x,i.y=s.to.y)}),this.body.emitter.emit("_redraw")}else this.showManipulatorToolbar()}deleteSelected(){!0!==this.editMode&&this.enableEditMode(),this._clean(),this.inMode="delete";const t=this.selectionHandler.getSelectedNodeIds(),e=this.selectionHandler.getSelectedEdgeIds();let i;if(t.length>0){for(let e=0;e<t.length;e++)if(!0===this.body.nodes[t[e]].isCluster)return void alert(this.options.locales[this.options.locale].deleteClusterError||this.options.locales.en.deleteClusterError);"function"==typeof this.options.deleteNode&&(i=this.options.deleteNode)}else e.length>0&&"function"==typeof this.options.deleteEdge&&(i=this.options.deleteEdge);if("function"==typeof i){const o={nodes:t,edges:e};if(2!==i.length)throw new Error("The function for delete does not support two arguments (data, callback)");i(o,t=>{null!=t&&"delete"===this.inMode?(this.body.data.edges.getDataSet().remove(t.edges),this.body.data.nodes.getDataSet().remove(t.nodes),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()):(this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().remove(e),this.body.data.nodes.getDataSet().remove(t),this.body.emitter.emit("startSimulation"),this.showManipulatorToolbar()}_setup(){!0===this.options.enabled?(this.guiEnabled=!0,this._createWrappers(),!1===this.editMode?this._createEditButton():this.showManipulatorToolbar()):(this._removeManipulationDOM(),this.guiEnabled=!1)}_createWrappers(){void 0===this.manipulationDiv&&(this.manipulationDiv=document.createElement("div"),this.manipulationDiv.className="vis-manipulation",!0===this.editMode?this.manipulationDiv.style.display="block":this.manipulationDiv.style.display="none",this.canvas.frame.appendChild(this.manipulationDiv)),void 0===this.editModeDiv&&(this.editModeDiv=document.createElement("div"),this.editModeDiv.className="vis-edit-mode",!0===this.editMode?this.editModeDiv.style.display="none":this.editModeDiv.style.display="block",this.canvas.frame.appendChild(this.editModeDiv)),void 0===this.closeDiv&&(this.closeDiv=document.createElement("button"),this.closeDiv.className="vis-close",this.closeDiv.setAttribute("aria-label",this.options.locales[this.options.locale]?.close??this.options.locales.en.close),this.closeDiv.style.display=this.manipulationDiv.style.display,this.canvas.frame.appendChild(this.closeDiv))}_getNewTargetNode(t,e){const o=i.deepExtend({},this.options.controlNodeStyle);o.id="targetNode"+s.v4(),o.hidden=!1,o.physics=!1,o.x=t,o.y=e;const n=this.body.functions.createNode(o);return n.shape.boundingBox={left:t,right:t,top:e,bottom:e},n}_createEditButton(){this._clean(),this.manipulationDOM={},i.recursiveDOMDelete(this.editModeDiv);const t=this.options.locales[this.options.locale],e=this._createButton("editMode","vis-edit vis-edit-mode",t.edit||this.options.locales.en.edit);this.editModeDiv.appendChild(e),this._bindElementEvents(e,this.toggleEditMode.bind(this))}_clean(){this.inMode=!1,!0===this.guiEnabled&&(i.recursiveDOMDelete(this.editModeDiv),i.recursiveDOMDelete(this.manipulationDiv),this._cleanupDOMEventListeners()),this._cleanupTemporaryNodesAndEdges(),this._unbindTemporaryUIs(),this._unbindTemporaryEvents(),this.body.emitter.emit("restorePhysics")}_cleanupDOMEventListeners(){for(const t of this._domEventListenerCleanupQueue.splice(0))t()}_removeManipulationDOM(){this._clean(),i.recursiveDOMDelete(this.manipulationDiv),i.recursiveDOMDelete(this.editModeDiv),i.recursiveDOMDelete(this.closeDiv),this.manipulationDiv&&this.canvas.frame.removeChild(this.manipulationDiv),this.editModeDiv&&this.canvas.frame.removeChild(this.editModeDiv),this.closeDiv&&this.canvas.frame.removeChild(this.closeDiv),this.manipulationDiv=void 0,this.editModeDiv=void 0,this.closeDiv=void 0}_createSeperator(t=1){this.manipulationDOM["seperatorLineDiv"+t]=document.createElement("div"),this.manipulationDOM["seperatorLineDiv"+t].className="vis-separator-line",this.manipulationDiv.appendChild(this.manipulationDOM["seperatorLineDiv"+t])}_createAddNodeButton(t){const e=this._createButton("addNode","vis-add",t.addNode||this.options.locales.en.addNode);this.manipulationDiv.appendChild(e),this._bindElementEvents(e,this.addNodeMode.bind(this))}_createAddEdgeButton(t){const e=this._createButton("addEdge","vis-connect",t.addEdge||this.options.locales.en.addEdge);this.manipulationDiv.appendChild(e),this._bindElementEvents(e,this.addEdgeMode.bind(this))}_createEditNodeButton(t){const e=this._createButton("editNode","vis-edit",t.editNode||this.options.locales.en.editNode);this.manipulationDiv.appendChild(e),this._bindElementEvents(e,this.editNode.bind(this))}_createEditEdgeButton(t){const e=this._createButton("editEdge","vis-edit",t.editEdge||this.options.locales.en.editEdge);this.manipulationDiv.appendChild(e),this._bindElementEvents(e,this.editEdgeMode.bind(this))}_createDeleteButton(t){let e;e=this.options.rtl?"vis-delete-rtl":"vis-delete";const i=this._createButton("delete",e,t.del||this.options.locales.en.del);this.manipulationDiv.appendChild(i),this._bindElementEvents(i,this.deleteSelected.bind(this))}_createBackButton(t){const e=this._createButton("back","vis-back",t.back||this.options.locales.en.back);this.manipulationDiv.appendChild(e),this._bindElementEvents(e,this.showManipulatorToolbar.bind(this))}_createButton(t,e,i,o="vis-label"){return this.manipulationDOM[t+"Div"]=document.createElement("button"),this.manipulationDOM[t+"Div"].className="vis-button "+e,this.manipulationDOM[t+"Label"]=document.createElement("div"),this.manipulationDOM[t+"Label"].className=o,this.manipulationDOM[t+"Label"].innerText=i,this.manipulationDOM[t+"Div"].appendChild(this.manipulationDOM[t+"Label"]),this.manipulationDOM[t+"Div"]}_createDescription(t){this.manipulationDOM.descriptionLabel=document.createElement("div"),this.manipulationDOM.descriptionLabel.className="vis-none",this.manipulationDOM.descriptionLabel.innerText=t,this.manipulationDiv.appendChild(this.manipulationDOM.descriptionLabel)}_temporaryBindEvent(t,e){this.temporaryEventFunctions.push({event:t,boundFunction:e}),this.body.emitter.on(t,e)}_temporaryBindUI(t,e){if(void 0===this.body.eventListeners[t])throw new Error("This UI function does not exist. Typo? You tried: "+t+" possible are: "+JSON.stringify(Object.keys(this.body.eventListeners)));this.temporaryUIFunctions[t]=this.body.eventListeners[t],this.body.eventListeners[t]=e}_unbindTemporaryUIs(){for(const t in this.temporaryUIFunctions)Object.prototype.hasOwnProperty.call(this.temporaryUIFunctions,t)&&(this.body.eventListeners[t]=this.temporaryUIFunctions[t],delete this.temporaryUIFunctions[t]);this.temporaryUIFunctions={}}_unbindTemporaryEvents(){for(let t=0;t<this.temporaryEventFunctions.length;t++){const e=this.temporaryEventFunctions[t].event,i=this.temporaryEventFunctions[t].boundFunction;this.body.emitter.off(e,i)}this.temporaryEventFunctions=[]}_bindElementEvents(t,e){const o=new i.Hammer(t,{});de(o,e),this._domEventListenerCleanupQueue.push(()=>{o.destroy()});const s=({keyCode:t,key:i})=>{"Enter"!==i&&" "!==i&&13!==t&&32!==t||e()};t.addEventListener("keyup",s,!1),this._domEventListenerCleanupQueue.push(()=>{t.removeEventListener("keyup",s,!1)})}_cleanupTemporaryNodesAndEdges(){for(let t=0;t<this.temporaryIds.edges.length;t++){this.body.edges[this.temporaryIds.edges[t]].disconnect(),delete this.body.edges[this.temporaryIds.edges[t]];const e=this.body.edgeIndices.indexOf(this.temporaryIds.edges[t]);-1!==e&&this.body.edgeIndices.splice(e,1)}for(let t=0;t<this.temporaryIds.nodes.length;t++){delete this.body.nodes[this.temporaryIds.nodes[t]];const e=this.body.nodeIndices.indexOf(this.temporaryIds.nodes[t]);-1!==e&&this.body.nodeIndices.splice(e,1)}this.temporaryIds={nodes:[],edges:[]}}_controlNodeTouch(t){this.selectionHandler.unselectAll(),this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Object.assign({},this.body.view.translation)}_controlNodeDragStart(){const t=this.lastTouch,e=this.selectionHandler._pointerToPositionObject(t),i=this.body.nodes[this.temporaryIds.nodes[0]],o=this.body.nodes[this.temporaryIds.nodes[1]],s=this.body.edges[this.edgeBeingEditedId];this.selectedControlNode=void 0;const n=i.isOverlappingWith(e),r=o.isOverlappingWith(e);!0===n?(this.selectedControlNode=i,s.edgeType.from=i):!0===r&&(this.selectedControlNode=o,s.edgeType.to=o),void 0!==this.selectedControlNode&&this.selectionHandler.selectObject(this.selectedControlNode),this.body.emitter.emit("_redraw")}_controlNodeDrag(t){this.body.emitter.emit("disablePhysics");const e=this.body.functions.getPointer(t.center),i=this.canvas.DOMtoCanvas(e);void 0!==this.selectedControlNode?(this.selectedControlNode.x=i.x,this.selectedControlNode.y=i.y):this.interactionHandler.onDrag(t),this.body.emitter.emit("_redraw")}_controlNodeDragEnd(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e),o=this.body.edges[this.edgeBeingEditedId];if(void 0===this.selectedControlNode)return;this.selectionHandler.unselectAll();const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--)if(s[t]!==this.selectedControlNode.id){n=this.body.nodes[s[t]];break}if(void 0!==n&&void 0!==this.selectedControlNode)if(!0===n.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this.body.nodes[this.temporaryIds.nodes[0]];this.selectedControlNode.id===t.id?this._performEditEdge(n.id,o.to.id):this._performEditEdge(o.from.id,n.id)}else o.updateEdgeType(),this.body.emitter.emit("restorePhysics");this.body.emitter.emit("_redraw")}_handleConnect(t){if((new Date).valueOf()-this.touchTime>100){this.lastTouch=this.body.functions.getPointer(t.center),this.lastTouch.translation=Object.assign({},this.body.view.translation),this.interactionHandler.drag.pointer=this.lastTouch,this.interactionHandler.drag.translation=this.lastTouch.translation;const e=this.lastTouch,i=this.selectionHandler.getNodeAt(e);if(void 0!==i)if(!0===i.isCluster)alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError);else{const t=this._getNewTargetNode(i.x,i.y);this.body.nodes[t.id]=t,this.body.nodeIndices.push(t.id);const e=this.body.functions.createEdge({id:"connectionEdge"+s.v4(),from:i.id,to:t.id,physics:!1,smooth:{enabled:!0,type:"continuous",roundness:.5}});this.body.edges[e.id]=e,this.body.edgeIndices.push(e.id),this.temporaryIds.nodes.push(t.id),this.temporaryIds.edges.push(e.id)}this.touchTime=(new Date).valueOf()}}_dragControlNode(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--)if(-1===this.temporaryIds.nodes.indexOf(s[t])){n=this.body.nodes[s[t]];break}if(t.controlEdge={from:o,to:n?n.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragging",t,e),void 0!==this.temporaryIds.nodes[0]){const t=this.body.nodes[this.temporaryIds.nodes[0]];t.x=this.canvas._XconvertDOMtoCanvas(e.x),t.y=this.canvas._YconvertDOMtoCanvas(e.y),this.body.emitter.emit("_redraw")}else this.interactionHandler.onDrag(t)}_finishConnect(t){const e=this.body.functions.getPointer(t.center),i=this.selectionHandler._pointerToPositionObject(e);let o;void 0!==this.temporaryIds.edges[0]&&(o=this.body.edges[this.temporaryIds.edges[0]].fromId);const s=this.selectionHandler._getAllNodesOverlappingWith(i);let n;for(let t=s.length-1;t>=0;t--)if(-1===this.temporaryIds.nodes.indexOf(s[t])){n=this.body.nodes[s[t]];break}this._cleanupTemporaryNodesAndEdges(),void 0!==n&&(!0===n.isCluster?alert(this.options.locales[this.options.locale].createEdgeError||this.options.locales.en.createEdgeError):void 0!==this.body.nodes[o]&&void 0!==this.body.nodes[n.id]&&this._performAddEdge(o,n.id)),t.controlEdge={from:o,to:n?n.id:void 0},this.selectionHandler.generateClickEvent("controlNodeDragEnd",t,e),this.body.emitter.emit("_redraw")}_dragStartEdge(t){const e=this.lastTouch;this.selectionHandler.generateClickEvent("dragStart",t,e,void 0,!0)}_performAddNode(t){const e={id:s.v4(),x:t.pointer.canvas.x,y:t.pointer.canvas.y,label:"new"};if("function"==typeof this.options.addNode){if(2!==this.options.addNode.length)throw this.showManipulatorToolbar(),new Error("The function for add does not support two arguments (data,callback)");this.options.addNode(e,t=>{null!=t&&"addNode"===this.inMode&&this.body.data.nodes.getDataSet().add(t),this.showManipulatorToolbar()})}else this.body.data.nodes.getDataSet().add(e),this.showManipulatorToolbar()}_performAddEdge(t,e){const i={from:t,to:e};if("function"==typeof this.options.addEdge){if(2!==this.options.addEdge.length)throw new Error("The function for connect does not support two arguments (data,callback)");this.options.addEdge(i,t=>{null!=t&&"addEdge"===this.inMode&&(this.body.data.edges.getDataSet().add(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().add(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}_performEditEdge(t,e){const i={id:this.edgeBeingEditedId,from:t,to:e,label:this.body.data.edges.get(this.edgeBeingEditedId).label};let o=this.options.editEdge;if("object"==typeof o&&(o=o.editWithoutDrag),"function"==typeof o){if(2!==o.length)throw new Error("The function for edit does not support two arguments (data, callback)");o(i,t=>{null==t||"editEdge"!==this.inMode?(this.body.edges[i.id].updateEdgeType(),this.body.emitter.emit("_redraw"),this.showManipulatorToolbar()):(this.body.data.edges.getDataSet().update(t),this.selectionHandler.unselectAll(),this.showManipulatorToolbar())})}else this.body.data.edges.getDataSet().update(i),this.selectionHandler.unselectAll(),this.showManipulatorToolbar()}}const Me="string",Ce="boolean",Se="number",ke="array",Ie="object",ze=["arrow","bar","box","circle","crow","curve","diamond","image","inv_curve","inv_triangle","triangle","vee"],Te={borderWidth:{number:Se},borderWidthSelected:{number:Se,undefined:"undefined"},brokenImage:{string:Me,undefined:"undefined"},chosen:{label:{boolean:Ce,function:"function"},node:{boolean:Ce,function:"function"},__type__:{object:Ie,boolean:Ce}},color:{border:{string:Me},background:{string:Me},highlight:{border:{string:Me},background:{string:Me},__type__:{object:Ie,string:Me}},hover:{border:{string:Me},background:{string:Me},__type__:{object:Ie,string:Me}},__type__:{object:Ie,string:Me}},opacity:{number:Se,undefined:"undefined"},fixed:{x:{boolean:Ce},y:{boolean:Ce},__type__:{object:Ie,boolean:Ce}},font:{align:{string:Me},color:{string:Me},size:{number:Se},face:{string:Me},background:{string:Me},strokeWidth:{number:Se},strokeColor:{string:Me},vadjust:{number:Se},multi:{boolean:Ce,string:Me},bold:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},boldital:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},ital:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},mono:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},__type__:{object:Ie,string:Me}},group:{string:Me,number:Se,undefined:"undefined"},heightConstraint:{minimum:{number:Se},valign:{string:Me},__type__:{object:Ie,boolean:Ce,number:Se}},hidden:{boolean:Ce},icon:{face:{string:Me},code:{string:Me},size:{number:Se},color:{string:Me},weight:{string:Me,number:Se},__type__:{object:Ie}},id:{string:Me,number:Se},image:{selected:{string:Me,undefined:"undefined"},unselected:{string:Me,undefined:"undefined"},__type__:{object:Ie,string:Me}},imagePadding:{top:{number:Se},right:{number:Se},bottom:{number:Se},left:{number:Se},__type__:{object:Ie,number:Se}},label:{string:Me,undefined:"undefined"},labelHighlightBold:{boolean:Ce},level:{number:Se,undefined:"undefined"},margin:{top:{number:Se},right:{number:Se},bottom:{number:Se},left:{number:Se},__type__:{object:Ie,number:Se}},mass:{number:Se},physics:{boolean:Ce},scaling:{min:{number:Se},max:{number:Se},label:{enabled:{boolean:Ce},min:{number:Se},max:{number:Se},maxVisible:{number:Se},drawThreshold:{number:Se},__type__:{object:Ie,boolean:Ce}},customScalingFunction:{function:"function"},__type__:{object:Ie}},shadow:{enabled:{boolean:Ce},color:{string:Me},size:{number:Se},x:{number:Se},y:{number:Se},__type__:{object:Ie,boolean:Ce}},shape:{string:["custom","ellipse","circle","database","box","text","image","circularImage","diamond","dot","star","triangle","triangleDown","square","icon","hexagon"]},ctxRenderer:{function:"function"},shapeProperties:{borderDashes:{boolean:Ce,array:ke},borderRadius:{number:Se},interpolation:{boolean:Ce},useImageSize:{boolean:Ce},useBorderWithImage:{boolean:Ce},coordinateOrigin:{string:["center","top-left"]},__type__:{object:Ie}},size:{number:Se},title:{string:Me,dom:"dom",undefined:"undefined"},value:{number:Se,undefined:"undefined"},widthConstraint:{minimum:{number:Se},maximum:{number:Se},__type__:{object:Ie,boolean:Ce,number:Se}},x:{number:Se},y:{number:Se},__type__:{object:Ie}},De={configure:{enabled:{boolean:Ce},filter:{boolean:Ce,string:Me,array:ke,function:"function"},container:{dom:"dom"},showButton:{boolean:Ce},__type__:{object:Ie,boolean:Ce,string:Me,array:ke,function:"function"}},edges:{arrows:{to:{enabled:{boolean:Ce},scaleFactor:{number:Se},type:{string:ze},imageHeight:{number:Se},imageWidth:{number:Se},src:{string:Me},__type__:{object:Ie,boolean:Ce}},middle:{enabled:{boolean:Ce},scaleFactor:{number:Se},type:{string:ze},imageWidth:{number:Se},imageHeight:{number:Se},src:{string:Me},__type__:{object:Ie,boolean:Ce}},from:{enabled:{boolean:Ce},scaleFactor:{number:Se},type:{string:ze},imageWidth:{number:Se},imageHeight:{number:Se},src:{string:Me},__type__:{object:Ie,boolean:Ce}},__type__:{string:["from","to","middle"],object:Ie}},endPointOffset:{from:{number:Se},to:{number:Se},__type__:{object:Ie,number:Se}},arrowStrikethrough:{boolean:Ce},background:{enabled:{boolean:Ce},color:{string:Me},size:{number:Se},dashes:{boolean:Ce,array:ke},__type__:{object:Ie,boolean:Ce}},chosen:{label:{boolean:Ce,function:"function"},edge:{boolean:Ce,function:"function"},__type__:{object:Ie,boolean:Ce}},color:{color:{string:Me},highlight:{string:Me},hover:{string:Me},inherit:{string:["from","to","both"],boolean:Ce},opacity:{number:Se},__type__:{object:Ie,string:Me}},dashes:{boolean:Ce,array:ke},font:{color:{string:Me},size:{number:Se},face:{string:Me},background:{string:Me},strokeWidth:{number:Se},strokeColor:{string:Me},align:{string:["horizontal","top","middle","bottom"]},vadjust:{number:Se},multi:{boolean:Ce,string:Me},bold:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},boldital:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},ital:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},mono:{color:{string:Me},size:{number:Se},face:{string:Me},mod:{string:Me},vadjust:{number:Se},__type__:{object:Ie,string:Me}},__type__:{object:Ie,string:Me}},hidden:{boolean:Ce},hoverWidth:{function:"function",number:Se},label:{string:Me,undefined:"undefined"},labelHighlightBold:{boolean:Ce},length:{number:Se,undefined:"undefined"},physics:{boolean:Ce},scaling:{min:{number:Se},max:{number:Se},label:{enabled:{boolean:Ce},min:{number:Se},max:{number:Se},maxVisible:{number:Se},drawThreshold:{number:Se},__type__:{object:Ie,boolean:Ce}},customScalingFunction:{function:"function"},__type__:{object:Ie}},selectionWidth:{function:"function",number:Se},selfReferenceSize:{number:Se},selfReference:{size:{number:Se},angle:{number:Se},renderBehindTheNode:{boolean:Ce},__type__:{object:Ie}},shadow:{enabled:{boolean:Ce},color:{string:Me},size:{number:Se},x:{number:Se},y:{number:Se},__type__:{object:Ie,boolean:Ce}},smooth:{enabled:{boolean:Ce},type:{string:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"]},roundness:{number:Se},forceDirection:{string:["horizontal","vertical","none"],boolean:Ce},__type__:{object:Ie,boolean:Ce}},title:{string:Me,undefined:"undefined"},width:{number:Se},widthConstraint:{maximum:{number:Se},__type__:{object:Ie,boolean:Ce,number:Se}},value:{number:Se,undefined:"undefined"},__type__:{object:Ie}},groups:{useDefaultGroups:{boolean:Ce},__any__:Te,__type__:{object:Ie}},interaction:{dragNodes:{boolean:Ce},dragView:{boolean:Ce},hideEdgesOnDrag:{boolean:Ce},hideEdgesOnZoom:{boolean:Ce},hideNodesOnDrag:{boolean:Ce},hover:{boolean:Ce},keyboard:{enabled:{boolean:Ce},speed:{x:{number:Se},y:{number:Se},zoom:{number:Se},__type__:{object:Ie}},bindToWindow:{boolean:Ce},autoFocus:{boolean:Ce},__type__:{object:Ie,boolean:Ce}},multiselect:{boolean:Ce},navigationButtons:{boolean:Ce},selectable:{boolean:Ce},selectConnectedEdges:{boolean:Ce},hoverConnectedEdges:{boolean:Ce},tooltipDelay:{number:Se},zoomView:{boolean:Ce},zoomSpeed:{number:Se},__type__:{object:Ie}},layout:{randomSeed:{undefined:"undefined",number:Se,string:Me},improvedLayout:{boolean:Ce},clusterThreshold:{number:Se},hierarchical:{enabled:{boolean:Ce},levelSeparation:{number:Se},nodeSpacing:{number:Se},treeSpacing:{number:Se},blockShifting:{boolean:Ce},edgeMinimization:{boolean:Ce},parentCentralization:{boolean:Ce},direction:{string:["UD","DU","LR","RL"]},sortMethod:{string:["hubsize","directed"]},shakeTowards:{string:["leaves","roots"]},__type__:{object:Ie,boolean:Ce}},__type__:{object:Ie}},manipulation:{enabled:{boolean:Ce},initiallyActive:{boolean:Ce},addNode:{boolean:Ce,function:"function"},addEdge:{boolean:Ce,function:"function"},editNode:{function:"function"},editEdge:{editWithoutDrag:{function:"function"},__type__:{object:Ie,boolean:Ce,function:"function"}},deleteNode:{boolean:Ce,function:"function"},deleteEdge:{boolean:Ce,function:"function"},controlNodeStyle:Te,__type__:{object:Ie,boolean:Ce}},nodes:Te,physics:{enabled:{boolean:Ce},barnesHut:{theta:{number:Se},gravitationalConstant:{number:Se},centralGravity:{number:Se},springLength:{number:Se},springConstant:{number:Se},damping:{number:Se},avoidOverlap:{number:Se},__type__:{object:Ie}},forceAtlas2Based:{theta:{number:Se},gravitationalConstant:{number:Se},centralGravity:{number:Se},springLength:{number:Se},springConstant:{number:Se},damping:{number:Se},avoidOverlap:{number:Se},__type__:{object:Ie}},repulsion:{centralGravity:{number:Se},springLength:{number:Se},springConstant:{number:Se},nodeDistance:{number:Se},damping:{number:Se},__type__:{object:Ie}},hierarchicalRepulsion:{centralGravity:{number:Se},springLength:{number:Se},springConstant:{number:Se},nodeDistance:{number:Se},damping:{number:Se},avoidOverlap:{number:Se},__type__:{object:Ie}},maxVelocity:{number:Se},minVelocity:{number:Se},solver:{string:["barnesHut","repulsion","hierarchicalRepulsion","forceAtlas2Based"]},stabilization:{enabled:{boolean:Ce},iterations:{number:Se},updateInterval:{number:Se},onlyDynamicEdges:{boolean:Ce},fit:{boolean:Ce},__type__:{object:Ie,boolean:Ce}},timestep:{number:Se},adaptiveTimestep:{boolean:Ce},wind:{x:{number:Se},y:{number:Se},__type__:{object:Ie}},__type__:{object:Ie,boolean:Ce}},autoResize:{boolean:Ce},clickToUse:{boolean:Ce},locale:{string:Me},locales:{__any__:{any:"any"},__type__:{object:Ie}},height:{string:Me},width:{string:Me},__type__:{object:Ie}},Be={nodes:{borderWidth:[1,0,10,1],borderWidthSelected:[2,0,10,1],color:{border:["color","#2B7CE9"],background:["color","#97C2FC"],highlight:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]},hover:{border:["color","#2B7CE9"],background:["color","#D2E5FF"]}},opacity:[0,0,1,.1],fixed:{x:!1,y:!1},font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[0,0,50,1],strokeColor:["color","#ffffff"]},hidden:!1,labelHighlightBold:!0,physics:!0,scaling:{min:[10,0,200,1],max:[30,0,200,1],label:{enabled:!1,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},shape:["ellipse","box","circle","database","diamond","dot","square","star","text","triangle","triangleDown","hexagon"],shapeProperties:{borderDashes:!1,borderRadius:[6,0,20,1],interpolation:!0,useImageSize:!1},size:[25,0,200,1]},edges:{arrows:{to:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},middle:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"},from:{enabled:!1,scaleFactor:[1,0,3,.05],type:"arrow"}},endPointOffset:{from:[0,-10,10,1],to:[0,-10,10,1]},arrowStrikethrough:!0,color:{color:["color","#848484"],highlight:["color","#848484"],hover:["color","#848484"],inherit:["from","to","both",!0,!1],opacity:[1,0,1,.05]},dashes:!1,font:{color:["color","#343434"],size:[14,0,100,1],face:["arial","verdana","tahoma"],background:["color","none"],strokeWidth:[2,0,50,1],strokeColor:["color","#ffffff"],align:["horizontal","top","middle","bottom"]},hidden:!1,hoverWidth:[1.5,0,5,.1],labelHighlightBold:!0,physics:!0,scaling:{min:[1,0,100,1],max:[15,0,100,1],label:{enabled:!0,min:[14,0,200,1],max:[30,0,200,1],maxVisible:[30,0,200,1],drawThreshold:[5,0,20,1]}},selectionWidth:[1.5,0,5,.1],selfReferenceSize:[20,0,200,1],selfReference:{size:[20,0,200,1],angle:[Math.PI/2,-6*Math.PI,6*Math.PI,Math.PI/8],renderBehindTheNode:!0},shadow:{enabled:!1,color:"rgba(0,0,0,0.5)",size:[10,0,20,1],x:[5,-30,30,1],y:[5,-30,30,1]},smooth:{enabled:!0,type:["dynamic","continuous","discrete","diagonalCross","straightCross","horizontal","vertical","curvedCW","curvedCCW","cubicBezier"],forceDirection:["horizontal","vertical","none"],roundness:[.5,0,1,.05]},width:[1,0,30,1]},layout:{hierarchical:{enabled:!1,levelSeparation:[150,20,500,5],nodeSpacing:[100,20,500,5],treeSpacing:[200,20,500,5],blockShifting:!0,edgeMinimization:!0,parentCentralization:!0,direction:["UD","DU","LR","RL"],sortMethod:["hubsize","directed"],shakeTowards:["leaves","roots"]}},interaction:{dragNodes:!0,dragView:!0,hideEdgesOnDrag:!1,hideEdgesOnZoom:!1,hideNodesOnDrag:!1,hover:!1,keyboard:{enabled:!1,speed:{x:[10,0,40,1],y:[10,0,40,1],zoom:[.02,0,.1,.005]},bindToWindow:!0,autoFocus:!0},multiselect:!1,navigationButtons:!1,selectable:!0,selectConnectedEdges:!0,hoverConnectedEdges:!0,tooltipDelay:[300,0,1e3,25],zoomView:!0,zoomSpeed:[1,.1,2,.1]},manipulation:{enabled:!1,initiallyActive:!1},physics:{enabled:!0,barnesHut:{theta:[.5,.1,1,.05],gravitationalConstant:[-2e3,-3e4,0,50],centralGravity:[.3,0,10,.05],springLength:[95,0,500,5],springConstant:[.04,0,1.2,.005],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},forceAtlas2Based:{theta:[.5,.1,1,.05],gravitationalConstant:[-50,-500,0,1],centralGravity:[.01,0,1,.005],springLength:[95,0,500,5],springConstant:[.08,0,1.2,.005],damping:[.4,0,1,.01],avoidOverlap:[0,0,1,.01]},repulsion:{centralGravity:[.2,0,10,.05],springLength:[200,0,500,5],springConstant:[.05,0,1.2,.005],nodeDistance:[100,0,500,5],damping:[.09,0,1,.01]},hierarchicalRepulsion:{centralGravity:[.2,0,10,.05],springLength:[100,0,500,5],springConstant:[.01,0,1.2,.005],nodeDistance:[120,0,500,5],damping:[.09,0,1,.01],avoidOverlap:[0,0,1,.01]},maxVelocity:[50,0,150,1],minVelocity:[.1,.01,.5,.01],solver:["barnesHut","forceAtlas2Based","repulsion","hierarchicalRepulsion"],timestep:[.5,.01,1,.01],wind:{x:[0,-10,10,.1],y:[0,-10,10,.1]}}},Ne=(t,e,i)=>!(!t.includes("physics")||!Be.physics.solver.includes(e)||i.physics.solver===e||"wind"===e);var Pe=Object.freeze({__proto__:null,allOptions:De,configuratorHideOption:Ne,configureOptions:Be});class Fe{constructor(){}getDistances(t,e,i){const o={},s=t.edges;for(let t=0;t<e.length;t++){const i={};o[e[t]]=i;for(let o=0;o<e.length;o++)i[e[o]]=t==o?0:1e9}for(let t=0;t<i.length;t++){const e=s[i[t]];!0===e.connected&&void 0!==o[e.fromId]&&void 0!==o[e.toId]&&(o[e.fromId][e.toId]=1,o[e.toId][e.fromId]=1)}const n=e.length;for(let t=0;t<n;t++){const i=e[t],s=o[i];for(let t=0;t<n-1;t++){const r=e[t],d=o[r];for(let a=t+1;a<n;a++){const t=e[a],n=o[t],h=Math.min(d[t],d[i]+s[t]);d[t]=h,n[r]=h}}}return o}}class Re{constructor(t,e,i){this.body=t,this.springLength=e,this.springConstant=i,this.distanceSolver=new Fe}setOptions(t){t&&(t.springLength&&(this.springLength=t.springLength),t.springConstant&&(this.springConstant=t.springConstant))}solve(t,e,i=!1){const o=this.distanceSolver.getDistances(this.body,t,e);this._createL_matrix(o),this._createK_matrix(o),this._createE_matrix();let s=0;const n=Math.max(1e3,Math.min(10*this.body.nodeIndices.length,6e3));let r=1e9,d=0,a=0,h=0,l=0,c=0;for(;r>.01&&s<n;)for(s+=1,[d,r,a,h]=this._getHighestEnergyNode(i),l=r,c=0;l>1&&c<5;)c+=1,this._moveNode(d,a,h),[l,a,h]=this._getEnergy(d)}_getHighestEnergyNode(t){const e=this.body.nodeIndices,i=this.body.nodes;let o=0,s=e[0],n=0,r=0;for(let d=0;d<e.length;d++){const a=e[d];if(!0!==i[a].predefinedPosition||!0===i[a].isCluster&&!0===t||!0!==i[a].options.fixed.x||!0!==i[a].options.fixed.y){const[t,e,i]=this._getEnergy(a);o<t&&(o=t,s=a,n=e,r=i)}}return[s,o,n,r]}_getEnergy(t){const[e,i]=this.E_sums[t];return[Math.sqrt(e**2+i**2),e,i]}_moveNode(t,e,i){const o=this.body.nodeIndices,s=this.body.nodes;let n=0,r=0,d=0;const a=s[t].x,h=s[t].y,l=this.K_matrix[t],c=this.L_matrix[t];for(let e=0;e<o.length;e++){const i=o[e];if(i!==t){const t=s[i].x,e=s[i].y,o=l[i],p=c[i],u=1/((a-t)**2+(h-e)**2)**1.5;n+=o*(1-p*(h-e)**2*u),r+=o*(p*(a-t)*(h-e)*u),d+=o*(1-p*(a-t)**2*u)}}const p=(e/n+i/r)/(r/n-d/r),u=-(r*p+e)/n;s[t].x+=u,s[t].y+=p,this._updateE_matrix(t)}_createL_matrix(t){const e=this.body.nodeIndices,i=this.springLength;this.L_matrix=[];for(let o=0;o<e.length;o++){this.L_matrix[e[o]]={};for(let s=0;s<e.length;s++)this.L_matrix[e[o]][e[s]]=i*t[e[o]][e[s]]}}_createK_matrix(t){const e=this.body.nodeIndices,i=this.springConstant;this.K_matrix=[];for(let o=0;o<e.length;o++){this.K_matrix[e[o]]={};for(let s=0;s<e.length;s++)this.K_matrix[e[o]][e[s]]=i*t[e[o]][e[s]]**-2}}_createE_matrix(){const t=this.body.nodeIndices,e=this.body.nodes;this.E_matrix={},this.E_sums={};for(let e=0;e<t.length;e++)this.E_matrix[t[e]]=[];for(let i=0;i<t.length;i++){const o=t[i],s=e[o].x,n=e[o].y;let r=0,d=0;for(let a=i;a<t.length;a++){const h=t[a];if(h!==o){const t=e[h].x,l=e[h].y,c=1/Math.sqrt((s-t)**2+(n-l)**2);this.E_matrix[o][a]=[this.K_matrix[o][h]*(s-t-this.L_matrix[o][h]*(s-t)*c),this.K_matrix[o][h]*(n-l-this.L_matrix[o][h]*(n-l)*c)],this.E_matrix[h][i]=this.E_matrix[o][a],r+=this.E_matrix[o][a][0],d+=this.E_matrix[o][a][1]}}this.E_sums[o]=[r,d]}}_updateE_matrix(t){const e=this.body.nodeIndices,i=this.body.nodes,o=this.E_matrix[t],s=this.K_matrix[t],n=this.L_matrix[t],r=i[t].x,d=i[t].y;let a=0,h=0;for(let l=0;l<e.length;l++){const c=e[l];if(c!==t){const t=o[l],e=t[0],p=t[1],u=i[c].x,g=i[c].y,b=1/Math.sqrt((r-u)**2+(d-g)**2),m=s[c]*(r-u-n[c]*(r-u)*b),f=s[c]*(d-g-n[c]*(d-g)*b);o[l]=[m,f],a+=m,h+=f;const y=this.E_sums[c];y[0]+=m-e,y[1]+=f-p}}this.E_sums[t]=[a,h]}}function Ae(t,e,i){if(!(this instanceof Ae))throw new SyntaxError("Constructor must be called with the new operator");this.options={},this.defaultOptions={locale:"en",locales:q,clickToUse:!1},Object.assign(this.options,this.defaultOptions),this.body={container:t,nodes:{},nodeIndices:[],edges:{},edgeIndices:[],emitter:{on:this.on.bind(this),off:this.off.bind(this),emit:this.emit.bind(this),once:this.once.bind(this)},eventListeners:{onTap:function(){},onTouch:function(){},onDoubleTap:function(){},onHold:function(){},onDragStart:function(){},onDrag:function(){},onDragEnd:function(){},onMouseWheel:function(){},onPinch:function(){},onMouseMove:function(){},onRelease:function(){},onContext:function(){}},data:{nodes:null,edges:null},functions:{createNode:function(){},createEdge:function(){},getPointer:function(){}},modules:{},view:{scale:1,translation:{x:0,y:0}},selectionBox:{show:!1,position:{start:{x:0,y:0},end:{x:0,y:0}}}},this.bindEventListeners(),this.images=new Y(()=>this.body.emitter.emit("_requestRedraw")),this.groups=new X,this.canvas=new he(this.body),this.selectionHandler=new me(this.body,this.canvas),this.interactionHandler=new pe(this.body,this.canvas,this.selectionHandler),this.view=new le(this.body,this.canvas),this.renderer=new re(this.body,this.canvas),this.physics=new ie(this.body),this.layoutEngine=new Ee(this.body),this.clustering=new ne(this.body),this.manipulation=new Oe(this.body,this.canvas,this.selectionHandler,this.interactionHandler),this.nodesHandler=new Ot(this.body,this.images,this.groups,this.layoutEngine),this.edgesHandler=new Xt(this.body,this.images,this.groups),this.body.modules.kamadaKawai=new Re(this.body,150,.05),this.body.modules.clustering=this.clustering,this.canvas._create(),this.setOptions(i),this.setData(e)}e(Ae.prototype),Ae.prototype.setOptions=function(t){if(null===t&&(t=void 0),void 0!==t){!0===i.Validator.validate(t,De)&&console.error("%cErrors have been found in the supplied options object.",i.VALIDATOR_PRINT_STYLE);const e=["locale","locales","clickToUse"];if(i.selectiveDeepExtend(e,this.options,t),void 0!==t.locale&&(t.locale=function(t,e){try{const[i,o]=e.split(/[-_ /]/,2),s=null!=i?i.toLowerCase():null,n=null!=o?o.toUpperCase():null;if(s&&n){const e=s+"-"+n;if(Object.prototype.hasOwnProperty.call(t,e))return e;console.warn(`Unknown variant ${n} of language ${s}.`)}if(s){const e=s;if(Object.prototype.hasOwnProperty.call(t,e))return e;console.warn(`Unknown language ${s}`)}return console.warn(`Unknown locale ${e}, falling back to English.`),"en"}catch(t){return console.error(t),console.warn(`Unexpected error while normalizing locale ${e}, falling back to English.`),"en"}}(t.locales||this.options.locales,t.locale)),t=this.layoutEngine.setOptions(t.layout,t),this.canvas.setOptions(t),this.groups.setOptions(t.groups),this.nodesHandler.setOptions(t.nodes),this.edgesHandler.setOptions(t.edges),this.physics.setOptions(t.physics),this.manipulation.setOptions(t.manipulation,t,this.options),this.interactionHandler.setOptions(t.interaction),this.renderer.setOptions(t.interaction),this.selectionHandler.setOptions(t.interaction),void 0!==t.groups&&this.body.emitter.emit("refreshNodes"),"configure"in t&&(this.configurator||(this.configurator=new i.Configurator(this,this.body.container,Be,this.canvas.pixelRatio,Ne)),this.configurator.setOptions(t.configure)),this.configurator&&!0===this.configurator.options.enabled){const t={nodes:{},edges:{},layout:{},interaction:{},manipulation:{},physics:{},global:{}};i.deepExtend(t.nodes,this.nodesHandler.options),i.deepExtend(t.edges,this.edgesHandler.options),i.deepExtend(t.layout,this.layoutEngine.options),i.deepExtend(t.interaction,this.selectionHandler.options),i.deepExtend(t.interaction,this.renderer.options),i.deepExtend(t.interaction,this.interactionHandler.options),i.deepExtend(t.manipulation,this.manipulation.options),i.deepExtend(t.physics,this.physics.options),i.deepExtend(t.global,this.canvas.options),i.deepExtend(t.global,this.options),this.configurator.setModuleOptions(t)}void 0!==t.clickToUse?!0===t.clickToUse?void 0===this.activator&&(this.activator=new i.Activator(this.canvas.frame),this.activator.on("change",()=>{this.body.emitter.emit("activate")})):(void 0!==this.activator&&(this.activator.destroy(),delete this.activator),this.body.emitter.emit("activate")):this.body.emitter.emit("activate"),this.canvas.setSize(),this.body.emitter.emit("startSimulation")}},Ae.prototype._updateVisibleIndices=function(){const t=this.body.nodes,e=this.body.edges;this.body.nodeIndices=[],this.body.edgeIndices=[];for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(this.clustering._isClusteredNode(e)||!1!==t[e].options.hidden||this.body.nodeIndices.push(t[e].id));for(const i in e)if(Object.prototype.hasOwnProperty.call(e,i)){const o=e[i],s=t[o.fromId],n=t[o.toId],r=void 0!==s&&void 0!==n;!this.clustering._isClusteredEdge(i)&&!1===o.options.hidden&&r&&!1===s.options.hidden&&!1===n.options.hidden&&this.body.edgeIndices.push(o.id)}},Ae.prototype.bindEventListeners=function(){this.body.emitter.on("_dataChanged",()=>{this.edgesHandler._updateState(),this.body.emitter.emit("_dataUpdated")}),this.body.emitter.on("_dataUpdated",()=>{this.clustering._updateState(),this._updateVisibleIndices(),this._updateValueRange(this.body.nodes),this._updateValueRange(this.body.edges),this.body.emitter.emit("startSimulation"),this.body.emitter.emit("_requestRedraw")})},Ae.prototype.setData=function(t){if(this.body.emitter.emit("resetPhysics"),this.body.emitter.emit("_resetData"),this.selectionHandler.unselectAll(),t&&t.dot&&(t.nodes||t.edges))throw new SyntaxError('Data must contain either parameter "dot" or  parameter pair "nodes" and "edges", but not both.');if(this.setOptions(t&&t.options),t&&t.dot){console.warn("The dot property has been deprecated. Please use the static convertDot method to convert DOT into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertDot(dotString);");const e=L(t.dot);return void this.setData(e)}if(t&&t.gephi){console.warn("The gephi property has been deprecated. Please use the static convertGephi method to convert gephi into vis.network format and use the normal data format with nodes and edges. This converter is used like this: var data = vis.network.convertGephi(gephiJson);");const e=W(t.gephi);return void this.setData(e)}this.nodesHandler.setData(t&&t.nodes,!0),this.edgesHandler.setData(t&&t.edges,!0),this.body.emitter.emit("_dataChanged"),this.body.emitter.emit("_dataLoaded"),this.body.emitter.emit("initPhysics")},Ae.prototype.destroy=function(){this.body.emitter.emit("destroy"),this.body.emitter.off(),this.off(),delete this.groups,delete this.canvas,delete this.selectionHandler,delete this.interactionHandler,delete this.view,delete this.renderer,delete this.physics,delete this.layoutEngine,delete this.clustering,delete this.manipulation,delete this.nodesHandler,delete this.edgesHandler,delete this.configurator,delete this.images;for(const t in this.body.nodes)Object.prototype.hasOwnProperty.call(this.body.nodes,t)&&delete this.body.nodes[t];for(const t in this.body.edges)Object.prototype.hasOwnProperty.call(this.body.edges,t)&&delete this.body.edges[t];i.recursiveDOMDelete(this.body.container)},Ae.prototype._updateValueRange=function(t){let e,i,o,s=0;for(e in t)if(Object.prototype.hasOwnProperty.call(t,e)){const n=t[e].getValue();void 0!==n&&(i=void 0===i?n:Math.min(n,i),o=void 0===o?n:Math.max(n,o),s+=n)}if(void 0!==i&&void 0!==o)for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&t[e].setValueRange(i,o,s)},Ae.prototype.isActive=function(){return!this.activator||this.activator.active},Ae.prototype.setSize=function(){return this.canvas.setSize.apply(this.canvas,arguments)},Ae.prototype.canvasToDOM=function(){return this.canvas.canvasToDOM.apply(this.canvas,arguments)},Ae.prototype.DOMtoCanvas=function(){return this.canvas.DOMtoCanvas.apply(this.canvas,arguments)},Ae.prototype.findNode=function(){return this.clustering.findNode.apply(this.clustering,arguments)},Ae.prototype.isCluster=function(){return this.clustering.isCluster.apply(this.clustering,arguments)},Ae.prototype.openCluster=function(){return this.clustering.openCluster.apply(this.clustering,arguments)},Ae.prototype.cluster=function(){return this.clustering.cluster.apply(this.clustering,arguments)},Ae.prototype.getNodesInCluster=function(){return this.clustering.getNodesInCluster.apply(this.clustering,arguments)},Ae.prototype.clusterByConnection=function(){return this.clustering.clusterByConnection.apply(this.clustering,arguments)},Ae.prototype.clusterByHubsize=function(){return this.clustering.clusterByHubsize.apply(this.clustering,arguments)},Ae.prototype.updateClusteredNode=function(){return this.clustering.updateClusteredNode.apply(this.clustering,arguments)},Ae.prototype.getClusteredEdges=function(){return this.clustering.getClusteredEdges.apply(this.clustering,arguments)},Ae.prototype.getBaseEdge=function(){return this.clustering.getBaseEdge.apply(this.clustering,arguments)},Ae.prototype.getBaseEdges=function(){return this.clustering.getBaseEdges.apply(this.clustering,arguments)},Ae.prototype.updateEdge=function(){return this.clustering.updateEdge.apply(this.clustering,arguments)},Ae.prototype.clusterOutliers=function(){return this.clustering.clusterOutliers.apply(this.clustering,arguments)},Ae.prototype.getSeed=function(){return this.layoutEngine.getSeed.apply(this.layoutEngine,arguments)},Ae.prototype.enableEditMode=function(){return this.manipulation.enableEditMode.apply(this.manipulation,arguments)},Ae.prototype.disableEditMode=function(){return this.manipulation.disableEditMode.apply(this.manipulation,arguments)},Ae.prototype.addNodeMode=function(){return this.manipulation.addNodeMode.apply(this.manipulation,arguments)},Ae.prototype.editNode=function(){return this.manipulation.editNode.apply(this.manipulation,arguments)},Ae.prototype.editNodeMode=function(){return console.warn("Deprecated: Please use editNode instead of editNodeMode."),this.manipulation.editNode.apply(this.manipulation,arguments)},Ae.prototype.addEdgeMode=function(){return this.manipulation.addEdgeMode.apply(this.manipulation,arguments)},Ae.prototype.editEdgeMode=function(){return this.manipulation.editEdgeMode.apply(this.manipulation,arguments)},Ae.prototype.deleteSelected=function(){return this.manipulation.deleteSelected.apply(this.manipulation,arguments)},Ae.prototype.getPositions=function(){return this.nodesHandler.getPositions.apply(this.nodesHandler,arguments)},Ae.prototype.getPosition=function(){return this.nodesHandler.getPosition.apply(this.nodesHandler,arguments)},Ae.prototype.storePositions=function(){return this.nodesHandler.storePositions.apply(this.nodesHandler,arguments)},Ae.prototype.moveNode=function(){return this.nodesHandler.moveNode.apply(this.nodesHandler,arguments)},Ae.prototype.getBoundingBox=function(){return this.nodesHandler.getBoundingBox.apply(this.nodesHandler,arguments)},Ae.prototype.getConnectedNodes=function(t){return void 0!==this.body.nodes[t]?this.nodesHandler.getConnectedNodes.apply(this.nodesHandler,arguments):this.edgesHandler.getConnectedNodes.apply(this.edgesHandler,arguments)},Ae.prototype.getConnectedEdges=function(){return this.nodesHandler.getConnectedEdges.apply(this.nodesHandler,arguments)},Ae.prototype.startSimulation=function(){return this.physics.startSimulation.apply(this.physics,arguments)},Ae.prototype.stopSimulation=function(){return this.physics.stopSimulation.apply(this.physics,arguments)},Ae.prototype.stabilize=function(){return this.physics.stabilize.apply(this.physics,arguments)},Ae.prototype.getSelection=function(){return this.selectionHandler.getSelection.apply(this.selectionHandler,arguments)},Ae.prototype.setSelection=function(){return this.selectionHandler.setSelection.apply(this.selectionHandler,arguments)},Ae.prototype.getSelectedNodes=function(){return this.selectionHandler.getSelectedNodeIds.apply(this.selectionHandler,arguments)},Ae.prototype.getSelectedEdges=function(){return this.selectionHandler.getSelectedEdgeIds.apply(this.selectionHandler,arguments)},Ae.prototype.getNodeAt=function(){const t=this.selectionHandler.getNodeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},Ae.prototype.getEdgeAt=function(){const t=this.selectionHandler.getEdgeAt.apply(this.selectionHandler,arguments);return void 0!==t&&void 0!==t.id?t.id:t},Ae.prototype.selectNodes=function(){return this.selectionHandler.selectNodes.apply(this.selectionHandler,arguments)},Ae.prototype.selectEdges=function(){return this.selectionHandler.selectEdges.apply(this.selectionHandler,arguments)},Ae.prototype.unselectAll=function(){this.selectionHandler.unselectAll.apply(this.selectionHandler,arguments),this.selectionHandler.commitWithoutEmitting.apply(this.selectionHandler),this.redraw()},Ae.prototype.redraw=function(){return this.renderer.redraw.apply(this.renderer,arguments)},Ae.prototype.getScale=function(){return this.view.getScale.apply(this.view,arguments)},Ae.prototype.getViewPosition=function(){return this.view.getViewPosition.apply(this.view,arguments)},Ae.prototype.fit=function(){return this.view.fit.apply(this.view,arguments)},Ae.prototype.moveTo=function(){return this.view.moveTo.apply(this.view,arguments)},Ae.prototype.focus=function(){return this.view.focus.apply(this.view,arguments)},Ae.prototype.releaseNode=function(){return this.view.releaseNode.apply(this.view,arguments)},Ae.prototype.getOptionsFromConfigurator=function(){let t={};return this.configurator&&(t=this.configurator.getOptions.apply(this.configurator)),t};const je=L;t.Network=Ae,t.NetworkImages=Y,t.networkDOTParser=H,t.networkGephiParser=V,t.networkOptions=Pe,t.parseDOTNetwork=je,t.parseGephiNetwork=W});
//# sourceMappingURL=vis-network.min.cjs.map
