{"version": 3, "file": "data-interface.d.ts", "sourceRoot": "", "sources": ["../src/data-interface.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAClD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAEnD,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAE7B,qBAAqB;AACrB,MAAM,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;AACjC,wBAAwB;AACxB,MAAM,MAAM,KAAK,GAAG,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1C;;;;GAIG;AACH,wBAAgB,IAAI,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,EAAE,CAEhD;AAED;;GAEG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG,QAAQ,GAAG,IAAI,GAC1D,CAAC,GACD,CAAC,SAAS,MAAM,GACd;KAAG,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CAAE,GAC1C,CAAC,CAAC;AAER;;;GAGG;AACH,MAAM,MAAM,QAAQ,CAAC,MAAM,SAAS,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7E;;;;GAIG;AACH,MAAM,MAAM,QAAQ,CAClB,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,IACnB,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC9B;;;;GAIG;AACH,MAAM,MAAM,UAAU,CACpB,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,IACnB,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAE5D;;;;;;;GAOG;AACH,wBAAgB,UAAU,CACxB,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,EACrB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAE5D;AAED,yBAAyB;AACzB,MAAM,WAAW,eAAe;IAC9B,0BAA0B;IAC1B,KAAK,EAAE,EAAE,EAAE,CAAC;CACb;AACD,4BAA4B;AAC5B,MAAM,WAAW,kBAAkB,CACjC,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM;IAErB,4BAA4B;IAC5B,KAAK,EAAE,EAAE,EAAE,CAAC;IACZ,6CAA6C;IAC7C,OAAO,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;IAClC;;;OAGG;IACH,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;CAChC;AACD,4BAA4B;AAC5B,MAAM,WAAW,kBAAkB,CACjC,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM;IAErB,4BAA4B;IAC5B,KAAK,EAAE,EAAE,EAAE,CAAC;IACZ,+CAA+C;IAC/C,OAAO,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;CACnC;AAED;;;;GAIG;AACH,MAAM,WAAW,aAAa,CAC5B,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM;IAErB,GAAG,EAAE,eAAe,CAAC;IACrB,MAAM,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACzC,MAAM,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;CAC1C;AACD;;;;GAIG;AACH,MAAM,WAAW,oBAAoB,CACnC,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,CACrB,SAAQ,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;IACnC,GAAG,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;CAC3C;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc,CAC7B,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM;IAErB;;;;OAIG;IACH,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;IAC9E;;;;OAIG;IACH,MAAM,CACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,EAChD,QAAQ,CAAC,EAAE,EAAE,GAAG,IAAI,GACnB,IAAI,CAAC;IACR;;;;OAIG;IACH,MAAM,CACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,EAChD,QAAQ,CAAC,EAAE,EAAE,GAAG,IAAI,GACnB,IAAI,CAAC;CACT;AACD;;;;GAIG;AACH,MAAM,WAAW,qBAAqB,CACpC,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,CACrB,SAAQ,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC;IACpC;;;;OAIG;IACH,GAAG,CAAC,CAAC,SAAS,MAAM,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,EAC9C,IAAI,EAAE,CAAC,EACP,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,EACvC,QAAQ,CAAC,EAAE,EAAE,GAAG,IAAI,GACnB,IAAI,CAAC;CACT;AAED,6BAA6B;AAC7B,MAAM,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACvD,uDAAuD;AACvD,MAAM,MAAM,gBAAgB,GAAG,MAAM,oBAAoB,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAErE;;;;;GAKG;AACH,MAAM,MAAM,kBAAkB,CAAC,IAAI,IAC/B,MAAM,IAAI,GACV,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC;AAEnC;;;GAGG;AACH,MAAM,WAAW,2BAA2B,CAAC,IAAI;IAC/C;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3C,2SAA2S;IAC3S,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC;IACjC,+DAA+D;IAC/D,KAAK,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;CAClC;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,4BAA4B,CAAC,IAAI,CAChD,SAAQ,2BAA2B,CAAC,IAAI,CAAC;IACzC,8HAA8H;IAC9H,UAAU,CAAC,EAAE,SAAS,GAAG,OAAO,CAAC;CAClC;AACD;;;;;GAKG;AACH,MAAM,WAAW,6BAA6B,CAAC,IAAI,CACjD,SAAQ,2BAA2B,CAAC,IAAI,CAAC;IACzC,2DAA2D;IAC3D,UAAU,EAAE,QAAQ,CAAC;CACtB;AACD;;;GAGG;AACH,MAAM,MAAM,uBAAuB,CAAC,IAAI,IACpC,4BAA4B,CAAC,IAAI,CAAC,GAClC,6BAA6B,CAAC,IAAI,CAAC,CAAC;AAExC;;;GAGG;AACH,MAAM,WAAW,0BAA0B,CAAC,IAAI;IAC9C,2SAA2S;IAC3S,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC;IACjC,+DAA+D;IAC/D,KAAK,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;CAClC;AAED;;;GAGG;AACH,MAAM,WAAW,2BAA2B,CAAC,IAAI;IAC/C,mSAAmS;IACnS,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3C,2SAA2S;IAC3S,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC;IACjC,+DAA+D;IAC/D,KAAK,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;CAClC;AAED;;;;GAIG;AACH,MAAM,WAAW,uBAAuB,CAAC,QAAQ,EAAE,MAAM;IACvD,mSAAmS;IACnS,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3C,2SAA2S;IAC3S,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAC;IACrC,+DAA+D;IAC/D,KAAK,CAAC,EAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;CACpC;AAED;;;;GAIG;AACH,MAAM,WAAW,aAAa,CAC5B,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,GAAG,IAAI;IAE5B,2BAA2B;IAC3B,MAAM,EAAE,MAAM,CAAC;IAEf,8BAA8B;IAC9B,MAAM,EAAE,MAAM,CAAC;IAEf;;;;;OAKG;IACH,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACzE;;;;;OAKG;IACH,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;IAC7E;;;;;OAKG;IACH,EAAE,CACA,KAAK,EAAE,QAAQ,EACf,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GACtD,IAAI,CAAC;IACR;;;;;OAKG;IACH,EAAE,CACA,KAAK,EAAE,QAAQ,EACf,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GACtD,IAAI,CAAC;IAER;;;;OAIG;IACH,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IAC1E;;;;OAIG;IACH,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;IAC9E;;;;OAIG;IACH,GAAG,CACD,KAAK,EAAE,QAAQ,EACf,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GACtD,IAAI,CAAC;IACR;;;;OAIG;IACH,GAAG,CACD,KAAK,EAAE,QAAQ,EACf,QAAQ,EAAE,qBAAqB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GACtD,IAAI,CAAC;IAER;;;OAGG;IACH,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;IAChC;;;;OAIG;IACH,GAAG,CAAC,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;IAC3E;;;;OAIG;IACH,GAAG,CACD,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACtC;;;;OAIG;IACH,GAAG,CACD,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACjE;;;;OAIG;IACH,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C;;;;;OAKG;IACH,GAAG,CACD,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAC1C,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjC;;;;;OAKG;IACH,GAAG,CACD,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACtC;;;;;OAKG;IACH,GAAG,CACD,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACtE;;;;OAIG;IACH,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;IACzC;;;;;OAKG;IACH,GAAG,CACD,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAC1C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;IAC5B;;;;;OAKG;IACH,GAAG,CACD,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACtC;;;;;;OAMG;IACH,GAAG,CACD,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IACjE;;;;;OAKG;IACH,GAAG,CACD,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EACd,OAAO,CAAC,EAAE,uBAAuB,CAAC,IAAI,CAAC,GAErC,IAAI,GACJ,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GACtB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GACxB,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAEvC;;;;OAIG;IACH,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAEpC;;;;;;OAMG;IACH,MAAM,CAAC,OAAO,CAAC,EAAE,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;IAEzD;;;;;;OAMG;IACH,OAAO,CACL,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,IAAI,EACtC,OAAO,CAAC,EAAE,2BAA2B,CAAC,IAAI,CAAC,GAC1C,IAAI,CAAC;IAER;;;;;;;OAOG;IACH,GAAG,CAAC,CAAC,EACH,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EACnC,OAAO,CAAC,EAAE,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,GACzC,CAAC,EAAE,CAAC;IAEP;;;;OAIG;IACH,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;CAC9C"}