{"version": 3, "file": "data-view.d.ts", "sourceRoot": "", "sources": ["../src/data-view.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,KAAK,aAAa,EAClB,KAAK,2BAA2B,EAChC,KAAK,0BAA0B,EAC/B,KAAK,uBAAuB,EAC5B,KAAK,4BAA4B,EACjC,KAAK,6BAA6B,EAClC,KAAK,uBAAuB,EAI5B,KAAK,QAAQ,EACb,KAAK,EAAE,EACP,KAAK,QAAQ,EAId,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C;;;;GAIG;AACH,MAAM,WAAW,eAAe,CAAC,IAAI,EAAE,MAAM,SAAS,MAAM;IAC1D;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,2SAA2S;IAC3S,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,OAAO,CAAC;CAClC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2CG;AACH,qBAAa,QAAQ,CACjB,IAAI,SAAS,QAAQ,CAAC,MAAM,CAAC,EAC7B,MAAM,SAAS,MAAM,GAAG,IAAI,CAE9B,SAAQ,WAAW,CAAC,IAAI,EAAE,MAAM,CAChC,YAAW,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;IAEtC,kBAAkB;IACX,MAAM,SAAK;IAClB,kBAAkB;IAClB,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED,OAAO,CAAC,QAAQ,CAAC,SAAS,CAA2C;IACrE,OAAO,CAAC,KAAK,CAA+B;IAC5C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAsB;IAC3C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAgC;IAEzD;;;;OAIG;gBAED,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,EACjC,OAAO,CAAC,EAAE,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC;IAczC;;;;;;;;OAQG;IACI,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI;IAqCvD;;;OAGG;IACI,OAAO,IAAI,IAAI;IAgDtB,kBAAkB;IACX,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;IACtC,kBAAkB;IACX,GAAG,CACR,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAC1C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;IAC3B,kBAAkB;IACX,GAAG,CACR,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,kBAAkB;IACX,GAAG,CACR,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAChE,kBAAkB;IACX,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACjD,kBAAkB;IACX,GAAG,CACR,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAC1C,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IAChC,kBAAkB;IACX,GAAG,CACR,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,kBAAkB;IACX,GAAG,CACR,EAAE,EAAE,EAAE,EACN,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrE,kBAAkB;IACX,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;IAC/C,kBAAkB;IACX,GAAG,CACR,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,4BAA4B,CAAC,IAAI,CAAC,GAC1C,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;IAC3B,kBAAkB;IACX,GAAG,CACR,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,6BAA6B,CAAC,IAAI,CAAC,GAC3C,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACrC,kBAAkB;IACX,GAAG,CACR,GAAG,EAAE,EAAE,EAAE,EACT,OAAO,EAAE,uBAAuB,CAAC,IAAI,CAAC,GACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAChE,kBAAkB;IACX,GAAG,CACR,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE,EACd,OAAO,CAAC,EAAE,uBAAuB,CAAC,IAAI,CAAC,GAErC,IAAI,GACJ,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,GACtB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,GACxB,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAgDtC,kBAAkB;IACX,MAAM,CAAC,OAAO,CAAC,EAAE,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE;IA2B/D,kBAAkB;IACX,OAAO,CACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,IAAI,EACtC,OAAO,CAAC,EAAE,2BAA2B,CAAC,IAAI,CAAC,GAC1C,IAAI;IAyBP,kBAAkB;IACX,GAAG,CAAC,CAAC,EACV,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EACnC,OAAO,CAAC,EAAE,uBAAuB,CAAC,IAAI,EAAE,CAAC,CAAC,GACzC,CAAC,EAAE;IA6BN,kBAAkB;IACX,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;IAI1C,kBAAkB;IACX,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;IAQnD;;;;;;OAMG;IACI,OAAO,IAAI,IAAI;IAqBtB;;;;;OAKG;IACH,OAAO,CAAC,QAAQ;CAsGjB"}