/**
 * vis-data
 * http://visjs.org/
 *
 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
 *
 * @version 8.0.1
 * @date    2025-07-13T02:52:37.151Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
import{pureDeepObjectAssign as t}from"vis-util/esnext/esm/vis-util.js";export{DELETE}from"vis-util/esnext/esm/vis-util.js";import{v4 as e}from"uuid";function s(t){return new i(t)}class r{_source;_transformers;_target;_listeners={add:this._add.bind(this),remove:this._remove.bind(this),update:this._update.bind(this)};constructor(t,e,s){this._source=t,this._transformers=e,this._target=s}all(){return this._target.update(this._transformItems(this._source.get())),this}start(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}stop(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}_transformItems(t){return this._transformers.reduce((t,e)=>e(t),t)}_add(t,e){null!=e&&this._target.add(this._transformItems(this._source.get(e.items)))}_update(t,e){null!=e&&this._target.update(this._transformItems(this._source.get(e.items)))}_remove(t,e){null!=e&&this._target.remove(this._transformItems(e.oldData))}}class i{_source;_transformers=[];constructor(t){this._source=t}filter(t){return this._transformers.push(e=>e.filter(t)),this}map(t){return this._transformers.push(e=>e.map(t)),this}flatMap(t){return this._transformers.push(e=>e.flatMap(t)),this}to(t){return new r(this._source,this._transformers,t)}}function o(t){return"string"==typeof t||"number"==typeof t}class n{delay;max;_queue=[];_timeout=null;_extended=null;constructor(t){this.delay=null,this.max=1/0,this.setOptions(t)}setOptions(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}static extend(t,e){const s=new n(e);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=()=>{s.flush()};const r=[{name:"flush",original:void 0}];if(e&&e.replace)for(let i=0;i<e.replace.length;i++){const o=e.replace[i];r.push({name:o,original:t[o]}),s.replace(t,o)}return s._extended={object:t,methods:r},s}destroy(){if(this.flush(),this._extended){const t=this._extended.object,e=this._extended.methods;for(let s=0;s<e.length;s++){const r=e[s];r.original?t[r.name]=r.original:delete t[r.name]}this._extended=null}}replace(t,e){const s=this,r=t[e];if(!r)throw new Error("Method "+e+" undefined");t[e]=function(...t){s.queue({args:t,fn:r,context:this})}}queue(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}_flushIfNeeded(){this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=setTimeout(()=>{this.flush()},this.delay))}flush(){this._queue.splice(0).forEach(t=>{t.fn.apply(t.context||t.fn,t.args||[])})}}class a{_subscribers={"*":[],add:[],remove:[],update:[]};_trigger(t,e,s){if("*"===t)throw new Error("Cannot trigger event *");[...this._subscribers[t],...this._subscribers["*"]].forEach(r=>{r(t,e,null!=s?s:null)})}on(t,e){"function"==typeof e&&this._subscribers[t].push(e)}off(t,e){this._subscribers[t]=this._subscribers[t].filter(t=>t!==e)}subscribe=a.prototype.on;unsubscribe=a.prototype.off;get testLeakSubscribers(){return this._subscribers}}class h{_pairs;constructor(t){this._pairs=t}*[Symbol.iterator](){for(const[t,e]of this._pairs)yield[t,e]}*entries(){for(const[t,e]of this._pairs)yield[t,e]}*keys(){for(const[t]of this._pairs)yield t}*values(){for(const[,t]of this._pairs)yield t}toIdArray(){return[...this._pairs].map(t=>t[0])}toItemArray(){return[...this._pairs].map(t=>t[1])}toEntryArray(){return[...this._pairs]}toObjectMap(){const t=Object.create(null);for(const[e,s]of this._pairs)t[e]=s;return t}toMap(){return new Map(this._pairs)}toIdSet(){return new Set(this.toIdArray())}toItemSet(){return new Set(this.toItemArray())}cache(){return new h([...this._pairs])}distinct(t){const e=new Set;for(const[s,r]of this._pairs)e.add(t(r,s));return e}filter(t){const e=this._pairs;return new h({*[Symbol.iterator](){for(const[s,r]of e)t(r,s)&&(yield[s,r])}})}forEach(t){for(const[e,s]of this._pairs)t(s,e)}map(t){const e=this._pairs;return new h({*[Symbol.iterator](){for(const[s,r]of e)yield[s,t(r,s)]}})}max(t){const e=this._pairs[Symbol.iterator]();let s=e.next();if(s.done)return null;let r=s.value[1],i=t(s.value[1],s.value[0]);for(;!(s=e.next()).done;){const[e,o]=s.value,n=t(o,e);n>i&&(i=n,r=o)}return r}min(t){const e=this._pairs[Symbol.iterator]();let s=e.next();if(s.done)return null;let r=s.value[1],i=t(s.value[1],s.value[0]);for(;!(s=e.next()).done;){const[e,o]=s.value,n=t(o,e);n<i&&(i=n,r=o)}return r}reduce(t,e){for(const[s,r]of this._pairs)e=t(e,r,s);return e}sort(t){return new h({[Symbol.iterator]:()=>[...this._pairs].sort(([e,s],[r,i])=>t(s,i,e,r))[Symbol.iterator]()})}}class l extends a{flush;length;get idProp(){return this._idProp}_options;_data;_idProp;_queue=null;constructor(t,e){super(),t&&!Array.isArray(t)&&(e=t,t=[]),this._options=e||{},this._data=new Map,this.length=0,this._idProp=this._options.fieldId||"id",t&&t.length&&this.add(t),this.setOptions(e)}setOptions(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=n.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}add(t,e){const s=[];let r;if(Array.isArray(t)){if(t.map(t=>t[this._idProp]).some(t=>this._data.has(t)))throw new Error("A duplicate id was found in the parameter array.");for(let e=0,i=t.length;e<i;e++)r=this._addItem(t[e]),s.push(r)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");r=this._addItem(t),s.push(r)}return s.length&&this._trigger("add",{items:s},e),s}update(t,e){const s=[],r=[],i=[],o=[],n=this._idProp,a=t=>{const e=t[n];if(null!=e&&this._data.has(e)){const s=t,n=Object.assign({},this._data.get(e)),a=this._updateItem(s);r.push(a),o.push(s),i.push(n)}else{const e=this._addItem(t);s.push(e)}};if(Array.isArray(t))for(let e=0,s=t.length;e<s;e++)t[e]&&"object"==typeof t[e]?a(t[e]):console.warn("Ignoring input item, which is not an object at index "+e);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");a(t)}if(s.length&&this._trigger("add",{items:s},e),r.length){const t={items:r,oldData:i,data:o};this._trigger("update",t,e)}return s.concat(r)}updateOnly(e,s){Array.isArray(e)||(e=[e]);const r=e.map(t=>{const e=this._data.get(t[this._idProp]);if(null==e)throw new Error("Updating non-existent items is not allowed.");return{oldData:e,update:t}}).map(({oldData:e,update:s})=>{const r=e[this._idProp],i=t(e,s);return this._data.set(r,i),{id:r,oldData:e,updatedData:i}});if(r.length){const t={items:r.map(t=>t.id),oldData:r.map(t=>t.oldData),data:r.map(t=>t.updatedData)};return this._trigger("update",t,s),t.items}return[]}get(t,e){let s,r,i;o(t)?(s=t,i=e):Array.isArray(t)?(r=t,i=e):i=t;const n=i&&"Object"===i.returnType?"Object":"Array",a=i&&i.filter,h=[];let l,u,d;if(null!=s)l=this._data.get(s),l&&a&&!a(l)&&(l=void 0);else if(null!=r)for(let t=0,e=r.length;t<e;t++)l=this._data.get(r[t]),null==l||a&&!a(l)||h.push(l);else{u=[...this._data.keys()];for(let t=0,e=u.length;t<e;t++)d=u[t],l=this._data.get(d),null==l||a&&!a(l)||h.push(l)}if(i&&i.order&&null==s&&this._sort(h,i.order),i&&i.fields){const t=i.fields;if(null!=s&&null!=l)l=this._filterFields(l,t);else for(let e=0,s=h.length;e<s;e++)h[e]=this._filterFields(h[e],t)}if("Object"==n){const t={};for(let e=0,s=h.length;e<s;e++){const s=h[e];t[s[this._idProp]]=s}return t}return null!=s?l??null:h}getIds(t){const e=this._data,s=t&&t.filter,r=t&&t.order,i=[...e.keys()],o=[];if(s)if(r){const t=[];for(let e=0,r=i.length;e<r;e++){const r=i[e],o=this._data.get(r);null!=o&&s(o)&&t.push(o)}this._sort(t,r);for(let e=0,s=t.length;e<s;e++)o.push(t[e][this._idProp])}else for(let t=0,e=i.length;t<e;t++){const e=i[t],r=this._data.get(e);null!=r&&s(r)&&o.push(r[this._idProp])}else if(r){const t=[];for(let s=0,r=i.length;s<r;s++){const r=i[s];t.push(e.get(r))}this._sort(t,r);for(let e=0,s=t.length;e<s;e++)o.push(t[e][this._idProp])}else for(let t=0,s=i.length;t<s;t++){const s=i[t],r=e.get(s);null!=r&&o.push(r[this._idProp])}return o}getDataSet(){return this}forEach(t,e){const s=e&&e.filter,r=[...this._data.keys()];if(e&&e.order){const s=this.get(e);for(let e=0,r=s.length;e<r;e++){const r=s[e];t(r,r[this._idProp])}}else for(let e=0,i=r.length;e<i;e++){const i=r[e],o=this._data.get(i);null==o||s&&!s(o)||t(o,i)}}map(t,e){const s=e&&e.filter,r=[],i=[...this._data.keys()];for(let e=0,o=i.length;e<o;e++){const o=i[e],n=this._data.get(o);null==n||s&&!s(n)||r.push(t(n,o))}return e&&e.order&&this._sort(r,e.order),r}_filterFields(t,e){return t?(Array.isArray(e)?e:Object.keys(e)).reduce((e,s)=>(e[s]=t[s],e),{}):t}_sort(t,e){if("string"==typeof e){const s=e;t.sort((t,e)=>{const r=t[s],i=e[s];return r>i?1:r<i?-1:0})}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");t.sort(e)}}remove(t,e){const s=[],r=[],i=Array.isArray(t)?t:[t];for(let t=0,e=i.length;t<e;t++){const e=this._remove(i[t]);if(e){const t=e[this._idProp];null!=t&&(s.push(t),r.push(e))}}return s.length&&this._trigger("remove",{items:s,oldData:r},e),s}_remove(t){let e;if(o(t)?e=t:t&&"object"==typeof t&&(e=t[this._idProp]),null!=e&&this._data.has(e)){const t=this._data.get(e)||null;return this._data.delete(e),--this.length,t}return null}clear(t){const e=[...this._data.keys()],s=[];for(let t=0,r=e.length;t<r;t++)s.push(this._data.get(e[t]));return this._data.clear(),this.length=0,this._trigger("remove",{items:e,oldData:s},t),e}max(t){let e=null,s=null;for(const r of this._data.values()){const i=r[t];"number"==typeof i&&(null==s||i>s)&&(e=r,s=i)}return e||null}min(t){let e=null,s=null;for(const r of this._data.values()){const i=r[t];"number"==typeof i&&(null==s||i<s)&&(e=r,s=i)}return e||null}distinct(t){const e=this._data,s=[...e.keys()],r=[];let i=0;for(let o=0,n=s.length;o<n;o++){const n=s[o],a=e.get(n)[t];let h=!1;for(let t=0;t<i;t++)if(r[t]==a){h=!0;break}h||void 0===a||(r[i]=a,i++)}return r}_addItem(t){const s=function(t,s){return null==t[s]&&(t[s]=e()),t}(t,this._idProp),r=s[this._idProp];if(this._data.has(r))throw new Error("Cannot add item: item with id "+r+" already exists");return this._data.set(r,s),++this.length,r}_updateItem(t){const e=t[this._idProp];if(null==e)throw new Error("Cannot update item: item has no id (item: "+JSON.stringify(t)+")");const s=this._data.get(e);if(!s)throw new Error("Cannot update item: no item with id "+e+" found");return this._data.set(e,{...s,...t}),e}stream(t){if(t){const e=this._data;return new h({*[Symbol.iterator](){for(const s of t){const t=e.get(s);null!=t&&(yield[s,t])}}})}return new h({[Symbol.iterator]:this._data.entries.bind(this._data)})}get testLeakData(){return this._data}get testLeakIdProp(){return this._idProp}get testLeakOptions(){return this._options}get testLeakQueue(){return this._queue}set testLeakQueue(t){this._queue=t}}class u extends a{length=0;get idProp(){return this.getDataSet().idProp}_listener;_data;_ids=new Set;_options;constructor(t,e){super(),this._options=e||{},this._listener=this._onEvent.bind(this),this.setData(t)}setData(t){if(this._data){this._data.off&&this._data.off("*",this._listener);const t=this._data.getIds({filter:this._options.filter}),e=this._data.get(t);this._ids.clear(),this.length=0,this._trigger("remove",{items:t,oldData:e})}if(null!=t){this._data=t;const e=this._data.getIds({filter:this._options.filter});for(let t=0,s=e.length;t<s;t++){const s=e[t];this._ids.add(s)}this.length=e.length,this._trigger("add",{items:e})}else this._data=new l;this._data.on&&this._data.on("*",this._listener)}refresh(){const t=this._data.getIds({filter:this._options.filter}),e=[...this._ids],s={},r=[],i=[],o=[];for(let e=0,i=t.length;e<i;e++){const i=t[e];s[i]=!0,this._ids.has(i)||(r.push(i),this._ids.add(i))}for(let t=0,r=e.length;t<r;t++){const r=e[t],n=this._data.get(r);null==n?console.error("If you see this, report it please."):s[r]||(i.push(r),o.push(n),this._ids.delete(r))}this.length+=r.length-i.length,r.length&&this._trigger("add",{items:r}),i.length&&this._trigger("remove",{items:i,oldData:o})}get(t,e){if(null==this._data)return null;let s,r=null;o(t)||Array.isArray(t)?(r=t,s=e):s=t;const i=Object.assign({},this._options,s),n=this._options.filter,a=s&&s.filter;return n&&a&&(i.filter=t=>n(t)&&a(t)),null==r?this._data.get(i):this._data.get(r,i)}getIds(t){if(this._data.length){const e=this._options.filter,s=null!=t?t.filter:null;let r;return r=s?e?t=>e(t)&&s(t):s:e,this._data.getIds({filter:r,order:t&&t.order})}return[]}forEach(t,e){if(this._data){const s=this._options.filter,r=e&&e.filter;let i;i=r?s?function(t){return s(t)&&r(t)}:r:s,this._data.forEach(t,{filter:i,order:e&&e.order})}}map(t,e){if(this._data){const s=this._options.filter,r=e&&e.filter;let i;return i=r?s?t=>s(t)&&r(t):r:s,this._data.map(t,{filter:i,order:e&&e.order})}return[]}getDataSet(){return this._data.getDataSet()}stream(t){return this._data.stream(t||{[Symbol.iterator]:this._ids.keys.bind(this._ids)})}dispose(){this._data?.off&&this._data.off("*",this._listener);const t="This data view has already been disposed of.",e={get:()=>{throw new Error(t)},set:()=>{throw new Error(t)},configurable:!1};for(const t of Reflect.ownKeys(u.prototype))Object.defineProperty(this,t,e)}_onEvent(t,e,s){if(!e||!e.items||!this._data)return;const r=e.items,i=[],o=[],n=[],a=[],h=[],l=[];switch(t){case"add":for(let t=0,e=r.length;t<e;t++){const e=r[t];this.get(e)&&(this._ids.add(e),i.push(e))}break;case"update":for(let t=0,s=r.length;t<s;t++){const s=r[t];this.get(s)?this._ids.has(s)?(o.push(s),h.push(e.data[t]),a.push(e.oldData[t])):(this._ids.add(s),i.push(s)):this._ids.has(s)&&(this._ids.delete(s),n.push(s),l.push(e.oldData[t]))}break;case"remove":for(let t=0,s=r.length;t<s;t++){const s=r[t];this._ids.has(s)&&(this._ids.delete(s),n.push(s),l.push(e.oldData[t]))}}this.length+=i.length-n.length,i.length&&this._trigger("add",{items:i},s),o.length&&this._trigger("update",{items:o,oldData:a,data:h},s),n.length&&this._trigger("remove",{items:n,oldData:l},s)}}function d(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.add&&"function"==typeof e.clear&&"function"==typeof e.distinct&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.max&&"function"==typeof e.min&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.remove&&"function"==typeof e.setOptions&&"function"==typeof e.stream&&"function"==typeof e.update&&"function"==typeof e.updateOnly}function f(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.stream&&d(t,e.getDataSet())}console.warn("You're running a development build.");export{l as DataSet,h as DataStream,u as DataView,n as Queue,s as createNewDataPipeFrom,d as isDataSetLike,f as isDataViewLike};
//# sourceMappingURL=vis-data.min.mjs.map
