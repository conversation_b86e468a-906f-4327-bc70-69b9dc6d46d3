/**
 * vis-data
 * http://visjs.org/
 *
 * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
 *
 * @version 8.0.1
 * @date    2025-07-13T02:52:37.151Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("vis-util/esnext/umd/vis-util.js"),require("uuid")):"function"==typeof define&&define.amd?define(["exports","vis-util/esnext/umd/vis-util.js","uuid"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vis=t.vis||{},t.vis,t.uuidv4)}(this,function(t,e,s){class i{_source;_transformers;_target;_listeners={add:this._add.bind(this),remove:this._remove.bind(this),update:this._update.bind(this)};constructor(t,e,s){this._source=t,this._transformers=e,this._target=s}all(){return this._target.update(this._transformItems(this._source.get())),this}start(){return this._source.on("add",this._listeners.add),this._source.on("remove",this._listeners.remove),this._source.on("update",this._listeners.update),this}stop(){return this._source.off("add",this._listeners.add),this._source.off("remove",this._listeners.remove),this._source.off("update",this._listeners.update),this}_transformItems(t){return this._transformers.reduce((t,e)=>e(t),t)}_add(t,e){null!=e&&this._target.add(this._transformItems(this._source.get(e.items)))}_update(t,e){null!=e&&this._target.update(this._transformItems(this._source.get(e.items)))}_remove(t,e){null!=e&&this._target.remove(this._transformItems(e.oldData))}}class r{_source;_transformers=[];constructor(t){this._source=t}filter(t){return this._transformers.push(e=>e.filter(t)),this}map(t){return this._transformers.push(e=>e.map(t)),this}flatMap(t){return this._transformers.push(e=>e.flatMap(t)),this}to(t){return new i(this._source,this._transformers,t)}}function n(t){return"string"==typeof t||"number"==typeof t}class o{delay;max;_queue=[];_timeout=null;_extended=null;constructor(t){this.delay=null,this.max=1/0,this.setOptions(t)}setOptions(t){t&&void 0!==t.delay&&(this.delay=t.delay),t&&void 0!==t.max&&(this.max=t.max),this._flushIfNeeded()}static extend(t,e){const s=new o(e);if(void 0!==t.flush)throw new Error("Target object already has a property flush");t.flush=()=>{s.flush()};const i=[{name:"flush",original:void 0}];if(e&&e.replace)for(let r=0;r<e.replace.length;r++){const n=e.replace[r];i.push({name:n,original:t[n]}),s.replace(t,n)}return s._extended={object:t,methods:i},s}destroy(){if(this.flush(),this._extended){const t=this._extended.object,e=this._extended.methods;for(let s=0;s<e.length;s++){const i=e[s];i.original?t[i.name]=i.original:delete t[i.name]}this._extended=null}}replace(t,e){const s=this,i=t[e];if(!i)throw new Error("Method "+e+" undefined");t[e]=function(...t){s.queue({args:t,fn:i,context:this})}}queue(t){"function"==typeof t?this._queue.push({fn:t}):this._queue.push(t),this._flushIfNeeded()}_flushIfNeeded(){this._queue.length>this.max&&this.flush(),null!=this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this.queue.length>0&&"number"==typeof this.delay&&(this._timeout=setTimeout(()=>{this.flush()},this.delay))}flush(){this._queue.splice(0).forEach(t=>{t.fn.apply(t.context||t.fn,t.args||[])})}}class a{_subscribers={"*":[],add:[],remove:[],update:[]};_trigger(t,e,s){if("*"===t)throw new Error("Cannot trigger event *");[...this._subscribers[t],...this._subscribers["*"]].forEach(i=>{i(t,e,null!=s?s:null)})}on(t,e){"function"==typeof e&&this._subscribers[t].push(e)}off(t,e){this._subscribers[t]=this._subscribers[t].filter(t=>t!==e)}subscribe=a.prototype.on;unsubscribe=a.prototype.off;get testLeakSubscribers(){return this._subscribers}}class h{_pairs;constructor(t){this._pairs=t}*[Symbol.iterator](){for(const[t,e]of this._pairs)yield[t,e]}*entries(){for(const[t,e]of this._pairs)yield[t,e]}*keys(){for(const[t]of this._pairs)yield t}*values(){for(const[,t]of this._pairs)yield t}toIdArray(){return[...this._pairs].map(t=>t[0])}toItemArray(){return[...this._pairs].map(t=>t[1])}toEntryArray(){return[...this._pairs]}toObjectMap(){const t=Object.create(null);for(const[e,s]of this._pairs)t[e]=s;return t}toMap(){return new Map(this._pairs)}toIdSet(){return new Set(this.toIdArray())}toItemSet(){return new Set(this.toItemArray())}cache(){return new h([...this._pairs])}distinct(t){const e=new Set;for(const[s,i]of this._pairs)e.add(t(i,s));return e}filter(t){const e=this._pairs;return new h({*[Symbol.iterator](){for(const[s,i]of e)t(i,s)&&(yield[s,i])}})}forEach(t){for(const[e,s]of this._pairs)t(s,e)}map(t){const e=this._pairs;return new h({*[Symbol.iterator](){for(const[s,i]of e)yield[s,t(i,s)]}})}max(t){const e=this._pairs[Symbol.iterator]();let s=e.next();if(s.done)return null;let i=s.value[1],r=t(s.value[1],s.value[0]);for(;!(s=e.next()).done;){const[e,n]=s.value,o=t(n,e);o>r&&(r=o,i=n)}return i}min(t){const e=this._pairs[Symbol.iterator]();let s=e.next();if(s.done)return null;let i=s.value[1],r=t(s.value[1],s.value[0]);for(;!(s=e.next()).done;){const[e,n]=s.value,o=t(n,e);o<r&&(r=o,i=n)}return i}reduce(t,e){for(const[s,i]of this._pairs)e=t(e,i,s);return e}sort(t){return new h({[Symbol.iterator]:()=>[...this._pairs].sort(([e,s],[i,r])=>t(s,r,e,i))[Symbol.iterator]()})}}class l extends a{flush;length;get idProp(){return this._idProp}_options;_data;_idProp;_queue=null;constructor(t,e){super(),t&&!Array.isArray(t)&&(e=t,t=[]),this._options=e||{},this._data=new Map,this.length=0,this._idProp=this._options.fieldId||"id",t&&t.length&&this.add(t),this.setOptions(e)}setOptions(t){t&&void 0!==t.queue&&(!1===t.queue?this._queue&&(this._queue.destroy(),this._queue=null):(this._queue||(this._queue=o.extend(this,{replace:["add","update","remove"]})),t.queue&&"object"==typeof t.queue&&this._queue.setOptions(t.queue)))}add(t,e){const s=[];let i;if(Array.isArray(t)){if(t.map(t=>t[this._idProp]).some(t=>this._data.has(t)))throw new Error("A duplicate id was found in the parameter array.");for(let e=0,r=t.length;e<r;e++)i=this._addItem(t[e]),s.push(i)}else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");i=this._addItem(t),s.push(i)}return s.length&&this._trigger("add",{items:s},e),s}update(t,e){const s=[],i=[],r=[],n=[],o=this._idProp,a=t=>{const e=t[o];if(null!=e&&this._data.has(e)){const s=t,o=Object.assign({},this._data.get(e)),a=this._updateItem(s);i.push(a),n.push(s),r.push(o)}else{const e=this._addItem(t);s.push(e)}};if(Array.isArray(t))for(let e=0,s=t.length;e<s;e++)t[e]&&"object"==typeof t[e]?a(t[e]):console.warn("Ignoring input item, which is not an object at index "+e);else{if(!t||"object"!=typeof t)throw new Error("Unknown dataType");a(t)}if(s.length&&this._trigger("add",{items:s},e),i.length){const t={items:i,oldData:r,data:n};this._trigger("update",t,e)}return s.concat(i)}updateOnly(t,s){Array.isArray(t)||(t=[t]);const i=t.map(t=>{const e=this._data.get(t[this._idProp]);if(null==e)throw new Error("Updating non-existent items is not allowed.");return{oldData:e,update:t}}).map(({oldData:t,update:s})=>{const i=t[this._idProp],r=e.pureDeepObjectAssign(t,s);return this._data.set(i,r),{id:i,oldData:t,updatedData:r}});if(i.length){const t={items:i.map(t=>t.id),oldData:i.map(t=>t.oldData),data:i.map(t=>t.updatedData)};return this._trigger("update",t,s),t.items}return[]}get(t,e){let s,i,r;n(t)?(s=t,r=e):Array.isArray(t)?(i=t,r=e):r=t;const o=r&&"Object"===r.returnType?"Object":"Array",a=r&&r.filter,h=[];let l,u,d;if(null!=s)l=this._data.get(s),l&&a&&!a(l)&&(l=void 0);else if(null!=i)for(let t=0,e=i.length;t<e;t++)l=this._data.get(i[t]),null==l||a&&!a(l)||h.push(l);else{u=[...this._data.keys()];for(let t=0,e=u.length;t<e;t++)d=u[t],l=this._data.get(d),null==l||a&&!a(l)||h.push(l)}if(r&&r.order&&null==s&&this._sort(h,r.order),r&&r.fields){const t=r.fields;if(null!=s&&null!=l)l=this._filterFields(l,t);else for(let e=0,s=h.length;e<s;e++)h[e]=this._filterFields(h[e],t)}if("Object"==o){const t={};for(let e=0,s=h.length;e<s;e++){const s=h[e];t[s[this._idProp]]=s}return t}return null!=s?l??null:h}getIds(t){const e=this._data,s=t&&t.filter,i=t&&t.order,r=[...e.keys()],n=[];if(s)if(i){const t=[];for(let e=0,i=r.length;e<i;e++){const i=r[e],n=this._data.get(i);null!=n&&s(n)&&t.push(n)}this._sort(t,i);for(let e=0,s=t.length;e<s;e++)n.push(t[e][this._idProp])}else for(let t=0,e=r.length;t<e;t++){const e=r[t],i=this._data.get(e);null!=i&&s(i)&&n.push(i[this._idProp])}else if(i){const t=[];for(let s=0,i=r.length;s<i;s++){const i=r[s];t.push(e.get(i))}this._sort(t,i);for(let e=0,s=t.length;e<s;e++)n.push(t[e][this._idProp])}else for(let t=0,s=r.length;t<s;t++){const s=r[t],i=e.get(s);null!=i&&n.push(i[this._idProp])}return n}getDataSet(){return this}forEach(t,e){const s=e&&e.filter,i=[...this._data.keys()];if(e&&e.order){const s=this.get(e);for(let e=0,i=s.length;e<i;e++){const i=s[e];t(i,i[this._idProp])}}else for(let e=0,r=i.length;e<r;e++){const r=i[e],n=this._data.get(r);null==n||s&&!s(n)||t(n,r)}}map(t,e){const s=e&&e.filter,i=[],r=[...this._data.keys()];for(let e=0,n=r.length;e<n;e++){const n=r[e],o=this._data.get(n);null==o||s&&!s(o)||i.push(t(o,n))}return e&&e.order&&this._sort(i,e.order),i}_filterFields(t,e){return t?(Array.isArray(e)?e:Object.keys(e)).reduce((e,s)=>(e[s]=t[s],e),{}):t}_sort(t,e){if("string"==typeof e){const s=e;t.sort((t,e)=>{const i=t[s],r=e[s];return i>r?1:i<r?-1:0})}else{if("function"!=typeof e)throw new TypeError("Order must be a function or a string");t.sort(e)}}remove(t,e){const s=[],i=[],r=Array.isArray(t)?t:[t];for(let t=0,e=r.length;t<e;t++){const e=this._remove(r[t]);if(e){const t=e[this._idProp];null!=t&&(s.push(t),i.push(e))}}return s.length&&this._trigger("remove",{items:s,oldData:i},e),s}_remove(t){let e;if(n(t)?e=t:t&&"object"==typeof t&&(e=t[this._idProp]),null!=e&&this._data.has(e)){const t=this._data.get(e)||null;return this._data.delete(e),--this.length,t}return null}clear(t){const e=[...this._data.keys()],s=[];for(let t=0,i=e.length;t<i;t++)s.push(this._data.get(e[t]));return this._data.clear(),this.length=0,this._trigger("remove",{items:e,oldData:s},t),e}max(t){let e=null,s=null;for(const i of this._data.values()){const r=i[t];"number"==typeof r&&(null==s||r>s)&&(e=i,s=r)}return e||null}min(t){let e=null,s=null;for(const i of this._data.values()){const r=i[t];"number"==typeof r&&(null==s||r<s)&&(e=i,s=r)}return e||null}distinct(t){const e=this._data,s=[...e.keys()],i=[];let r=0;for(let n=0,o=s.length;n<o;n++){const o=s[n],a=e.get(o)[t];let h=!1;for(let t=0;t<r;t++)if(i[t]==a){h=!0;break}h||void 0===a||(i[r]=a,r++)}return i}_addItem(t){const e=function(t,e){return null==t[e]&&(t[e]=s.v4()),t}(t,this._idProp),i=e[this._idProp];if(this._data.has(i))throw new Error("Cannot add item: item with id "+i+" already exists");return this._data.set(i,e),++this.length,i}_updateItem(t){const e=t[this._idProp];if(null==e)throw new Error("Cannot update item: item has no id (item: "+JSON.stringify(t)+")");const s=this._data.get(e);if(!s)throw new Error("Cannot update item: no item with id "+e+" found");return this._data.set(e,{...s,...t}),e}stream(t){if(t){const e=this._data;return new h({*[Symbol.iterator](){for(const s of t){const t=e.get(s);null!=t&&(yield[s,t])}}})}return new h({[Symbol.iterator]:this._data.entries.bind(this._data)})}get testLeakData(){return this._data}get testLeakIdProp(){return this._idProp}get testLeakOptions(){return this._options}get testLeakQueue(){return this._queue}set testLeakQueue(t){this._queue=t}}class u extends a{length=0;get idProp(){return this.getDataSet().idProp}_listener;_data;_ids=new Set;_options;constructor(t,e){super(),this._options=e||{},this._listener=this._onEvent.bind(this),this.setData(t)}setData(t){if(this._data){this._data.off&&this._data.off("*",this._listener);const t=this._data.getIds({filter:this._options.filter}),e=this._data.get(t);this._ids.clear(),this.length=0,this._trigger("remove",{items:t,oldData:e})}if(null!=t){this._data=t;const e=this._data.getIds({filter:this._options.filter});for(let t=0,s=e.length;t<s;t++){const s=e[t];this._ids.add(s)}this.length=e.length,this._trigger("add",{items:e})}else this._data=new l;this._data.on&&this._data.on("*",this._listener)}refresh(){const t=this._data.getIds({filter:this._options.filter}),e=[...this._ids],s={},i=[],r=[],n=[];for(let e=0,r=t.length;e<r;e++){const r=t[e];s[r]=!0,this._ids.has(r)||(i.push(r),this._ids.add(r))}for(let t=0,i=e.length;t<i;t++){const i=e[t],o=this._data.get(i);null==o?console.error("If you see this, report it please."):s[i]||(r.push(i),n.push(o),this._ids.delete(i))}this.length+=i.length-r.length,i.length&&this._trigger("add",{items:i}),r.length&&this._trigger("remove",{items:r,oldData:n})}get(t,e){if(null==this._data)return null;let s,i=null;n(t)||Array.isArray(t)?(i=t,s=e):s=t;const r=Object.assign({},this._options,s),o=this._options.filter,a=s&&s.filter;return o&&a&&(r.filter=t=>o(t)&&a(t)),null==i?this._data.get(r):this._data.get(i,r)}getIds(t){if(this._data.length){const e=this._options.filter,s=null!=t?t.filter:null;let i;return i=s?e?t=>e(t)&&s(t):s:e,this._data.getIds({filter:i,order:t&&t.order})}return[]}forEach(t,e){if(this._data){const s=this._options.filter,i=e&&e.filter;let r;r=i?s?function(t){return s(t)&&i(t)}:i:s,this._data.forEach(t,{filter:r,order:e&&e.order})}}map(t,e){if(this._data){const s=this._options.filter,i=e&&e.filter;let r;return r=i?s?t=>s(t)&&i(t):i:s,this._data.map(t,{filter:r,order:e&&e.order})}return[]}getDataSet(){return this._data.getDataSet()}stream(t){return this._data.stream(t||{[Symbol.iterator]:this._ids.keys.bind(this._ids)})}dispose(){this._data?.off&&this._data.off("*",this._listener);const t="This data view has already been disposed of.",e={get:()=>{throw new Error(t)},set:()=>{throw new Error(t)},configurable:!1};for(const t of Reflect.ownKeys(u.prototype))Object.defineProperty(this,t,e)}_onEvent(t,e,s){if(!e||!e.items||!this._data)return;const i=e.items,r=[],n=[],o=[],a=[],h=[],l=[];switch(t){case"add":for(let t=0,e=i.length;t<e;t++){const e=i[t];this.get(e)&&(this._ids.add(e),r.push(e))}break;case"update":for(let t=0,s=i.length;t<s;t++){const s=i[t];this.get(s)?this._ids.has(s)?(n.push(s),h.push(e.data[t]),a.push(e.oldData[t])):(this._ids.add(s),r.push(s)):this._ids.has(s)&&(this._ids.delete(s),o.push(s),l.push(e.oldData[t]))}break;case"remove":for(let t=0,s=i.length;t<s;t++){const s=i[t];this._ids.has(s)&&(this._ids.delete(s),o.push(s),l.push(e.oldData[t]))}}this.length+=r.length-o.length,r.length&&this._trigger("add",{items:r},s),n.length&&this._trigger("update",{items:n,oldData:a,data:h},s),o.length&&this._trigger("remove",{items:o,oldData:l},s)}}function d(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.add&&"function"==typeof e.clear&&"function"==typeof e.distinct&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.max&&"function"==typeof e.min&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.remove&&"function"==typeof e.setOptions&&"function"==typeof e.stream&&"function"==typeof e.update&&"function"==typeof e.updateOnly}console.warn("You're running a development build."),Object.defineProperty(t,"DELETE",{enumerable:!0,get:function(){return e.DELETE}}),t.DataSet=l,t.DataStream=h,t.DataView=u,t.Queue=o,t.createNewDataPipeFrom=function(t){return new r(t)},t.isDataSetLike=d,t.isDataViewLike=function(t,e){return"object"==typeof e&&null!==e&&t===e.idProp&&"function"==typeof e.forEach&&"function"==typeof e.get&&"function"==typeof e.getDataSet&&"function"==typeof e.getIds&&"number"==typeof e.length&&"function"==typeof e.map&&"function"==typeof e.off&&"function"==typeof e.on&&"function"==typeof e.stream&&d(t,e.getDataSet())}});
//# sourceMappingURL=vis-data.min.cjs.map
