<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title>Tests</title>
    <link rel="stylesheet" href="../../node_modules/qunitjs/qunit/qunit.css">

    <script src="../../node_modules/jquery/dist/jquery.min.js"></script>
    <script src="../../node_modules/lodash-compat/index.js"></script>
    <script src="../../node_modules/qunitjs/qunit/qunit.js"></script>
    <!--[if !IE]> --><script src="../../node_modules/blanket/dist/qunit/blanket.js"></script><!-- <![endif]-->
    <script src="assets/utils.js"></script>

    <script src="../../node_modules/hammer-simulator/index.js"></script>
    <script>
        Simulator.setType('touch');
        Simulator.events.touch.fakeSupport();
    </script>

    <script src="../../dist/hammer.js" data-cover></script>
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>

<script src="test_utils.js"></script>
<script src="test_enable.js"></script>
<script src="test_hammer.js"></script>
<script src="test_events.js"></script>
<script src="test_nested_gesture_recognizers.js"></script>
<script src="test_simultaneous_recognition.js"></script>
<script src="test_propagation_bubble.js"></script>
<script src="test_gestures.js"></script>
<script src="test_multiple_taps.js"></script>
<script src="test_require_failure.js"></script>

<script src="test_jquery_plugin.js"></script>
<script src="gestures/test_pan.js"></script>
<script src="gestures/test_pinch.js"></script>
<script src="gestures/test_swipe.js"></script>

</body>
</html>
