"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/clear/route";
exports.ids = ["app/api/admin/clear/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "sqlite3":
/*!**************************!*\
  !*** external "sqlite3" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fclear%2Froute&page=%2Fapi%2Fadmin%2Fclear%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclear%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fclear%2Froute&page=%2Fapi%2Fadmin%2Fclear%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclear%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_clear_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/admin/clear/route.ts */ \"(rsc)/./app/api/admin/clear/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/clear/route\",\n        pathname: \"/api/admin/clear\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/clear/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/dev/poy/poy-app/app/api/admin/clear/route.ts\",\n    nextConfigOutput,\n    userland: _Users_adityabalasubramanian_Desktop_dev_poy_poy_app_app_api_admin_clear_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/clear/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fclear%2Froute&page=%2Fapi%2Fadmin%2Fclear%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclear%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/clear/route.ts":
/*!**************************************!*\
  !*** ./app/api/admin/clear/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./lib/database.ts\");\n\n\nasync function POST(request) {\n    try {\n        const result = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.clearDatabase)();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: result.message\n        });\n    } catch (error) {\n        console.error(\"Clear database error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to clear database\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2FkbWluL2NsZWFyL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1RDtBQUNUO0FBRXZDLGVBQWVFLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNQyxTQUFTLE1BQU1ILDREQUFhQTtRQUVsQyxPQUFPRCxrRkFBWUEsQ0FBQ0ssSUFBSSxDQUFDO1lBQ3ZCQyxTQUFTO1lBQ1RDLFNBQVNILE9BQU9HLE9BQU87UUFDekI7SUFFRixFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT1Isa0ZBQVlBLENBQUNLLElBQUksQ0FDdEI7WUFBRUMsU0FBUztZQUFPQyxTQUFTO1FBQTJCLEdBQ3REO1lBQUVHLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG95LWFwcC8uL2FwcC9hcGkvYWRtaW4vY2xlYXIvcm91dGUudHM/OWU0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgeyBjbGVhckRhdGFiYXNlIH0gZnJvbSAnQC9saWIvZGF0YWJhc2UnXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xlYXJEYXRhYmFzZSgpXG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6IHJlc3VsdC5tZXNzYWdlXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0NsZWFyIGRhdGFiYXNlIGVycm9yOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgc3VjY2VzczogZmFsc2UsIG1lc3NhZ2U6ICdGYWlsZWQgdG8gY2xlYXIgZGF0YWJhc2UnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjbGVhckRhdGFiYXNlIiwiUE9TVCIsInJlcXVlc3QiLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJzdGF0dXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/clear/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/database.ts":
/*!*************************!*\
  !*** ./lib/database.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRole: () => (/* binding */ assignRole),\n/* harmony export */   assignRolesToNonAdmins: () => (/* binding */ assignRolesToNonAdmins),\n/* harmony export */   assignStickyRolesToNonAdmins: () => (/* binding */ assignStickyRolesToNonAdmins),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   clearNonAdminRoles: () => (/* binding */ clearNonAdminRoles),\n/* harmony export */   closeDatabase: () => (/* binding */ closeDatabase),\n/* harmony export */   createNote: () => (/* binding */ createNote),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllNotes: () => (/* binding */ getAllNotes),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getAllUsersWithRoles: () => (/* binding */ getAllUsersWithRoles),\n/* harmony export */   getDatabase: () => (/* binding */ getDatabase),\n/* harmony export */   getNonAdminUserCount: () => (/* binding */ getNonAdminUserCount),\n/* harmony export */   getRoleStats: () => (/* binding */ getRoleStats),\n/* harmony export */   getUserByEmail: () => (/* binding */ getUserByEmail),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserPasswords: () => (/* binding */ getUserPasswords),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserWithRole: () => (/* binding */ getUserWithRole),\n/* harmony export */   initDatabase: () => (/* binding */ initDatabase),\n/* harmony export */   userExists: () => (/* binding */ userExists)\n/* harmony export */ });\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqlite3 */ \"sqlite3\");\n/* harmony import */ var sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"poy.db\");\nlet db;\n// Generate random password\nfunction generateRandomPassword(length = 8) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\nfunction migrateUsersTable(database) {\n    database.serialize(()=>{\n        // Create new table with correct constraints\n        database.run(`\n      CREATE TABLE users_new (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        name TEXT NOT NULL UNIQUE,\n        email TEXT UNIQUE,\n        phone TEXT,\n        about TEXT,\n        connection TEXT,\n        selfie TEXT,\n        password TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Copy data from old table, handling potential duplicates\n        database.run(`\n      INSERT OR IGNORE INTO users_new (id, name, email, phone, about, selfie, password, created_at)\n      SELECT id, name, email, phone, about, selfie, password, created_at FROM users\n    `);\n        // Drop old table\n        database.run(\"DROP TABLE users\");\n        // Rename new table\n        database.run(\"ALTER TABLE users_new RENAME TO users\");\n        console.log(\"✅ Users table migration completed\");\n    });\n}\nfunction addConnectionColumn(database) {\n    // Add connection column if it doesn't exist\n    database.run(`ALTER TABLE users ADD COLUMN connection TEXT`, (err)=>{\n        if (err && !err.message.includes(\"duplicate column name\")) {\n            console.error(\"Error adding connection column:\", err);\n        } else if (!err) {\n            console.log(\"✅ Connection column added to users table\");\n        }\n    });\n}\nfunction initDatabase() {\n    return new Promise((resolve, reject)=>{\n        if (db) {\n            resolve(db);\n            return;\n        }\n        db = new (sqlite3__WEBPACK_IMPORTED_MODULE_0___default().Database)(dbPath, (err)=>{\n            if (err) {\n                reject(err);\n                return;\n            }\n            // Create tables\n            db.serialize(()=>{\n                // Check if we need to migrate the users table\n                db.get(\"SELECT sql FROM sqlite_master WHERE type='table' AND name='users'\", (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking table schema:\", err);\n                        return;\n                    }\n                    // If table exists but doesn't have UNIQUE constraint on name, migrate it\n                    if (row && row.sql && !row.sql.includes(\"name TEXT NOT NULL UNIQUE\")) {\n                        console.log(\"Migrating users table to add UNIQUE constraint on name...\");\n                        migrateUsersTable(db);\n                    } else {\n                        // Create users table with proper constraints\n                        db.run(`\n              CREATE TABLE IF NOT EXISTS users (\n                id INTEGER PRIMARY KEY AUTOINCREMENT,\n                name TEXT NOT NULL UNIQUE,\n                email TEXT UNIQUE,\n                phone TEXT,\n                about TEXT,\n                connection TEXT,\n                selfie TEXT,\n                password TEXT NOT NULL,\n                created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n              )\n            `);\n                        // Add connection column to existing tables if needed\n                        addConnectionColumn(db);\n                    }\n                });\n                // Create roles table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS roles (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            role TEXT NOT NULL CHECK (role IN ('detective', 'liar', 'normal', 'admin')),\n            assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create notes table\n                db.run(`\n          CREATE TABLE IF NOT EXISTS notes (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL,\n            content TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create user_passwords table to store plain passwords for admin viewing\n                db.run(`\n          CREATE TABLE IF NOT EXISTS user_passwords (\n            id INTEGER PRIMARY KEY AUTOINCREMENT,\n            user_id INTEGER NOT NULL UNIQUE,\n            plain_password TEXT NOT NULL,\n            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n            FOREIGN KEY (user_id) REFERENCES users (id)\n          )\n        `);\n                // Create admin user if it doesn't exist and ensure admin role\n                db.get(\"SELECT id FROM users WHERE name = ?\", [\n                    \"admin\"\n                ], (err, row)=>{\n                    if (err) {\n                        console.error(\"Error checking admin user:\", err);\n                        return;\n                    }\n                    if (!row) {\n                        // Create admin user\n                        const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(\"zxcvbnm\", 10);\n                        db.run(\"INSERT INTO users (name, password, connection) VALUES (?, ?, ?)\", [\n                            \"admin\",\n                            hashedPassword,\n                            \"\"\n                        ], function(err) {\n                            if (err) {\n                                console.error(\"Error creating admin user:\", err);\n                                return;\n                            }\n                            // Assign admin role\n                            db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                this.lastID,\n                                \"admin\"\n                            ]);\n                            console.log(\"Admin user created with password: zxcvbnm\");\n                        });\n                    } else {\n                        // Admin user exists, ensure they have admin role\n                        db.get(\"SELECT id FROM roles WHERE user_id = ? AND role = ?\", [\n                            row.id,\n                            \"admin\"\n                        ], (err, roleRow)=>{\n                            if (err) {\n                                console.error(\"Error checking admin role:\", err);\n                                return;\n                            }\n                            if (!roleRow) {\n                                // Remove any existing role and assign admin role\n                                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                                    row.id\n                                ], (err)=>{\n                                    if (err) {\n                                        console.error(\"Error removing existing admin roles:\", err);\n                                        return;\n                                    }\n                                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                                        row.id,\n                                        \"admin\"\n                                    ], (err)=>{\n                                        if (err) {\n                                            console.error(\"Error assigning admin role:\", err);\n                                        } else {\n                                            console.log(\"Admin role assigned to existing admin user\");\n                                        }\n                                    });\n                                });\n                            }\n                        });\n                    }\n                });\n                resolve(db);\n            });\n        });\n    });\n}\nfunction getDatabase() {\n    if (db) {\n        return Promise.resolve(db);\n    }\n    return initDatabase();\n}\nfunction closeDatabase() {\n    if (db) {\n        db.close();\n    }\n}\n// User functions\nfunction createUser(name, email, phone, about, connection, selfie) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            const plainPassword = generateRandomPassword(8);\n            const hashedPassword = bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hashSync(plainPassword, 10);\n            db.run(\"INSERT INTO users (name, email, phone, about, connection, selfie, password) VALUES (?, ?, ?, ?, ?, ?, ?)\", [\n                name,\n                email,\n                phone,\n                about,\n                connection,\n                selfie,\n                hashedPassword\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const userId = this.lastID;\n                // Store plain password for admin viewing\n                db.run(\"INSERT INTO user_passwords (user_id, plain_password) VALUES (?, ?)\", [\n                    userId,\n                    plainPassword\n                ], (err)=>{\n                    if (err) {\n                        console.error(\"Error storing plain password:\", err);\n                    // Don't reject here, user creation was successful\n                    }\n                    // Assign normal role to new user\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        \"normal\"\n                    ], (err)=>{\n                        if (err) {\n                            console.error(\"Error assigning normal role to new user:\", err);\n                        // Don't reject here, user creation was successful\n                        }\n                        resolve({\n                            lastID: userId,\n                            password: plainPassword\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByName(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE name = ?\", [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserByEmail(email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT * FROM users WHERE email = ?\", [\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction userExists(name, email) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT id FROM users WHERE name = ? OR email = ?\", [\n                name,\n                email\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(!!row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsers() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(\"SELECT * FROM users ORDER BY name\", (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserWithRole(name) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        WHERE u.name = ?\n      `, [\n                name\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row);\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllUsersWithRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT u.*, r.role \n        FROM users u \n        LEFT JOIN roles r ON u.id = r.user_id \n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Role functions\nfunction assignRole(userId, role) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                // Remove existing role\n                db.run(\"DELETE FROM roles WHERE user_id = ?\", [\n                    userId\n                ], (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    // Assign new role\n                    db.run(\"INSERT INTO roles (user_id, role) VALUES (?, ?)\", [\n                        userId,\n                        role\n                    ], (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        resolve();\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserRole(userId) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(\"SELECT role FROM roles WHERE user_id = ?\", [\n                userId\n            ], (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row ? row.role : null);\n            });\n        }).catch(reject);\n    });\n}\nfunction clearNonAdminRoles() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve();\n            });\n        }).catch(reject);\n    });\n}\nfunction getRoleStats() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT role, COUNT(*) as count \n        FROM roles \n        GROUP BY role\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const result = {\n                    detective: 0,\n                    liar: 0,\n                    normal: 0,\n                    admin: 0\n                };\n                rows.forEach((row)=>{\n                    result[row.role] = row.count;\n                });\n                resolve(result);\n            });\n        }).catch(reject);\n    });\n}\nfunction getNonAdminUserCount() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.get(`\n        SELECT COUNT(*) as count FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, (err, row)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(row.count);\n            });\n        }).catch(reject);\n    });\n}\nfunction assignRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users without admin role\n            db.all(`\n        SELECT u.id, u.name FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 3) {\n                    resolve({\n                        message: `Need at least 3 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Clear existing non-admin roles\n                    await clearNonAdminRoles();\n                    // Shuffle users randomly\n                    const shuffled = [\n                        ...rows\n                    ].sort(()=>Math.random() - 0.5);\n                    // Assign exactly 1 detective, 1 liar, and everyone else as normal\n                    await assignRole(shuffled[0].id, \"detective\");\n                    await assignRole(shuffled[1].id, \"liar\");\n                    // Assign normal role to everyone else\n                    for(let i = 2; i < shuffled.length; i++){\n                        await assignRole(shuffled[i].id, \"normal\");\n                    }\n                    resolve({\n                        message: `Roles assigned successfully: 1 detective, 1 liar, ${shuffled.length - 2} normal users`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\nfunction assignStickyRolesToNonAdmins() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            // Get all users with their current roles\n            db.all(`\n        SELECT u.id, u.name, r.role FROM users u\n        LEFT JOIN roles r ON u.id = r.user_id\n        WHERE r.role IS NULL OR r.role != 'admin'\n        ORDER BY u.id\n      `, async (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                if (rows.length < 5) {\n                    resolve({\n                        message: `Need at least 5 non-admin users to assign roles (found ${rows.length})`\n                    });\n                    return;\n                }\n                try {\n                    // Count existing special roles\n                    const existingDetectives = rows.filter((u)=>u.role === \"detective\");\n                    const existingLiars = rows.filter((u)=>u.role === \"liar\");\n                    let detectivesNeeded = 0;\n                    let liarsNeeded = 0;\n                    // Determine how many detectives and liars we need based on user count\n                    if (rows.length >= 9) {\n                        // 9+ users: 2 detectives, 2 liars\n                        detectivesNeeded = 2 - existingDetectives.length;\n                        liarsNeeded = 2 - existingLiars.length;\n                    } else {\n                        // 5-8 users: 1 detective, 1 liar\n                        detectivesNeeded = 1 - existingDetectives.length;\n                        liarsNeeded = 1 - existingLiars.length;\n                    }\n                    let newAssignments = 0;\n                    // First, assign normal role to any unassigned users\n                    const unassignedUsers = rows.filter((u)=>!u.role);\n                    for (const user of unassignedUsers){\n                        await assignRole(user.id, \"normal\");\n                        newAssignments++;\n                    }\n                    // If we need more detectives or liars, promote from normal users\n                    if (detectivesNeeded > 0 || liarsNeeded > 0) {\n                        // Get users who are currently normal (including newly assigned ones)\n                        const availableNormalUsers = rows.filter((u)=>u.role === \"normal\" || !u.role);\n                        const shuffledNormal = [\n                            ...availableNormalUsers\n                        ].sort(()=>Math.random() - 0.5);\n                        let promotionIndex = 0;\n                        // Assign needed detectives\n                        for(let i = 0; i < detectivesNeeded && promotionIndex < shuffledNormal.length; i++){\n                            await assignRole(shuffledNormal[promotionIndex].id, \"detective\");\n                            promotionIndex++;\n                            newAssignments++;\n                        }\n                        // Assign needed liars\n                        for(let i = 0; i < liarsNeeded && promotionIndex < shuffledNormal.length; i++){\n                            await assignRole(shuffledNormal[promotionIndex].id, \"liar\");\n                            promotionIndex++;\n                            newAssignments++;\n                        }\n                    }\n                    const finalDetectives = existingDetectives.length + Math.max(0, detectivesNeeded);\n                    const finalLiars = existingLiars.length + Math.max(0, liarsNeeded);\n                    const finalNormal = rows.length - finalDetectives - finalLiars;\n                    resolve({\n                        message: `Roles assigned: ${finalDetectives} detective(s), ${finalLiars} liar(s), ${finalNormal} normal users (${newAssignments} new assignments)`\n                    });\n                } catch (error) {\n                    reject(error);\n                }\n            });\n        }).catch(reject);\n    });\n}\n// Notes functions\nfunction createNote(userId, content) {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.run(\"INSERT INTO notes (user_id, content) VALUES (?, ?)\", [\n                userId,\n                content\n            ], function(err) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve({\n                    lastID: this.lastID\n                });\n            });\n        }).catch(reject);\n    });\n}\nfunction getAllNotes() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT n.*, u.name as user_name\n        FROM notes n\n        JOIN users u ON n.user_id = u.id\n        ORDER BY n.created_at DESC\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\nfunction getUserPasswords() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.all(`\n        SELECT up.*, u.name as user_name\n        FROM user_passwords up\n        JOIN users u ON up.user_id = u.id\n        ORDER BY u.name\n      `, (err, rows)=>{\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(rows);\n            });\n        }).catch(reject);\n    });\n}\n// Clear all data except admin\nfunction clearDatabase() {\n    return new Promise((resolve, reject)=>{\n        getDatabase().then((db)=>{\n            db.serialize(()=>{\n                db.run(\"DELETE FROM notes\", (err)=>{\n                    if (err) {\n                        reject(err);\n                        return;\n                    }\n                    db.run('DELETE FROM user_passwords WHERE user_id != (SELECT id FROM users WHERE name = \"admin\")', (err)=>{\n                        if (err) {\n                            reject(err);\n                            return;\n                        }\n                        db.run('DELETE FROM roles WHERE role != \"admin\"', (err)=>{\n                            if (err) {\n                                reject(err);\n                                return;\n                            }\n                            db.run('DELETE FROM users WHERE name != \"admin\"', (err)=>{\n                                if (err) {\n                                    reject(err);\n                                    return;\n                                }\n                                resolve({\n                                    message: \"Database cleared successfully\"\n                                });\n                            });\n                        });\n                    });\n                });\n            });\n        }).catch(reject);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/bcryptjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fclear%2Froute&page=%2Fapi%2Fadmin%2Fclear%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fclear%2Froute.ts&appDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fadityabalasubramanian%2FDesktop%2Fdev%2Fpoy%2Fpoy-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();